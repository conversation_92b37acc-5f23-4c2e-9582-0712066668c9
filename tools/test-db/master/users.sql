/* Create role app_user */
CREATE ROLE IF NOT EXISTS 'app_user';

/* Grant app_user role access to database */
GRANT ALL PRIVILEGES ON predicthq.* TO 'app_user';

/* Create user siteminder */
CREATE USER IF NOT EXISTS 'siteminder'@'%' IDENTIFIED WITH mysql_native_password BY 'siteminder';
CREATE USER IF NOT EXISTS 'siteminder'@'localhost' IDENTIFIED WITH mysql_native_password BY 'siteminder';

/* Assign role to siteminder user */
GRANT 'app_user' TO 'siteminder'@'%';
GRANT 'app_user' TO 'siteminder'@'localhost';

SET DEFAULT ROLE 'app_user' TO 'siteminder'@'%';
SET DEFAULT ROLE 'app_user' TO 'siteminder'@'localhost';

/* Create role replication_slave */
CREATE ROLE 'replication_slave';

/* Grant roles access for replication */
GRANT REPLICATION CLIENT ON *.* TO 'app_user';
GRANT REPLICATION SLAVE ON *.* TO 'replication_slave';

/* Create user slave_user for replication */
CREATE USER 'slave_user'@'%' IDENTIFIED WITH mysql_native_password BY 'password';
CREATE USER 'slave_user'@'localhost' IDENTIFIED WITH mysql_native_password BY 'password';

/* Assign role to replication_slave user */
GRANT 'replication_slave' TO 'slave_user'@'%';
GRANT 'replication_slave' TO 'slave_user'@'localhost';

SET DEFAULT ROLE 'replication_slave' TO 'slave_user'@'%';
SET DEFAULT ROLE 'replication_slave' TO 'slave_user'@'localhost';

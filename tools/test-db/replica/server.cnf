[mysqld]
port = 3306
max_allowed_packet = 10M
sort_buffer_size = 512K
read_buffer_size = 512K
read_rnd_buffer_size = 256K
net_buffer_length = 16K
thread_stack = 256K
lower_case_table_names = 1

thread_cache_size = 8

collation_server=utf8mb4_unicode_ci
character_set_server=utf8mb4

# Replication (SLAVE)
log-bin=mysql-bin
relay-log=mysql-relay-bin.log
server_id = 2
binlog_format=ROW
binlog_do_db = predicthq

# InnoDB
innodb_file_per_table = 1

innodb_buffer_pool_size = 128M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 0
innodb_lock_wait_timeout = 50

innodb_redo_log_capacity = 50M

innodb_flush_method = O_DSYNC
innodb_write_io_threads = 4
innodb_read_io_threads = 4

# Performance Schema
performance_schema = OFF

# Information Schema
information_schema_stats_expiry = 0

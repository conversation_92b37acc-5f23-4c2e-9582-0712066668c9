/* Create role app_user */
CREATE ROLE IF NOT EXISTS 'app_user';

/* Grant app_user role access to database */
GRANT ALL PRIVILEGES ON predicthq.* TO 'app_user';

/* Create user siteminder */
CREATE USER IF NOT EXISTS 'siteminder'@'%' IDENTIFIED WITH mysql_native_password BY 'siteminder';
CREATE USER IF NOT EXISTS 'siteminder'@'localhost' IDENTIFIED WITH mysql_native_password BY 'siteminder';

/* Assign role to siteminder user */
GRANT 'app_user' TO 'siteminder'@'%';
GRANT 'app_user' TO 'siteminder'@'localhost';

SET DEFAULT ROLE 'app_user' TO 'siteminder'@'%';
SET DEFAULT ROLE 'app_user' TO 'siteminder'@'localhost';

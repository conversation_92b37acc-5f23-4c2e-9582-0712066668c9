buildscript {
    ext {
        smBuildPluginVersion = '4.0.0'
        kotlinVersion = '1.9.20'
        springBootVersion = '3.3.5'
        springFrameworkVersion = '6.1.15'
        springSecurityVersion = '6.4.1'
        smLibraryVersion = '11.0.2.RELEASE'
        awsSdkVersion = '2.26.29'
        kotlinTestVersion = '3.4.2'
        mockitoKotlinVersion = '3.2.0'
    }
    repositories {
        maven {
            url "https://plugins.gradle.org/m2/"
        }
        def repos = ["release"] // add snapshot if needed
        repos.each { repo ->
            maven {
                credentials {
                    username artifactory_user
                    password artifactory_password
                }
                name "siteminder-${repo}"
                url "${artifactory_contextUrl}/siteminder-${repo}"
            }
        }
    }
    dependencies {
        classpath("com.siteminder:sm-build-plugin:${smBuildPluginVersion}")
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlinVersion}")
        classpath("org.jetbrains.kotlin:kotlin-allopen:${kotlinVersion}")
        classpath("org.jetbrains.kotlin:kotlin-noarg:${kotlinVersion}")
    }
}

group = 'com.siteminder'

apply plugin: 'sm-build-plugin' // https://github.com/siteminder-au/sm-gradle-plugins/tree/master/sm-build-plugin

subprojects {

    dependencyManagement {
        dependencies {
            dependency "org.mariadb.jdbc:mariadb-java-client:2.7.9"
        }
    }

}

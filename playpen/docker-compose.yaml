services:
  mysql:
    build:
      context: ${PROJECT_ROOT}/tools/test-db/master
      dockerfile: ${PROJECT_ROOT}/tools/test-db/master/Dockerfile
    command: --default-authentication-plugin=mysql_native_password
    tmpfs:
      - /var/lib/mysql:rw
    ports:
      - 3306:3306
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost"]
    profiles:
      - local-dev
      - domain-model
      - dump-schema
      - hotel-api
      - hotel-feature-job

  mysql-replica:
    build:
      context: ${PROJECT_ROOT}/tools/test-db/replica
      dockerfile: ${PROJECT_ROOT}/tools/test-db/replica/Dockerfile
    command: --default-authentication-plugin=mysql_native_password
    tmpfs:
      - /var/lib/mysql:rw
    ports:
      - 3307:3306
    depends_on:
      mysql:
        condition: service_healthy # wait for mysql to be healthy before starting replication
    healthcheck:
      test: ["<PERSON><PERSON>", "mysqladmin" ,"ping", "-h", "localhost"]
    profiles:
      - local-dev
      - domain-model
      - hotel-api
      - hotel-feature-job

  # Runs the alters on mysql container startup and then exits
  db-migrations:
    build:
      context: ${PROJECT_ROOT}/libs/db-migrations
      dockerfile: ${PROJECT_ROOT}/libs/db-migrations/Dockerfile.db-migrations
    environment:
      - db_host=mysql
      - db_port=3306
      - db_user=root
      - db_password=
      - db_name=predicthq
    depends_on:
      mysql:
        condition: service_healthy # wait for mysql to be healthy before running migrations
    profiles:
      - local-dev
      - domain-model
      - dump-schema
      - hotel-api
      - hotel-feature-job

  localstack:
    image: 278521702583.dkr.ecr.us-west-2.amazonaws.com/arch/localstack:v5.1.0
    ports:
      - 4566:4566
    volumes:
      - $PROJECT_ROOT/playpen/localstack:/etc/localstack/init/ready.d
      - "/var/run/docker.sock:/var/run/docker.sock"
    environment:
      - LOCALSTACK_HOST=localstack
      - AWS_DEFAULT_REGION=us-west-2
      - SERVICES=s3,sqs
    healthcheck:
      test: ["CMD-SHELL", "awslocal s3 ls"]
    profiles:
      - local-dev
      - analysis-processor
      - hotel-api
      - hotel-feature-processor
      - hotel-feature-job
      - hotel-seed-job
      - events-api
      - hotel-api-client
      - predicthq-client

  mockserver:
    image: 278521702583.dkr.ecr.us-west-2.amazonaws.com/arch/mockserver:v1.0.0
    ports:
      - 1080:1080
    environment:
      MOCKSERVER_MAX_EXPECTATIONS: 2000
      MOCKSERVER_PROPERTY_FILE: /config/mockserver.properties
    volumes:
      - type: bind
        source: ./mockserver
        target: /config
    profiles:
      - local-dev
      - analysis-processor
      - hotel-api
      - hotel-feature-processor
      - hotel-seed-job
      - events-api
      - hotel-api-client
      - predicthq-client

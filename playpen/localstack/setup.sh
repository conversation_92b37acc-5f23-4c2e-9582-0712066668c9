#!/bin/bash

set -e

## Secrets Bucket
echo "create S3 bucket sm.secrets.dev"
awslocal s3 mb s3://sm.secrets.dev

## Tokens
 awslocal s3 sync "$(dirname "${BASH_SOURCE[0]}")/tokens" s3://sm.secrets.dev/tokens

## ADX Bucket
echo "create S3 bucket s3://sm.predicthq.adx"
awslocal s3 mb s3://sm.predicthq.adx

## Parquet
# awslocal s3 sync "$(dirname "${BASH_SOURCE[0]}")/adx" s3://sm.predicthq.adx

## SQS
awslocal sqs create-queue --queue-name "analysis-queue"
awslocal sqs create-queue --queue-name "hotel-feature-queue"

{"name": "predicthq", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "predicthq", "version": "1.0.0", "license": "ISC", "dependencies": {"@siteminder/cm-underling": "^3.5.0"}, "engines": {"node": ">=18.16.0", "npm": ">=9.5.1"}}, "node_modules/@siteminder/cm-underling": {"version": "3.5.0", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/@siteminder/cm-underling/-/@siteminder/cm-underling-3.5.0.tgz", "integrity": "sha512-mgKa/iMklSpAUC8psXzmV7FRtAMY6VNJwDwZjsrvWRQwPjULYLyfZQR7MSwb6OMIX9Lu1iadrEqPS0qnIoUv4g==", "license": "ISC", "dependencies": {"@siteminder/overlord": "^17.5.6", "colors": "^1.4.0", "yargs": "^17.7.2"}, "bin": {"cm-underling": "cli.js"}, "engines": {"node": ">=18.16.0", "npm": ">=9.5.1"}}, "node_modules/@siteminder/overlord": {"version": "17.5.6", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/@siteminder/overlord/-/@siteminder/overlord-17.5.6.tgz", "integrity": "sha512-sumb+n4wVFmNRHminhjthkXZyfBxU2M1VbTSkPk72Rr+6YK0pYKTmtKigWRsDH8QqLYJd9jt5R/3XEqIsBpxmw==", "license": "UNLICENSED", "dependencies": {"js-yaml": "^4.1.0", "yargs": "^17.7.2"}, "bin": {"pen": "playpen.js", "playpen": "playpen.js"}, "engines": {"node": ">=18.14.0", "npm": ">=9.5.0"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "license": "Python-2.0"}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT"}, "node_modules/colors": {"version": "1.4.0", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/colors/-/colors-1.4.0.tgz", "integrity": "sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA==", "engines": {"node": ">=0.1.90"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "license": "MIT"}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/********************************************************************==", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-******************************************************************+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://smbuild.jfrog.io/artifactory/api/npm/siteminder-npm/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "license": "ISC", "engines": {"node": ">=12"}}}}
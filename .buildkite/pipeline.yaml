steps:

- label: ':git: Create Git diff for calculating incremental build'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/create-git-diff.sh'

- wait

- label: ':docker: Create ECR Repos'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/create-ecr-repos.sh'

- label: ':closed_lock_with_key: Define audit steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/audit.sh'

- label: ':docker: Check for outdated images'
  command: './node_modules/\@siteminder/overlord/shared/ci-scripts/check-for-outdated-images.sh'
  soft_fail:
    - exit_status: 3

- label: ':mysql: Test DB Migration Scripts (MySQL 8.0)'
  command: ./libs/db-migrations/ci-scripts/test.sh
  artifact_paths:
    - ./libs/db-migrations/logs/*.log

- label: ':package: Define package steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/package.sh'

- label: ':junit: Define test steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/test.sh'

- wait

- label: ':docker: Define migration steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/migrations.sh'

- label: ':docker: Define build steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/build.sh'

- label: ':s3: Define upload s3 steps (lambdas & frontends)'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/s3-upload.sh'

- label: ':jfrog: define artifactory publish steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/publish.sh'

- wait

- label: ':helm: Define dev deployment steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/deployments.sh -r dev --app-infras-branch master --config-branch master --deploy-version branch-dynamic'
  if: build.tag == null

- label: ':helm: Define preprod deployment steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/deployments.sh -r preprod  --config-branch master --app-infras-branch master --exclude "(components/analysis-processor)|(components/hotel-feature-job)|(components/hotel-feature-processor)|(components/hotel-seed-job)"'
  if: build.tag != null

- label: ':helm: Define pciprod deployment steps'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/deployments.sh -r pciprod -e prod --exclude "(components/hotel-seed-job)"'
  if: build.tag != null

- wait

- label: ':git: Save Git sha for incremental build later'
  command: './node_modules/\@siteminder/overlord/shared/buildkite/save-git-sha.sh'

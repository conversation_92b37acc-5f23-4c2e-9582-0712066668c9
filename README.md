# predicthq

## Components
- [analysis-processor](components/analysis-processor) - update hotel's feature importance based on PredictHQ analysis
- [cdc-events-pipeline](components/cdc-events-pipeline) - Databricks ELT pipeline for PredictHQ data
  - Extracts from S3
  - Transforms to `predicthq` schema in Databricks.
  - Publishes to `events`  Kinesis stream.
- [events-api](components/events-api) - provides a RESTful API for PredictHQ events
- [hotel-api](components/hotel-api) - provides a RESTful API for hotels
- [hotel-feature-job](components/hotel-feature-job) - kicks off hotel feature importance flow
- [hotel-feature-processor](components/hotel-feature-processor) - creates PredictHQ analysis based on hotel's pace data
- [hotel-seed-job](components/hotel-seed-job) - seed hotels from Nexus to PredictHQ. Filtered by country. Only to be run once per new area rollout.

Destroy in the reverse order, however ensure aurora has delete protection turned off if destroying in pciprod.

## Tools

### Schema (MySQL)
When making any changes to database schema e.g. create [database migrations](libs/db-migrations/src/database/migrations), you must also execute:

* `bin/dump-schema.sh` to update [schema/schema.sql](schema/schema.sql).
    * This generates the schema for the database so that a reference is always available.
* `bin/dump-schema-dbz-relay.sh` to update [schema/dbz-relay/\*.json](schema/dbz-relay/)
    * This generates the master copy of the files for the Debezium relay preserving any edits made e.g. descriptions etc.
* See [here](bin/README.md) for more details on the schema generation process.

### Playpen MySQL Setup

The MySQL instance is configured at startup to have migrations executed on it, and to have a replication slave configured.

The replication slave is configured to wait for the master to be healthy before starting replication.

### NodeJS api client

To regenerate client, execute:

```bash
./bin/generate-node-api-client.sh
```

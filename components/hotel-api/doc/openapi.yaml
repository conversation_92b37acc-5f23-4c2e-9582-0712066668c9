openapi: 3.0.1
info:
  title: predicthq/hotel-api
  description: See README in https://github.com/siteminder-au/predicthq for details
  version: v1
servers:
- url: http://localhost:8080
  description: Generated server url
security:
- bearerAuth: []
paths:
  /api/hotels:
    get:
      tags:
      - hotel-controller
      summary: Requires hotel:read permission
      operationId: getHotels
      parameters:
      - name: lat
        in: query
        required: false
        schema:
          type: number
      - name: lng
        in: query
        required: false
        schema:
          type: number
      - name: localRank
        in: query
        required: false
        schema:
          type: integer
          format: int64
      - name: X-SM-TRACE-TOKEN
        in: header
        description: Trace Token
        required: true
        schema:
          type: string
          example: 123e4567-e89b-12d3-a456-426655440000
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/HotelResponse"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    post:
      tags:
      - hotel-controller
      summary: Requires hotel:write permission
      operationId: createHotel
      parameters:
      - name: X-SM-TRACE-TOKEN
        in: header
        description: Trace Token
        required: true
        schema:
          type: string
          example: 123e4567-e89b-12d3-a456-426655440000
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateHotelRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HotelResponse"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /api/hotels/{spid}:
    get:
      tags:
      - hotel-controller
      summary: Requires hotel:read permission
      operationId: getHotel
      parameters:
      - name: spid
        in: path
        required: true
        schema:
          type: string
      - name: X-SM-TRACE-TOKEN
        in: header
        description: Trace Token
        required: true
        schema:
          type: string
          example: 123e4567-e89b-12d3-a456-426655440000
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HotelResponse"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    put:
      tags:
      - hotel-controller
      summary: Requires hotel:write permission
      operationId: updateHotel
      parameters:
      - name: spid
        in: path
        required: true
        schema:
          type: string
      - name: X-SM-TRACE-TOKEN
        in: header
        description: Trace Token
        required: true
        schema:
          type: string
          example: 123e4567-e89b-12d3-a456-426655440000
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateHotelRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HotelResponse"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    delete:
      tags:
      - hotel-controller
      summary: Requires hotel:write permission
      operationId: deleteHotel
      parameters:
      - name: spid
        in: path
        required: true
        schema:
          type: string
      - name: X-SM-TRACE-TOKEN
        in: header
        description: Trace Token
        required: true
        schema:
          type: string
          example: 123e4567-e89b-12d3-a456-426655440000
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /api/hotels/{spid}/feature-importance:
    put:
      tags:
      - hotel-controller
      summary: Requires hotel:write-internal permission
      operationId: updateFeatureImportance
      parameters:
      - name: spid
        in: path
        required: true
        schema:
          type: string
      - name: X-SM-TRACE-TOKEN
        in: header
        description: Trace Token
        required: true
        schema:
          type: string
          example: 123e4567-e89b-12d3-a456-426655440000
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/FeatureImportanceRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/HotelResponse"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /api/hotels/{spid}/mark-feature-importance-attempt:
    put:
      tags:
      - hotel-controller
      summary: Requires hotel:write-internal permission
      operationId: markFeatureImportanceAttempt
      parameters:
      - name: spid
        in: path
        required: true
        schema:
          type: string
      - name: X-SM-TRACE-TOKEN
        in: header
        description: Trace Token
        required: true
        schema:
          type: string
          example: 123e4567-e89b-12d3-a456-426655440000
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
components:
  schemas:
    CreateHotelRequest:
      required:
      - countryCode
      - latitude
      - longitude
      - spid
      type: object
      properties:
        spid:
          type: string
        latitude:
          maximum: 90
          minimum: -90
          type: number
        longitude:
          maximum: 180
          minimum: -180
          type: number
        countryCode:
          type: string
    Error:
      required:
      - code
      - message
      type: object
      properties:
        code:
          type: string
          enum:
          - ERROR
          - UNAUTHORIZED
          - FORBIDDEN
          - INVALID
          - NOT_FOUND
          - NOT_NULL
          - NOT_BLANK
          - NOT_EMPTY
          - EMAIL
          - MIN
          - MAX
          - UNIQUE
        message:
          type: string
          description: Brief description of error
        meta:
          type: object
          additionalProperties:
            type: object
            description: Error context
            nullable: true
          description: Error context
          nullable: true
    ErrorResponse:
      required:
      - errors
      type: object
      properties:
        errors:
          type: array
          items:
            $ref: "#/components/schemas/Error"
    FeatureImportanceRequest:
      required:
      - category
      - important
      - pvalue
      type: object
      properties:
        category:
          type: string
          enum:
          - observances
          - public-holidays
          - concerts
          - sports
          - community
          - expos
          - performing-arts
          - conferences
          - school-holidays
          - festivals
          - severe-weather
          - academic
        pvalue:
          type: number
        important:
          type: boolean
    FeatureImportanceResponse:
      required:
      - category
      - important
      - pvalue
      type: object
      properties:
        category:
          type: string
          enum:
          - observances
          - public-holidays
          - concerts
          - sports
          - community
          - expos
          - performing-arts
          - conferences
          - school-holidays
          - festivals
          - severe-weather
          - academic
        pvalue:
          type: number
          format: double
        important:
          type: boolean
    HotelResponse:
      required:
      - countryCode
      - createdAt
      - featureImportance
      - featureImportanceUpdatedAt
      - latitude
      - longitude
      - spid
      - suggestedRadius
      - updatedAt
      type: object
      properties:
        spid:
          type: string
        latitude:
          maximum: 90
          minimum: -90
          type: number
        longitude:
          maximum: 180
          minimum: -180
          type: number
        countryCode:
          type: string
        suggestedRadius:
          type: number
          description: Radius in kilometres
        featureImportance:
          type: array
          items:
            $ref: "#/components/schemas/FeatureImportanceResponse"
        featureImportanceUpdatedAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    UpdateHotelRequest:
      required:
      - countryCode
      - latitude
      - longitude
      type: object
      properties:
        latitude:
          maximum: 90
          minimum: -90
          type: number
        longitude:
          maximum: 180
          minimum: -180
          type: number
        countryCode:
          type: string
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

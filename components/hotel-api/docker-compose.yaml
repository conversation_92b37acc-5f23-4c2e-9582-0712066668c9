version: '3'
services:
  hotel-api:
    image: 278521702583.dkr.ecr.us-west-2.amazonaws.com/arch/amazoncorretto17-springboot-builder:v1.2.0
    environment:
      - SPRING_PROFILES_ACTIVE=buildkite
      - MODULE=$MODULE
    working_dir: /app/components/$MODULE
    volumes:
      - ${HOME}/.gradle/gradle.properties:/root/.gradle/gradle.properties
      - $HOME/.npmrc:/root/.npmrc
      - $PROJECT_ROOT:/app

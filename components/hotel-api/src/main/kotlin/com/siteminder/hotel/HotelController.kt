package com.siteminder.hotel

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.siteminder.api.BadRequestException
import com.siteminder.api.NotFoundException
import com.siteminder.api.error.ErrorCode
import com.siteminder.predicthq.PredictHqClient
import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.predicthq.domain.HotelRepository
import io.swagger.v3.oas.annotations.Operation
import jakarta.validation.Valid
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.math.BigDecimal
import java.time.ZonedDateTime

@Validated
@RestController
class HotelController(private val hotelRepository: HotelRepository, private val predictHqClient: PredictHqClient, private val objectMapper: ObjectMapper) {

    @Transactional
    @PostMapping("/api/hotels")
    @PreAuthorize("hasPermission(null, 'hotel', 'write')")
    @Operation(summary = "Requires hotel:write permission")
    fun createHotel(@Valid @RequestBody request: CreateHotelRequest): HotelResponse {
        hotelRepository.findBySpid(request.spid!!).ifPresent {
            throw BadRequestException(ErrorCode.UNIQUE.name, "Hotel already exists for SPID", mapOf("field" to "spid"))
        }

        val suggestedRadius = predictHqClient.getSuggestedRadius(request.latitude!!, request.longitude!!)

        val now = ZonedDateTime.now()
        val hotel = Hotel(
            id = null,
            spid = request.spid,
            location = GeoUtils.newGeoPoint(request.latitude.toDouble(), request.longitude.toDouble()),
            countryCode = request.countryCode!!,
            suggestedRadius = suggestedRadius,
            createdAt = now,
            updatedAt = now
        )

        return toHotelResponse(hotelRepository.save(hotel))
    }

    @Transactional
    @PutMapping("/api/hotels/{spid}")
    @PreAuthorize("hasPermission(null, 'hotel', 'write')")
    @Operation(summary = "Requires hotel:write permission")
    fun updateHotel(@PathVariable spid: String, @Valid @RequestBody request: UpdateHotelRequest): HotelResponse {
        val existing = hotelRepository.findBySpid(spid).orElseThrow {
            NotFoundException("Hotel not found", mapOf("spid" to spid, "entity" to "Hotel"))
        }

        val suggestedRadius = predictHqClient.getSuggestedRadius(request.latitude!!, request.longitude!!)

        existing.updatedAt = ZonedDateTime.now()
        existing.suggestedRadius = suggestedRadius
        existing.location = GeoUtils.newGeoPoint(request.latitude.toDouble(), request.longitude.toDouble())
        existing.countryCode = request.countryCode!!

        return toHotelResponse(hotelRepository.save(existing))
    }

    @Transactional
    @DeleteMapping("/api/hotels/{spid}")
    @PreAuthorize("hasPermission(null, 'hotel', 'write')")
    @Operation(summary = "Requires hotel:write permission")
    fun deleteHotel(@PathVariable spid: String) {
        val existing = hotelRepository.findBySpid(spid).orElseThrow {
            NotFoundException("Hotel not found", mapOf("spid" to spid, "entity" to "Hotel"))
        }

        hotelRepository.delete(existing)
    }

    @Transactional(readOnly = true)
    @GetMapping("/api/hotels/{spid}")
    @PreAuthorize("hasPermission(null, 'hotel', 'read')")
    @Operation(summary = "Requires hotel:read permission")
    fun getHotel(@PathVariable spid: String): HotelResponse {
        val hotel = hotelRepository.findBySpid(spid).orElseThrow { NotFoundException("Hotel not found", mapOf("spid" to spid, "entity" to "Hotel")) }

        return toHotelResponse(hotel)
    }

    @Transactional(readOnly = true)
    @GetMapping("/api/hotels")
    @PreAuthorize("hasPermission(null, 'hotel', 'read')")
    @Operation(summary = "Requires hotel:read permission")
    fun getHotels(@RequestParam(required = false) lat: BigDecimal?, @RequestParam(required = false) lng: BigDecimal?, @RequestParam(required = false) localRank: Long?): List<HotelResponse> {
        val hotels = if (lat != null && lng != null) {
            val localRank = localRank ?: 0
            val radiusMultiplier = when {
                localRank >= 99 -> 5
                localRank >= 95 -> 3
                else -> 1
            }
            hotelRepository.findAllByLocationWithinRadius(lat, lng, radiusMultiplier.toBigDecimal())
        } else {
            hotelRepository.findAll()
        }

        return hotels.map { toHotelResponse(it) }
    }

    @PutMapping("/api/hotels/{spid}/feature-importance")
    @PreAuthorize("hasPermission(null, 'hotel', 'write-internal')")
    @Operation(summary = "Requires hotel:write-internal permission")
    fun updateFeatureImportance(@PathVariable spid: String, @Valid @RequestBody request: List<FeatureImportanceRequest>): HotelResponse {
        val existing = hotelRepository.findBySpid(spid).orElseThrow {
            NotFoundException("Hotel not found", mapOf("spid" to spid, "entity" to "Hotel"))
        }

        existing.featureImportance = request.let { objectMapper.writeValueAsString(it) }
        existing.featureImportanceUpdatedAt = ZonedDateTime.now()

        return toHotelResponse(hotelRepository.save(existing))
    }

    @PutMapping("/api/hotels/{spid}/mark-feature-importance-attempt")
    @PreAuthorize("hasPermission(null, 'hotel', 'write-internal')")
    @Operation(summary = "Requires hotel:write-internal permission")
    fun markFeatureImportanceAttempt(@PathVariable spid: String) {
        val existing = hotelRepository.findBySpid(spid).orElseThrow {
            NotFoundException("Hotel not found", mapOf("spid" to spid, "entity" to "Hotel"))
        }

        existing.featureImportanceLastAttemptAt = ZonedDateTime.now()

        hotelRepository.save(existing)
    }

    private fun toHotelResponse(hotel: Hotel): HotelResponse {
        return HotelResponse(
            spid = hotel.spid,
            latitude = GeoUtils.getLatitude(hotel.location),
            longitude = GeoUtils.getLongitude(hotel.location),
            countryCode = hotel.countryCode,
            suggestedRadius = hotel.suggestedRadius,
            featureImportance = hotel.featureImportance?.let { objectMapper.readValue<List<FeatureImportanceResponse>>(it) } ?: emptyList(),
            featureImportanceUpdatedAt = hotel.featureImportanceUpdatedAt,
            createdAt = hotel.createdAt,
            updatedAt = hotel.updatedAt
        )
    }
}

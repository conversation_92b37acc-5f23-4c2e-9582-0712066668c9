package com.siteminder.hotel

import com.fasterxml.jackson.annotation.JsonValue
import java.math.BigDecimal
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

data class CreateHotelRequest(

    @field:NotBlank
    val spid: String?,

    @field:NotNull
    @field:Min(-90)
    @field:Max(90)
    val latitude: BigDecimal?,

    @field:NotNull
    @field:Min(-180)
    @field:Max(180)
    val longitude: BigDecimal?,

    @field:NotBlank
    val countryCode: String?
)

data class UpdateHotelRequest(

    @field:NotNull
    @field:Min(-90)
    @field:Max(90)
    val latitude: BigDecimal?,

    @field:NotNull
    @field:Min(-180)
    @field:Max(180)
    val longitude: BigDecimal?,

    @field:NotBlank
    val countryCode: String?
)

data class FeatureImportanceRequest(

    @field:NotNull
    val category: Category?,

    @field:NotNull
    val pvalue: BigDecimal?,

    @field:NotNull
    val important: Boolean?
) {
    enum class Category(@get:JsonValue val value: String) {
        observances("observances"),
        public_holidays("public-holidays"),
        concerts("concerts"),
        sports("sports"),
        community("community"),
        expos("expos"),
        performing_arts("performing-arts"),
        conferences("conferences"),
        school_holidays("school-holidays"),
        festivals("festivals"),
        severe_weather("severe-weather"),
        academic("academic")
    }
}

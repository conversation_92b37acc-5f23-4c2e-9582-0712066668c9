package com.siteminder.hotel

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonValue
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.ZonedDateTime

@JsonInclude(JsonInclude.Include.NON_NULL)
data class HotelResponse(
    @field:Schema(required = true) val spid: String,
    @field:Schema(required = true, minimum = "-90", maximum = "90") val latitude: BigDecimal,
    @field:Schema(required = true, minimum = "-180", maximum = "180") val longitude: BigDecimal,
    @field:Schema(required = true) val countryCode: String,
    @field:Schema(required = true, description = "Radius in kilometres") val suggestedRadius: BigDecimal,
    @field:Schema(required = true) val featureImportance: List<FeatureImportanceResponse>?,
    @field:Schema(required = true) val featureImportanceUpdatedAt: ZonedDateTime?,
    @field:Schema(required = true) val createdAt: ZonedDateTime,
    @field:Schema(required = true) val updatedAt: ZonedDateTime
)

data class FeatureImportanceResponse(
    @field:Schema(required = true) val category: Category,
    @field:Schema(required = true) val pvalue: Double,
    @field:Schema(required = true) val important: Boolean
) {
    enum class Category(@get:JsonValue val value: String) {
        observances("observances"),
        public_holidays("public-holidays"),
        concerts("concerts"),
        sports("sports"),
        community("community"),
        expos("expos"),
        performing_arts("performing-arts"),
        conferences("conferences"),
        school_holidays("school-holidays"),
        festivals("festivals"),
        severe_weather("severe-weather"),
        academic("academic")
    }
}

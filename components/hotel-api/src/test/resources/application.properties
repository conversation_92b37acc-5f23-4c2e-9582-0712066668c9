spring.profiles.include=localstack

system.jwt-secret=secret
secrets.bucketName=sm.secrets.dev

spring.datasource.url=***************************************,localhost:3307/predicthq?useSSL=false
spring.datasource.username=siteminder
spring.datasource.password=siteminder
test.replication-slave.jdbc-url=*****************************************

predicthq.api.base-url=http://mockserver:1080/predicthq-api
predicthq.api.access-token=predicthq-api-access-token
predicthq.api.concurrency=100
predicthq.api.timeout-in-seconds=60

package com.siteminder.hotel

import com.siteminder.BaseSpringBootTest
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.smDelete
import io.kotlintest.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@AutoConfigureMockMvc
class HotelControllerDeleteTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    private val spid = "test-spid"

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:write")
            .sign()
    }

    @Test
    fun `DELETE hotels should return 401 if missing token`() {

        mockMvc.perform(MockMvcRequestBuilders.delete("/api/hotels/$spid"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `DELETE hotels should return 401 if invalid token`() {

        mockMvc.perform(smDelete("/api/hotels/$spid", "xxx"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `DELETE hotels should return 403 if jwt with incorrect permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()

        mockMvc.perform(smDelete("/api/hotels/$spid", invalidJwt))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `DELETE hotels should return 404 if SPID does not exist`() {

        build(Hotel::class) {
            spid = "existing-spid"
        }

        mockMvc.perform(smDelete("/api/hotels/$spid", jwt))
            .andExpect(status().isNotFound)
            .andExpect(content().json("""{"errors":[{"code":"NOT_FOUND","message":"Hotel not found","meta":{"spid":"$spid","entity":"Hotel"}}]}"""))
    }

    @Test
    fun `DELETE hotels should return 200 if hotel is found`() {

        val requestSpid = spid

        val hotel = build(Hotel::class) {
            spid = requestSpid
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 5.0.toBigDecimal()
        }

        clearSession()

        mockMvc.perform(smDelete("/api/hotels/$spid", jwt))
            .andExpect(status().isOk)

        reload(hotel) shouldBe null
    }
}

package com.siteminder.hotel

import com.siteminder.BaseSpringBootTest
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.smGet
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.format.DateTimeFormatter

@AutoConfigureMockMvc
class HotelControllerGetHotelsTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:read")
            .sign()
    }

    @Test
    fun `GET hotels should return 401 if missing token`() {

        mockMvc.perform(MockMvcRequestBuilders.get("/api/hotels"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `GET hotels should return 401 if invalid token`() {

        mockMvc.perform(smGet("/api/hotels", "xxx"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `GET hotels should return 403 if jwt with incorrect permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()
        
        mockMvc.perform(smGet("/api/hotels", invalidJwt))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `GET hotels should return 200 with empty list if no Hotels`() {

        mockMvc.perform(smGet("/api/hotels", jwt))
            .andExpect(status().isOk)
            .andExpect(content().json("""[]"""))
    }

    @Test
    fun `GET hotels should return 200 with list of Hotels if found`() {

        val hotel1 = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 5.0.toBigDecimal()
            featureImportance = """[
                  {
                    "category": "sports",
                    "important": false,
                    "pvalue": 0.6
                  },
                  {
                    "category": "concerts",
                    "important": true,
                    "pvalue": 0.3
                  }
                ]""".trimIndent()
        }

        val hotel2 = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-37.7362147, 145.0055442)
            suggestedRadius = 3.3.toBigDecimal()
            featureImportance = """[
                  {
                    "category": "conferences",
                    "important": true,
                    "pvalue": 0.2
                  }
                ]""".trimIndent()
        }

        clearSession()

        waitUntilReplication()

        mockMvc.perform(smGet("/api/hotels", jwt))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                [
                  {
                    "spid": "${hotel1.spid}",
                    "latitude": -33.8688,
                    "longitude": 151.2093,
                    "suggestedRadius": 5.0,
                    "featureImportance": [
                          {
                            "category": "sports",
                            "important": false,
                            "pvalue": 0.6
                          },
                          {
                            "category": "concerts",
                            "important": true,
                            "pvalue": 0.3
                          }
                        ],
                    "createdAt": "${hotel1.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                    "updatedAt": "${hotel1.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                  },
                  {
                    "spid": "${hotel2.spid}",
                    "latitude": -37.7362147,
                    "longitude": 145.0055442,
                    "suggestedRadius": 3.3,
                    "featureImportance": [
                          {
                            "category": "conferences",
                            "important": true,
                            "pvalue": 0.2
                          }
                        ],
                    "createdAt": "${hotel2.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                    "updatedAt": "${hotel2.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                  }
                ]
            """.trimIndent()
                )
            )
    }

    @Test
    fun `GET hotels should return 200 with list of Hotels that provided latitude and longitude is relevant to when localRank equates to a times 1 radius multiplier`() {
        val hotel1 = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 9.toBigDecimal()
            featureImportance = """[
                  {
                    "category": "conferences",
                    "important": true,
                    "pvalue": 0.2
                  }
                ]""".trimIndent()
        }

        build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 8.toBigDecimal()
        }

        clearSession()

        waitUntilReplication()

        mockMvc.perform(smGet("/api/hotels?lat=-33.9400&lng=151.1754&localRank=45", jwt))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                [
                  {
                    "spid": "${hotel1.spid}",
                    "latitude": -33.8688,
                    "longitude": 151.2093,
                    "suggestedRadius": 9,
                    "featureImportance": [
                          {
                            "category": "conferences",
                            "important": true,
                            "pvalue": 0.2
                          }
                        ],
                    "createdAt": "${hotel1.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                    "updatedAt": "${hotel1.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                  }
                ]
            """.trimIndent()
                )
            )
    }

    @Test
    fun `GET hotels should return 200 with list of Hotels that provided latitude and longitude is relevant to when localRank equates to a times 3 radius multiplier`() {
        val hotel1 = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 4.toBigDecimal()
            countryCode = "NZ"
            featureImportance = """[
                  {
                    "category": "conferences",
                    "important": true,
                    "pvalue": 0.2
                  }
                ]""".trimIndent()
        }

        val hotel2 = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 3.toBigDecimal()
            featureImportance = """[
                  {
                    "category": "sports",
                    "important": true,
                    "pvalue": 0.01
                  }
                ]""".trimIndent()
        }

        build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 2.toBigDecimal()
        }

        clearSession()

        waitUntilReplication()

        mockMvc.perform(smGet("/api/hotels?lat=-33.9400&lng=151.1754&localRank=96", jwt))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                [
                  {
                    "spid": "${hotel1.spid}",
                    "latitude": -33.8688,
                    "longitude": 151.2093,
                    "countryCode": "NZ",
                    "suggestedRadius": 4,
                    "featureImportance": [
                          {
                            "category": "conferences",
                            "important": true,
                            "pvalue": 0.2
                          }
                        ],
                    "createdAt": "${hotel1.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                    "updatedAt": "${hotel1.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                  },
                  {
                    "spid": "${hotel2.spid}",
                    "latitude": -33.8688,
                    "longitude": 151.2093,
                    "countryCode": "AU",
                    "suggestedRadius": 3,
                    "featureImportance": [
                          {
                            "category": "sports",
                            "important": true,
                            "pvalue": 0.01
                          }
                        ],
                    "createdAt": "${hotel2.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                    "updatedAt": "${hotel2.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                  }
                ]
            """.trimIndent()
                )
            )
    }

    @Test
    fun `GET hotels should return 200 with list of Hotels that provided latitude and longitude is relevant to when localRank equates to a times 5 radius multiplier`() {
        val hotel1 = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 4.toBigDecimal()
            countryCode = "NZ"
            featureImportance = """[
                  {
                    "category": "conferences",
                    "important": true,
                    "pvalue": 0.2
                  }
                ]""".trimIndent()
        }

        val hotel2 = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 3.toBigDecimal()
            featureImportance = """[
                  {
                    "category": "sports",
                    "important": true,
                    "pvalue": 0.01
                  }
                ]""".trimIndent()
        }

        val hotel3 = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 2.toBigDecimal()
        }

        build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 1.toBigDecimal()
        }

        clearSession()

        waitUntilReplication()

        mockMvc.perform(smGet("/api/hotels?lat=-33.9400&lng=151.1754&localRank=99", jwt))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                [
                  {
                    "spid": "${hotel1.spid}",
                    "latitude": -33.8688,
                    "longitude": 151.2093,
                    "countryCode": "NZ",
                    "suggestedRadius": 4,
                    "featureImportance": [
                          {
                            "category": "conferences",
                            "important": true,
                            "pvalue": 0.2
                          }
                        ],
                    "createdAt": "${hotel1.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                    "updatedAt": "${hotel1.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                  },
                  {
                    "spid": "${hotel2.spid}",
                    "latitude": -33.8688,
                    "longitude": 151.2093,
                    "countryCode": "AU",
                    "suggestedRadius": 3,
                    "featureImportance": [
                          {
                            "category": "sports",
                            "important": true,
                            "pvalue": 0.01
                          }
                        ],
                    "createdAt": "${hotel2.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                    "updatedAt": "${hotel2.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                  },
                  {
                    "spid": "${hotel3.spid}",
                    "latitude": -33.8688,
                    "longitude": 151.2093,
                    "countryCode": "AU",
                    "suggestedRadius": 2,
                    "createdAt": "${hotel3.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                    "updatedAt": "${hotel3.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                  }
                ]
            """.trimIndent()
                )
            )
    }

}

package com.siteminder.hotel

import com.siteminder.BaseSpringBootTest
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.smPut
import io.kotlintest.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@AutoConfigureMockMvc
class HotelControllerUpdateFeatureImportanceTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var mockMvc: MockMvc

    private val spid = "test-spid"

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:write-internal")
            .sign()
    }

    @Test
    fun `PUT hotels feature importance should return 401 if missing token`() {

        mockMvc.perform(MockMvcRequestBuilders.post("/api/hotels/$spid/feature-importance"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `PUT hotels feature importance should return 401 if invalid token`() {

        val request = """
            [
              {
                "category": "sports",
                "important": false,
                "pvalue": 0.607
              }
            ]
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid/feature-importance", "xxx", request))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `PUT hotels feature importance should return 403 if jwt with incorrect permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()
        
        val request = """
            [
              {
                "category": "sports",
                "important": false,
                "pvalue": 0.607
              }
            ]
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid/feature-importance", invalidJwt, request))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `PUT hotels feature importance should return 400 if missing required fields`() {

        val request = """
            [
              {
                "cat": "sports",
                "xxx": false
              }
            ]
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid/feature-importance", jwt, request))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                        {
                          "errors": [
                            {"code":"NOT_NULL","message":"must not be null","meta":{"field":"updateFeatureImportance.request[0].pvalue"}},
                            {"code":"NOT_NULL","message":"must not be null","meta":{"field":"updateFeatureImportance.request[0].important"}},
                            {"code":"NOT_NULL","message":"must not be null","meta":{"field":"updateFeatureImportance.request[0].category"}}
                          ]
                        }
                    """.trimIndent()
                )
            )
    }

    @Test
    fun `PUT hotels feature importance should return 404 if SPID does not exist`() {

        build(Hotel::class) {
            spid = "existing-spid"
        }

        val request = """
            [
              {
                "category": "sports",
                "important": false,
                "pvalue": 0.607
              }
            ]
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid/feature-importance", jwt, request))
            .andExpect(status().isNotFound)
            .andExpect(content().json("""{"errors":[{"code":"NOT_FOUND","message":"Hotel not found","meta":{"spid":"$spid","entity":"Hotel"}}]}"""))
    }

    @Test
    fun `PUT hotels feature importance should return 200 with Hotel if found`() {

        val latitude = -33.8688
        val longitude = 151.2093
        val requestSpid = spid

        val hotel = build(Hotel::class) {
            spid = requestSpid
            location = GeoUtils.newGeoPoint(latitude, longitude)
            suggestedRadius = 3.3.toBigDecimal()
        }

        clearSession()

        val request = """
            [
              {
                "category": "sports",
                "important": false,
                "pvalue": 0.6
              },
              {
                "category": "concerts",
                "important": true,
                "pvalue": 0.3
              }
            ]
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid/feature-importance", jwt, request))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                {
                  "spid": "$spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": "AU",
                  "suggestedRadius": 3.3,
                  "featureImportance": [
                          {
                            "category": "sports",
                            "important": false,
                            "pvalue": 0.6
                          },
                          {
                            "category": "concerts",
                            "important": true,
                            "pvalue": 0.3
                          }
                        ]
                }
            """.trimIndent()
                )
            )

        reload(hotel)?.apply {
            featureImportance shouldBe """[{"pvalue": 0.6, "category": "sports", "important": false}, {"pvalue": 0.3, "category": "concerts", "important": true}]"""
        }
    }
}

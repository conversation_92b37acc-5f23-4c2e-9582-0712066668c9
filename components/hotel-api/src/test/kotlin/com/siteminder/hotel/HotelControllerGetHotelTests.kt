package com.siteminder.hotel

import com.siteminder.BaseSpringBootTest
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.smGet
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.format.DateTimeFormatter

@AutoConfigureMockMvc
class HotelControllerGetHotelTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var mockMvc: MockMvc

    private val spid = "test-spid"

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:read")
            .sign()
    }

    @Test
    fun `GET hotel should return 401 if missing token for read endpoint`() {

        mockMvc.perform(MockMvcRequestBuilders.get("/api/hotels/$spid"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `GET hotel should return 401 if invalid token for read endpoint`() {

        mockMvc.perform(smGet("/api/hotels/$spid", "xxx"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `GET hotel should return 403 if JWT with incorrect permissions for read endpoint`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()

        mockMvc.perform(smGet("/api/hotels/$spid", invalidJwt))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `GET hotel should return 404 if Hotel not found`() {

        mockMvc.perform(smGet("/api/hotels/$spid", jwt))
            .andExpect(status().isNotFound)
            .andExpect(content().json("""{"errors":[{"code":"NOT_FOUND","message":"Hotel not found","meta":{"spid":"$spid","entity":"Hotel"}}]}"""))
    }

    @Test
    fun `GET hotel should return 200 with Hotel if found`() {

        val hotel = build(Hotel::class) {
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 5.0.toBigDecimal()
            countryCode = "AU"
            featureImportance = """[
                  {
                    "category": "conferences",
                    "important": true,
                    "pvalue": 0.2
                  }
                ]""".trimIndent()
        }

        clearSession()

        waitUntilReplication()

        mockMvc.perform(smGet("/api/hotels/${hotel.spid}", jwt))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                {
                  "spid": "${hotel.spid}",
                  "latitude": -33.8688,
                  "longitude": 151.2093,
                  "countryCode": "AU",
                  "suggestedRadius": 5.0,
                  "featureImportance": [
                          {
                            "category": "conferences",
                            "important": true,
                            "pvalue": 0.2
                          }
                        ],
                  "createdAt": "${hotel.createdAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}",
                  "updatedAt": "${hotel.updatedAt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)}"
                }
            """.trimIndent()
                )
            )
    }
}

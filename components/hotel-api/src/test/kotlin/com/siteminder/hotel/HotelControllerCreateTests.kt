package com.siteminder.hotel

import com.siteminder.BaseSpringBootTest
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.mockserver.MockServerExtension
import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.smPost
import io.kotlintest.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.math.BigDecimal

@AutoConfigureMockMvc
@ExtendWith(MockServerExtension::class)
class HotelControllerCreateTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var mockMvc: MockMvc

    private lateinit var mockServerClient: MockServerClient

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:write")
            .sign()
    }

    // Create endpoint

    @Test
    fun `POST hotels should return 401 if missing token`() {

        mockMvc.perform(MockMvcRequestBuilders.post("/api/hotels"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `POST hotels should return 401 if invalid token`() {

        val request = """
            {
              "spid": "test-spid",
              "latitude": 1.0,
              "longitude": 1.0,
              "countryCode": "AU"
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/hotels", "xxx", request))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `POST hotels should return 403 if jwt with incorrect permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()

        val request = """
            {
              "spid": "test-spid",
              "latitude": 1.0,
              "longitude": 1.0,
              "countryCode": "NZ"
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/hotels", invalidJwt, request))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `POST hotels should return 400 if missing required fields`() {

        val request = "{}"

        mockMvc.perform(smPost("/api/hotels", jwt, request))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                        {
                          "errors": [
                            {"code":"NOT_BLANK","message":"must not be blank","meta":{"field":"spid"}},
                            {"code":"NOT_NULL","message":"must not be null","meta":{"field":"latitude"}},
                            {"code":"NOT_NULL","message":"must not be null","meta":{"field":"longitude"}},
                            {"code":"NOT_BLANK","message":"must not be blank","meta":{"field":"countryCode"}}
                          ]
                        }
                    """.trimIndent()
                )
            )
    }

    @Test
    fun `POST hotels should return 400 if fields are out of range`() {

        val request = """
            {
              "spid": "test-spid",
              "latitude": 91.0,
              "longitude": -181.0,
              "countryCode": "AU"
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/hotels", jwt, request))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                        {
                          "errors": [
                            {"code":"MAX","message":"must be less than or equal to 90","meta":{"field":"latitude"}},
                            {"code":"MIN","message":"must be greater than or equal to -180","meta":{"field":"longitude"}}
                          ]
                        }
                    """.trimIndent()
                )
            )
    }

    @Test
    fun `POST hotels should return 400 if duplicate SPID`() {

        build(Hotel::class) {
            spid = "test-spid"
        }

        val request = """
            {
              "spid": "test-spid",
              "latitude": -37.1234,
              "longitude": 151.5678,
              "countryCode": "AU"
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/hotels", jwt, request))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                        {
                          "errors": [
                            {"code":"UNIQUE","message":"Hotel already exists for SPID","meta":{"field":"spid"}}
                          ]
                        }
                    """.trimIndent()
                )
            )
    }

    @Test
    fun `POST hotels should return 200 with Hotel if successful`() {

        val latitude = -37.1234
        val longitude = 151.5678
        val countryCode = "NZ"

        val getSuggestedRadiusRequest = apiRequest(path = "/predicthq-api/suggested-radius", traceToken = null, queryParams = mapOf("industry" to "accommodation", "radius_unit" to "km", "location.origin" to "$latitude,$longitude"))
        val getSuggestedRadiusResponsePayload = """
            {
              "radius": 3.3,
              "radius_unit": "km",
              "location": {
                "lat": "$latitude",
                "lon": "$longitude"
              }
            }
        """.trimIndent()
        mockServerClient.`when`(getSuggestedRadiusRequest).respond(
            apiResponse(body = getSuggestedRadiusResponsePayload)
        )

        val request = """
            {
              "spid": "test-spid",
              "latitude": $latitude,
              "longitude": $longitude,
              "countryCode": "$countryCode"
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/hotels", jwt, request))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                {
                  "spid": "test-spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": $countryCode,
                  "suggestedRadius": 3.3
                }
            """.trimIndent()
                )
            )

        clearSession()

        val hotel = load(Hotel::class) { it.spid == "test-spid" }

        hotel!!.spid shouldBe "test-spid"
        hotel.location shouldBe GeoUtils.newGeoPoint(latitude, longitude)
        hotel.suggestedRadius shouldBe BigDecimal("3.300")
    }
}

package com.siteminder.hotel

import com.siteminder.BaseSpringBootTest
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.mockserver.MockServerExtension
import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.smPut
import io.kotlintest.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.math.BigDecimal

@AutoConfigureMockMvc
@ExtendWith(MockServerExtension::class)
class HotelControllerUpdateTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var mockMvc: MockMvc

    private lateinit var mockServerClient: MockServerClient

    private val spid = "test-spid"

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:write")
            .sign()
    }

    @Test
    fun `PUT hotels should return 401 if missing token`() {

        mockMvc.perform(MockMvcRequestBuilders.post("/api/hotels/$spid"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `PUT hotels should return 401 if invalid token`() {

        val request = """
            {
              "latitude": 1.0,
              "longitude": 1.0,
              "countryCode": "NZ"
            }
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid", "xxx", request))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `PUT hotels should return 403 if jwt with incorrect permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()

        val request = """
            {
              "latitude": 1.0,
              "longitude": 1.0,
              "countryCode": "NZ"
            }
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid", invalidJwt, request))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `PUT hotels should return 400 if missing required fields`() {

        val request = "{}"

        mockMvc.perform(smPut("/api/hotels/$spid", jwt, request))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                        {
                          "errors": [
                            {"code":"NOT_NULL","message":"must not be null","meta":{"field":"latitude"}},
                            {"code":"NOT_NULL","message":"must not be null","meta":{"field":"longitude"}},
                            {"code":"NOT_BLANK","message":"must not be blank","meta":{"field":"countryCode"}}
                          ]
                        }
                    """.trimIndent()
                )
            )
    }

    @Test
    fun `PUT hotels should return 400 if fields are out of range`() {

        val request = """
            {
              "latitude": 91.0,
              "longitude": -181.0,
              "countryCode": "NZ"
            }
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid", jwt, request))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                        {
                          "errors": [
                            {"code":"MAX","message":"must be less than or equal to 90","meta":{"field":"latitude"}},
                            {"code":"MIN","message":"must be greater than or equal to -180","meta":{"field":"longitude"}}
                          ]
                        }
                    """.trimIndent()
                )
            )
    }

    @Test
    fun `PUT hotels should return 404 if SPID does not exist`() {

        build(Hotel::class) {
            spid = "existing-spid"
        }

        val request = """
            {
              "latitude": -37.1234,
              "longitude": 151.5678,
              "countryCode": "AU"
            }
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid", jwt, request))
            .andExpect(status().isNotFound)
            .andExpect(content().json("""{"errors":[{"code":"NOT_FOUND","message":"Hotel not found","meta":{"spid":"$spid","entity":"Hotel"}}]}"""))
    }

    @Test
    fun `PUT hotels should return 200 with Hotel if found`() {

        val latitude = -37.1234
        val longitude = 151.5678

        val getSuggestedRadiusRequest = apiRequest(path = "/predicthq-api/suggested-radius", traceToken = null, queryParams = mapOf("industry" to "accommodation", "radius_unit" to "km", "location.origin" to "$latitude,$longitude"))
        val getSuggestedRadiusResponsePayload = """
            {
              "radius": 3.3,
              "radius_unit": "km",
              "location": {
                "lat": "$latitude",
                "lon": "$longitude"
              }
            }
        """.trimIndent()
        mockServerClient.`when`(getSuggestedRadiusRequest).respond(
            apiResponse(body = getSuggestedRadiusResponsePayload)
        )

        val requestSpid = spid

        val hotel = build(Hotel::class) {
            spid = requestSpid
            location = GeoUtils.newGeoPoint(-33.8688, 151.2093)
            suggestedRadius = 5.0.toBigDecimal()
        }

        clearSession()

        val request = """
            {
              "spid": "$spid",
              "latitude": $latitude,
              "longitude": $longitude,
              "countryCode": "NZ"
            }
        """.trimIndent()

        mockMvc.perform(smPut("/api/hotels/$spid", jwt, request))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                {
                  "spid": "test-spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": "NZ",
                  "suggestedRadius": 3.3
                }
            """.trimIndent()
                )
            )

        reload(hotel)?.apply {
            spid shouldBe "$spid"
            location shouldBe GeoUtils.newGeoPoint(latitude, longitude)
            countryCode shouldBe "NZ"
            suggestedRadius shouldBe BigDecimal("3.300")
        }
    }
}

package com.siteminder.hotel

import com.siteminder.BaseSpringBootTest
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.smPut
import io.kotlintest.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.LocalDate

@AutoConfigureMockMvc
class HotelControllerMarkFeatureImportanceAttemptTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var mockMvc: MockMvc

    private val spid = "test-spid"

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:write-internal")
            .sign()
    }

    @Test
    fun `should return 401 if missing token`() {

        mockMvc.perform(MockMvcRequestBuilders.post("/api/hotels/$spid/mark-feature-importance-attempt"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `should return 401 if invalid token`() {

        mockMvc.perform(smPut("/api/hotels/$spid/mark-feature-importance-attempt", "xxx"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `should return 403 if jwt with incorrect permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()

        mockMvc.perform(smPut("/api/hotels/$spid/mark-feature-importance-attempt", invalidJwt))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `should return 404 if SPID does not exist`() {

        build(Hotel::class) {
            spid = "existing-spid"
        }

        mockMvc.perform(smPut("/api/hotels/$spid/mark-feature-importance-attempt", jwt))
            .andExpect(status().isNotFound)
            .andExpect(content().json("""{"errors":[{"code":"NOT_FOUND","message":"Hotel not found","meta":{"spid":"$spid","entity":"Hotel"}}]}"""))
    }

    @Test
    fun `should return 200 with Hotel if found`() {
        val requestSpid = spid

        val hotel = build(Hotel::class) {
            spid = requestSpid
            featureImportanceLastAttemptAt = null
        }

        clearSession()

        mockMvc.perform(smPut("/api/hotels/$spid/mark-feature-importance-attempt", jwt))
            .andExpect(status().isOk)

        reload(hotel)?.apply {
            featureImportanceLastAttemptAt!!.toLocalDate() shouldBe LocalDate.now()
        }
    }
}

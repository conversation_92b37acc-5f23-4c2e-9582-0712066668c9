package com.siteminder

import com.siteminder.metrics.ResetMetricsExtension
import com.siteminder.test.BaseJpaSpringBootTest
import com.siteminder.test.ModelDefinitions
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(ResetMetricsExtension::class)
abstract class BaseSpringBootTest : BaseJpaSpringBootTest() {

    companion object {

        @JvmStatic
        @BeforeAll
        fun initModelDefinitions() {
            ModelDefinitions.initialize()
        }
    }
}

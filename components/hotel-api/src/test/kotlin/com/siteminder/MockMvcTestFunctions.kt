package com.siteminder

import org.springframework.http.MediaType
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders

fun smGet(url: String, jwt: String): MockHttpServletRequestBuilder {
    return MockMvcRequestBuilders.get(url)
        .header("X-SM-TRACE-TOKEN", "trace-token-123")
        .header("Authorization", "Bearer $jwt")
}

fun smPost(url: String, jwt: String, body: String): MockHttpServletRequestBuilder {
    return MockMvcRequestBuilders.post(url)
        .header("X-SM-TRACE-TOKEN", "trace-token-123")
        .header("Authorization", "Bearer $jwt")
        .contentType(MediaType.APPLICATION_JSON)
        .content(body)
}

fun smPut(url: String, jwt: String, body: String? = null): MockHttpServletRequestBuilder {
    return MockMvcRequestBuilders.put(url)
        .header("X-SM-TRACE-TOKEN", "trace-token-123")
        .header("Authorization", "Bearer $jwt")
        .contentType(MediaType.APPLICATION_JSON)
        .apply {
            if(body != null) {
                content(body)
            }
        }
}

fun smDelete(url: String, jwt: String): MockHttpServletRequestBuilder {
    return MockMvcRequestBuilders.delete(url)
        .header("X-SM-TRACE-TOKEN", "trace-token-123")
        .header("Authorization", "Bearer $jwt")
}

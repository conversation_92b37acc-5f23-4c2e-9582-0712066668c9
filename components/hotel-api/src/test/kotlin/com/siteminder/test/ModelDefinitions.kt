package com.siteminder.test

import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.test.ModelFactory.define
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

object ModelDefinitions {

    fun initialize() {
        define(Hotel::class) { f ->
            spid = f.uuid()
            location = GeoUtils.newGeoPoint(-37.1234, 151.5678)
            suggestedRadius = 5.0.toBigDecimal()
            createdAt = f.datetime().withZoneSameInstant(ZoneId.of("UTC").normalized()).truncatedTo(ChronoUnit.SECONDS)
            updatedAt = f.datetime().withZoneSameInstant(ZoneId.of("UTC").normalized()).truncatedTo(ChronoUnit.SECONDS)
            countryCode = "AU"
        }
    }
}

package com.siteminder

import com.siteminder.api.TraceTokenContextUtils
import org.mockserver.matchers.MatchType
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse
import org.mockserver.model.JsonBody
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus

fun apiRequest(path: String, body: String? = null, httpMethod: HttpMethod = HttpMethod.GET, traceToken: String? = TraceTokenContextUtils.getTraceToken(), additionalHeaders: Map<String, String> = mapOf(), queryParams: Map<String, String> = mapOf()): HttpRequest =
    HttpRequest.request()
        .withMethod(httpMethod.name())
        .withPath(path)
        .apply {
            if (traceToken != null) {
                withHeader("X-SM-TRACE-TOKEN", traceToken)
            }

            additionalHeaders.forEach {
                withHeader(it.key, it.value)
            }

            queryParams.forEach {
                withQueryStringParameter(it.key, it.value)
            }

            if (body != null) {
                withContentType(org.mockserver.model.MediaType.APPLICATION_JSON)
                withBody(JsonBody(body, MatchType.STRICT))
            }
        }

fun <T> apiResponse(status: HttpStatus = HttpStatus.OK, body: T? = null): HttpResponse = HttpResponse.response()
    .withStatusCode(status.value())
    .withContentType(org.mockserver.model.MediaType.APPLICATION_JSON)
    .apply {
        if (body != null) {
            if (body is String)
                withBody(body)
            else
                withBody(JsonBody.json(body))
        }
    }


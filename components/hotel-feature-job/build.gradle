apply plugin: 'springboot-kotlin-configuration-plugin' // https://github.com/siteminder-au/sm-gradle-plugins/tree/master/cm-build-plugin
apply plugin: 'kotlin-jpa'

dependencies {
    implementation "com.siteminder:sm-spring-boot-job-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-sqs-starter:${smLibraryVersion}"

    implementation project(":libs:domain-model")

    testImplementation "com.siteminder:sm-aws-starter-test:${smLibraryVersion}"
    testImplementation "com.siteminder:sm-spring-boot-starter-test:${smLibraryVersion}"
}

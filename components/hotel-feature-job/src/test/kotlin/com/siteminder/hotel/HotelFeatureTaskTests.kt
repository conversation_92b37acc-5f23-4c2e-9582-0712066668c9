package com.siteminder.hotel

import com.siteminder.BaseSpringBootTest
import com.siteminder.aws.SqsTestUtils
import com.siteminder.context.TraceTokenContextUtils
import com.siteminder.predicthq.domain.Hotel
import io.kotlintest.matchers.string.shouldContain
import io.kotlintest.shouldBe
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import java.time.ZonedDateTime

@SpringBootTest
class HotelFeatureTaskTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var task: HotelFeatureTask

    @Autowired
    private lateinit var sqsAsyncClient: SqsAsyncClient

    private val hotelFeatureQueueName = "hotel-feature-queue"

    private lateinit var sqsTestUtils: SqsTestUtils

    @BeforeEach
    fun init() {
        sqsTestUtils = SqsTestUtils.newInstance(sqsAsyncClient, hotelFeatureQueueName)
        sqsTestUtils.emptyQueue()

        TraceTokenContextUtils.setTraceToken("test-trace-token")
    }

    @Test
    fun `job run should do nothing when there's no hotel to process`() {
        clearSession()

        task.run()

        val sqsMessages = sqsTestUtils.rawMessages
        sqsMessages.size shouldBe 0
    }

    @Test
    fun `job run should publish hotels with featureImportanceUpdatedAt as null `() {

        build(Hotel::class) {
            spid = "test-spid-1"
        }

        build(Hotel::class) {
            spid = "test-spid-2"
        }

        build(Hotel::class) {
            spid = "test-spid-3"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(11)
        }

        clearSession()

        task.run()

        val sqsMessages = sqsTestUtils.rawMessages
        sqsMessages.size shouldBe 2
        sqsMessages[0].body() shouldContain """{"spid":"test-spid-1"}"""
        sqsMessages[1].body() shouldContain """{"spid":"test-spid-2"}"""
    }

    @Test
    fun `job run should publish hotels with featureImportanceUpdatedAt is null or past 90 days`() {

        build(Hotel::class) {
            spid = "test-spid-1"
            featureImportanceLastAttemptAt = ZonedDateTime.now().minusDays(120)
        }

        build(Hotel::class) {
            spid = "test-spid-2"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(60)
        }

        build(Hotel::class) {
            spid = "test-spid-3"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(11)
        }

        build(Hotel::class) {
            spid = "test-spid-4"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(120)
        }

        clearSession()

        task.run()

        val sqsMessages = sqsTestUtils.rawMessages
        sqsMessages.size shouldBe 2
        sqsMessages[0].body() shouldContain """{"spid":"test-spid-4"}"""
        sqsMessages[1].body() shouldContain """{"spid":"test-spid-1"}"""
    }

    @Test
    fun `job run should publish hotels where featureImportanceUpdatedAt past 90 days starts on hotels where featureImportanceLastAttemptAt is null`() {

        build(Hotel::class) {
            spid = "test-spid-0"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(93)
            featureImportanceLastAttemptAt = null
        }

        build(Hotel::class) {
            spid = "test-spid-1"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(91)
            featureImportanceLastAttemptAt = null
        }

        build(Hotel::class) {
            spid = "test-spid-2"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(45)
            featureImportanceLastAttemptAt = ZonedDateTime.now()
        }

        build(Hotel::class) {
            spid = "test-spid-3"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(92)
            featureImportanceLastAttemptAt = ZonedDateTime.now()
        }

        build(Hotel::class) {
            spid = "test-spid-5"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(90)
            featureImportanceLastAttemptAt = ZonedDateTime.now().minusDays(40)
        }

        clearSession()

        task.run()

        val sqsMessages = sqsTestUtils.rawMessages
        sqsMessages.size shouldBe 2
        sqsMessages[0].body() shouldContain """{"spid":"test-spid-0"}"""
        sqsMessages[1].body() shouldContain """{"spid":"test-spid-1"}"""
    }

    @Test
    fun `job run should publish hotels where featureImportanceUpdatedAt past 90 days starts on oldest featureImportanceLastAttemptAt`() {

        build(Hotel::class) {
            spid = "test-spid-0"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(91)
            featureImportanceLastAttemptAt = ZonedDateTime.now().minusDays(10)
        }

        build(Hotel::class) {
            spid = "test-spid-1"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(91)
            featureImportanceLastAttemptAt = ZonedDateTime.now().minusDays(2)
        }

        build(Hotel::class) {
            spid = "test-spid-2"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(45)
            featureImportanceLastAttemptAt = ZonedDateTime.now().minusDays(203)
        }

        build(Hotel::class) {
            spid = "test-spid-3"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(92)
            featureImportanceLastAttemptAt = ZonedDateTime.now().minusDays(40)
        }

        build(Hotel::class) {
            spid = "test-spid-5"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(90)
            featureImportanceLastAttemptAt = ZonedDateTime.now().minusDays(40)
        }

        clearSession()

        task.run()

        val sqsMessages = sqsTestUtils.rawMessages
        sqsMessages.size shouldBe 2
        sqsMessages[0].body() shouldContain """{"spid":"test-spid-3"}"""
        sqsMessages[1].body() shouldContain """{"spid":"test-spid-0"}"""
    }

    @Test
    fun `job run should publish hotels where featureImportanceUpdatedAt past 90 days even if hotel size is less than limit`() {

        build(Hotel::class) {
            spid = "test-spid-0"
            featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(91)
        }

        clearSession()

        task.run()

        val sqsMessages = sqsTestUtils.rawMessages
        sqsMessages.size shouldBe 1
        sqsMessages[0].body() shouldContain """{"spid":"test-spid-0"}"""
    }

}

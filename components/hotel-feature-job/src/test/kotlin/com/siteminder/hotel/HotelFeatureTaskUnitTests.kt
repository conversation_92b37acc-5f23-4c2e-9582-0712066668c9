package com.siteminder.hotel

import com.siteminder.aws.SqsPublisher
import com.siteminder.context.TraceTokenContextUtils
import com.siteminder.predicthq.domain.GeoUtils
import com.siteminder.predicthq.domain.Hotel
import com.siteminder.predicthq.domain.HotelRepository
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.ZonedDateTime
import java.util.concurrent.CompletableFuture

class HotelFeatureTaskUnitTests {

    private lateinit var task: HotelFeatureTask

    private lateinit var hotelRepository: HotelRepository

    private lateinit var sqsPublisher: SqsPublisher

    private val hotelFeatureQueueName = "hotel-feature-queue"

    @BeforeEach
    fun init() {
        hotelRepository = mock()
        sqsPublisher = mock()

        task = HotelFeatureTask(hotelRepository, sqsPublisher, hotelFeatureQueueName, 10)
        TraceTokenContextUtils.setTraceToken("test-trace-token")
    }

    @AfterEach
    fun after() {
        TraceTokenContextUtils.clearToken()
    }

    @Test
    fun `job should skip the hotel that failed to publish to queue and continue processing the rest of the hotels`() {
        whenever(hotelRepository.findAllByFeatureImportanceToUpdate()).thenReturn(
            listOf(
                Hotel(
                    id = 1,
                    spid = "spid1",
                    location = GeoUtils.newGeoPoint(1.0, 2.0),
                    countryCode = "AU",
                    suggestedRadius = 1.toBigDecimal(),
                    createdAt = ZonedDateTime.now(),
                    updatedAt = ZonedDateTime.now()
                ),
                Hotel(
                    id = 2,
                    spid = "spid2",
                    location = GeoUtils.newGeoPoint(2.0, 2.0),
                    countryCode = "AU",
                    suggestedRadius = 2.toBigDecimal(),
                    createdAt = ZonedDateTime.now(),
                    updatedAt = ZonedDateTime.now()
                ),
                Hotel(
                    id = 3,
                    spid = "spid3",
                    location = GeoUtils.newGeoPoint(3.0, 2.0),
                    countryCode = "AU",
                    suggestedRadius = 3.toBigDecimal(),
                    createdAt = ZonedDateTime.now(),
                    updatedAt = ZonedDateTime.now()
                ),
            )
        )

        whenever(sqsPublisher.publish(hotelFeatureQueueName, HotelFeatureTask.HotelMessage("spid1"), "test-trace-token"))
            .thenReturn(CompletableFuture.completedFuture(false))
        whenever(sqsPublisher.publish(hotelFeatureQueueName, HotelFeatureTask.HotelMessage("spid2"), "test-trace-token"))
            .thenReturn(CompletableFuture.completedFuture(true))
        whenever(sqsPublisher.publish(hotelFeatureQueueName, HotelFeatureTask.HotelMessage("spid3"), "test-trace-token"))
            .thenReturn(CompletableFuture.completedFuture(true))

        task.run()

        verify(sqsPublisher).publish(hotelFeatureQueueName, HotelFeatureTask.HotelMessage("spid1"), "test-trace-token")
        verify(sqsPublisher).publish(hotelFeatureQueueName, HotelFeatureTask.HotelMessage("spid2"), "test-trace-token")
        verify(sqsPublisher).publish(hotelFeatureQueueName, HotelFeatureTask.HotelMessage("spid3"), "test-trace-token")
    }
}

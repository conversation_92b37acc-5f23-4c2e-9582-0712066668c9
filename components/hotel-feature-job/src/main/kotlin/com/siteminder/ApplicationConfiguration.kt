package com.siteminder

import com.siteminder.aws.SqsPublisher
import com.siteminder.hotel.HotelFeatureTask
import com.siteminder.metrics.MetricsAutoConfigurationProperties
import com.siteminder.predicthq.domain.HotelRepository
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ApplicationConfiguration {

    @Bean
    fun metricsAutoConfigurationProperties() = MetricsAutoConfigurationProperties("hotel-feature-job")

    @Bean
    fun hotelFeatureTask(
        hotelRepository: HotelRepository,
        sqsPublisher: SqsPublisher,
        @Value("\${predicthq.hotel-feature-queue-url}") hotelFeatureQueueUrl: String,
        @Value("\${predicthq.hotel-feature-job.limit:1000}") limit: Int
    ): HotelFeatureTask {
        return HotelFeatureTask(hotelRepository, sqsPublisher, hotelFeatureQueueUrl, limit)
    }
}

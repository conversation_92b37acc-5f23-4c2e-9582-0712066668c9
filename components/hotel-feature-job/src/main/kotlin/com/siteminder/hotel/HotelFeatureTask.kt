package com.siteminder.hotel

import com.siteminder.aws.SqsPublisher
import com.siteminder.context.TraceTokenContextUtils
import com.siteminder.job.Task
import com.siteminder.metrics.Metrics
import com.siteminder.predicthq.domain.HotelRepository
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import kotlin.math.min

class HotelFeatureTask(
    private val hotelRepository: HotelRepository,
    private val sqsPublisher: SqsPublisher,
    private val hotelFeatureQueueUrl: String,
    private val limit: Int
) : Task {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun run() {
        try {
            val eligibleHotels = hotelRepository.findAllByFeatureImportanceToUpdate().sortedBy { it.featureImportanceLastAttemptAt }

            val hotelsToProcess = eligibleHotels.subList(0, min(eligibleHotels.size, limit))

            val count = hotelsToProcess.size

            logger.info("Processing $count hotels")

            hotelsToProcess.forEachIndexed { idx, hotel ->
                val spid = hotel.spid

                MDC.put("spid", spid)

                logger.info("[${idx + 1}/$count] Processing hotel $spid")

                if (!sqsPublisher.publish(hotelFeatureQueueUrl, HotelMessage(spid), TraceTokenContextUtils.getTraceToken()).get()) {
                    logger.error("Failed to publish message")
                }
            }
        } catch (e: Exception) {
            logger.error("Error running job", e)
            Metrics.meterException(e, this::class.java)
            throw e
        } finally {
            MDC.remove("spid")
        }
    }

    data class HotelMessage(
        val spid: String,
    )
}

package com.siteminder.analysis

import com.siteminder.api.ServerException
import com.siteminder.api.TraceTokenContextUtils
import com.siteminder.api.error.ErrorCode
import com.siteminder.hotel.FeatureImportanceRequest
import com.siteminder.hotel.HotelApiClient
import com.siteminder.metrics.Metrics
import com.siteminder.predicthq.AnalysisNotFoundException
import com.siteminder.predicthq.GetAnalysisResponse
import com.siteminder.predicthq.PredictHqClient
import io.swagger.v3.oas.annotations.Operation
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import jakarta.validation.Valid

@Validated
@RestController
class AnalysisController(private val predictHqClient: PredictHqClient, private val hotelApiClient: HotelApiClient) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Operation(summary = "Requires analysis:write permission")
    @PreAuthorize("hasPermission(null, 'analysis', 'write')")
    @PostMapping("/api/events")
    fun processAnalysis(@Valid @RequestBody request: AnalysisRequest) {
        val timer = Metrics.timer("analysis-processing").time()
        val analysisId = request.analysisId!!
        val spid = request.spid!!

        try {
            MDC.put("spid", spid)

            val analysis = predictHqClient.getAnalysis(analysisId)

            when (analysis.readinessStatus) {

                GetAnalysisResponse.ReadinessStatus.failed -> {
                    Metrics.meter("predicthq-analysis-failed")
                    logger.error("Analysis $analysisId failed with error code ${analysis.readinessChecks?.errorCode}")
                    deleteAnalysisSilently(analysisId)
                    return
                }

                GetAnalysisResponse.ReadinessStatus.pending -> {
                    logger.info("Analysis $analysisId is still pending")
                    throw ServerException(ErrorCode.ERROR.name, "Analysis $analysisId is still pending")
                }

                GetAnalysisResponse.ReadinessStatus.ready -> {
                    val featureImportanceResponse = predictHqClient.getFeatureImportance(analysisId)

                    val featureImportanceList = featureImportanceResponse.featureImportance.map {
                        FeatureImportanceRequest(
                            category = FeatureImportanceRequest.Category.valueOf(it.featureGroup.name),
                            pvalue = it.pvalue.toBigDecimal(),
                            important = it.important
                        )
                    }

                    hotelApiClient.updateFeatureImportance(spid, featureImportanceList, TraceTokenContextUtils.getTraceToken())
                    deleteAnalysisSilently(analysisId)
                }

                null -> {
                    logger.error("Analysis $analysisId has no readiness status")
                    throw ServerException(ErrorCode.ERROR.name, "Analysis $analysisId has no readiness status")
                }
            }
        } catch (e: Exception) {
            logger.error("Error processing analysis", e)
            Metrics.meterException(e, this::class.java)

            when(e) {
                is AnalysisNotFoundException -> return
                is ServerException -> throw e
                else -> throw ServerException(ErrorCode.ERROR.name, "Failed to process analysis: $analysisId.", null, e)
            }

        } finally {
            MDC.remove("spid")
            timer.stop()
        }
    }

    private fun deleteAnalysisSilently(analysisId: String) {
        try {
            predictHqClient.deleteAnalysis(analysisId)
        } catch (e: Exception) {
            logger.error("Failed to delete analysis $analysisId", e)
        }
    }
}

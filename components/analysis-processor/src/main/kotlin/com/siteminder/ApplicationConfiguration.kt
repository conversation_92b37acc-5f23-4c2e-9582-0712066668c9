package com.siteminder

import com.siteminder.api.ApiAutoConfigurationProperties
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ApplicationConfiguration {

    @Bean
    fun apiAutoConfigurationProperties(@Value("\${system.jwt-secret}") secret: String): ApiAutoConfigurationProperties {
        return ApiAutoConfigurationProperties("predicthq", "analysis-processor", secret, "/api/**")
    }

}

package com.siteminder.analysis

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.OutputStreamAppender
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.api.TraceTokenContextExtension
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.metrics.ResetMetricsExtension
import com.siteminder.mockserver.MockServerExtension
import com.siteminder.smPost
import io.kotlintest.matchers.string.shouldContain
import io.kotlintest.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockserver.client.MockServerClient
import org.mockserver.model.HttpRequest
import org.mockserver.verify.VerificationTimes
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.ZoneId
import java.time.ZonedDateTime

@AutoConfigureMockMvc
@SpringBootTest
@ExtendWith(MockServerExtension::class, TraceTokenContextExtension::class, ResetMetricsExtension::class)
class AnalysisControllerProcessTests {

    @Autowired
    private lateinit var mockMvc: MockMvc

    private lateinit var mockServerClient: MockServerClient

    private lateinit var mockedAppender: OutputStreamAppender<ILoggingEvent>
    
    private val spid = "spid123"
    private val analysisId = "analysisId123"

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("analysis:write")
            .sign()
        mockedAppender = mock()
        val logger = LoggerFactory.getLogger(AnalysisController::class.java) as Logger
        logger.level = Level.INFO
        logger.addAppender(mockedAppender)
    }

    @Test
    fun `post analysis should return 200 and just log and delete analysis when analysis failed`() {
        val getAnalysisRequest = apiRequest(path = "/v1/beam/analyses/$analysisId", traceToken = null)
        val getAnalysisResponsePayload = """
            {
              "name": "$spid",
              "status": "draft",
              "readiness_status": "failed",
              "readiness_checks": {
                "date_range": {
                  "start": "2023-01-01",
                  "end": "2023-07-01"
                },
                "error_code": "BELOW_MINIMUM_THRESHOLD"
              },
              "processing_completed": {
                "correlation": false,
                "feature_importance": false
              }
            }
        """.trimIndent()
        mockServerClient.`when`(getAnalysisRequest).respond(
            apiResponse(body = getAnalysisResponsePayload)
        )

        val deleteAnalysisRequest = apiRequest(path = "/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE, traceToken = null)
        mockServerClient.`when`(deleteAnalysisRequest).respond(
            apiResponse<Void>(
                status = HttpStatus.TOO_MANY_REQUESTS
            )
        )

        mockMvc.perform(smPost("/api/events", jwt, requestBody()))
            .andExpect(status().isOk)

        mockServerClient.verify(getAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(deleteAnalysisRequest, VerificationTimes.once())

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(2)).doAppend(argumentCaptor.capture())
        argumentCaptor.allValues.first().message shouldBe "Analysis $analysisId failed with error code BELOW_MINIMUM_THRESHOLD"
        argumentCaptor.allValues[1].message shouldBe "Failed to delete analysis $analysisId"
    }

    @Test
    fun `post analysis should return 200 and ignore when analysis is not found`() {
        val getAnalysisRequest = apiRequest(path = "/v1/beam/analyses/$analysisId", traceToken = null)
        mockServerClient.`when`(getAnalysisRequest).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
                {
                  "error": "Analysis not found"
                }
            """.trimIndent())
        )

        mockMvc.perform(smPost("/api/events", jwt, requestBody()))
            .andExpect(status().isOk)

        mockServerClient.verify(getAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withPath("/v1/beam/analyses/{analysisId}/feature-importance"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withPath("/v1/beam/analyses/$analysisId").withMethod("DELETE"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withPath("/api/hotels/$spid/feature-importance"), VerificationTimes.never())
    }

    @Test
    fun `post analysis should return 500 when analysis is pending`() {
        val getAnalysisRequest = apiRequest(path = "/v1/beam/analyses/$analysisId", traceToken = null)
        val getAnalysisResponsePayload = """
            {
              "name": "$spid",
              "status": "draft",
              "readiness_status": "pending",
              "readiness_checks": {
                "date_range": {
                  "start": "2023-01-01",
                  "end": "2023-07-01"
                }
              },
              "processing_completed": {
                "correlation": false,
                "feature_importance": false
              }
            }
        """.trimIndent()
        mockServerClient.`when`(getAnalysisRequest).respond(
            apiResponse(body = getAnalysisResponsePayload)
        )

        mockMvc.perform(smPost("/api/events", jwt, requestBody()))
            .andExpect(status().isInternalServerError)


        mockServerClient.verify(getAnalysisRequest, VerificationTimes.once())

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(2)).doAppend(argumentCaptor.capture())
        argumentCaptor.allValues[0].message shouldBe "Analysis $analysisId is still pending"
        argumentCaptor.allValues[1].message shouldContain "Error processing analysis"
    }

    @Test
    fun `post analysis should return 500 when feature importance results is not ready`() {
        val getAnalysisRequest = apiRequest(path = "/v1/beam/analyses/$analysisId", traceToken = null)
        val getAnalysisResponsePayload = """
            {
              "name": "$spid",
              "status": "draft",
              "readiness_status": "ready",
              "readiness_checks": {
                "date_range": {
                  "start": "2023-01-01",
                  "end": "2023-07-01"
                }
              },
              "processing_completed": {
                "correlation": true,
                "feature_importance": false
              }
            }
        """.trimIndent()
        mockServerClient.`when`(getAnalysisRequest).respond(
            apiResponse(body = getAnalysisResponsePayload)
        )

        val getFeatureImportanceRequest = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance", traceToken = null)
        val getFeatureImportanceResponseBody = """
            {
              "error": "Feature Importance results are not ready"
            }
        """.trimIndent()
        mockServerClient.`when`(getFeatureImportanceRequest).respond(
            apiResponse(body = getFeatureImportanceResponseBody, status = HttpStatus.BAD_REQUEST)
        )

        mockMvc.perform(smPost("/api/events", jwt, requestBody()))
            .andExpect(status().isInternalServerError)

        mockServerClient.verify(getAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(getFeatureImportanceRequest, VerificationTimes.once())

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(1)).doAppend(argumentCaptor.capture())
        argumentCaptor.allValues.first().message shouldContain "Error processing analysis"
    }

    @Test
    fun `post analysis should return 200 and update feature importance then finally delete the analysis`() {
        val today = ZonedDateTime.now(ZoneId.of("UTC"))

        val getAnalysisRequest = apiRequest(path = "/v1/beam/analyses/$analysisId", traceToken = null)
        val getAnalysisResponsePayload = """
            {
              "name": "$spid",
              "status": "draft",
              "readiness_status": "ready",
              "readiness_checks": {
                "date_range": {
                  "start": "2023-01-01",
                  "end": "2023-07-01"
                }
              },
              "processing_completed": {
                "correlation": true,
                "feature_importance": false
              }
            }
        """.trimIndent()
        mockServerClient.`when`(getAnalysisRequest).respond(
            apiResponse(body = getAnalysisResponsePayload)
        )

        val getFeatureImportanceRequest = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance", traceToken = null)
        val getFeatureImportanceResponseBody = """
            {
              "feature_importance": [
                {
                  "feature_group": "school-holidays",
                  "features": [
                    "phq_attendance_school-holidays"
                  ],
                  "p_value": 0.607,
                  "important": false
                },
                {
                  "feature_group": "concerts",
                  "features": [
                    "phq_attendance_concerts"
                  ],
                  "p_value": 0.01,
                  "important": true
                }
              ]
            }
        """.trimIndent()
        mockServerClient.`when`(getFeatureImportanceRequest).respond(
            apiResponse(body = getFeatureImportanceResponseBody)
        )

        val updateFeatureImportanceRequestPayload = """
            [
              {
                "category": "school-holidays",
                "pvalue": 0.607,
                "important": false
              },
              {
                "category": "concerts",
                "pvalue": 0.01,
                "important": true
              }
            ]
        """.trimIndent()
        val updateFeatureImportanceRequest = apiRequest(path = "/api/hotels/$spid/feature-importance", body = updateFeatureImportanceRequestPayload, httpMethod = HttpMethod.PUT)
        val updateFeatureImportanceResponsePayload = """
            {
              "spid": "$spid",
              "latitude": -23.43,
              "longitude": 43.56,
              "countryCode": "AU",
              "suggestedRadius": 3.3,
              "featureImportance": [
                {
                  "category": "school-holidays",
                  "pvalue": 0.607,
                  "important": false
                },
                {
                  "category": "concerts",
                  "pvalue": 0.01,
                  "important": true
                }
              ],
              "featureImportanceUpdatedAt": "$today",
              "createdAt": "$today",
              "updatedAt": "$today"
            }
        """.trimIndent()
        mockServerClient.`when`(updateFeatureImportanceRequest).respond(
            apiResponse(body = updateFeatureImportanceResponsePayload)
        )

        val deleteAnalysisRequest = apiRequest(path = "/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE, traceToken = null)
        mockServerClient.`when`(deleteAnalysisRequest).respond(
            apiResponse<Void>()
        )

        mockMvc.perform(smPost("/api/events", jwt, requestBody()))
            .andExpect(status().isOk)

        mockServerClient.verify(getAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(getFeatureImportanceRequest, VerificationTimes.once())
        mockServerClient.verify(updateFeatureImportanceRequest, VerificationTimes.once())
        mockServerClient.verify(deleteAnalysisRequest, VerificationTimes.once())

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(0)).doAppend(argumentCaptor.capture())
    }

    @Test
    fun `post analysis should return 500 when unexpected error happens`() {
        val getAnalysisRequest = apiRequest(path = "/v1/beam/analyses/$analysisId", traceToken = null)
        mockServerClient.`when`(getAnalysisRequest).respond(
            apiResponse<Void>(status = HttpStatus.BAD_REQUEST)
        )

        mockMvc.perform(smPost("/api/events", jwt, requestBody()))
            .andExpect(status().isInternalServerError)
            .andExpect(content().json("""
                {
                  "errors": [
                    {
                      "code": "ERROR",
                      "message": "Failed to process analysis: $analysisId."
                    }
                  ]
                }
            """.trimIndent()))

        mockServerClient.verify(getAnalysisRequest, VerificationTimes.once())

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(1)).doAppend(argumentCaptor.capture())
        argumentCaptor.allValues.first().message shouldContain "Error processing analysis"
    }

    private fun requestBody() = """
            {
              "spid": "$spid",
              "analysisId": "$analysisId"
            }
        """.trimIndent()

}

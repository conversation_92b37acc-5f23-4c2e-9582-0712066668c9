spring.profiles.include=localstack
predicthq.hotel-api.base-url=http://mockserver:1080
predicthq.hotel-api.token-path=tokens/predicthq-hotel-api-token
predicthq.hotel-api.concurrency=100
predicthq.hotel-api.timeout-in-seconds=60
predicthq.api.base-url=http://mockserver:1080
predicthq.api.access-token=predicthq-api-access-token
predicthq.api.concurrency=100
predicthq.api.timeout-in-seconds=60

system.jwt-secret=secret
secrets.bucketName=sm.secrets.dev

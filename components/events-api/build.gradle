apply plugin: 'springboot-kotlin-configuration-plugin' // https://github.com/siteminder-au/sm-gradle-plugins/tree/master/sm-build-plugin

dependencies {
    implementation 'com.databricks:databricks-jdbc:2.6.34'
    implementation "com.siteminder:sm-spring-boot-api-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-http-client-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-jwt-client-starter:${smLibraryVersion}"
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'

    implementation project(":libs:hotel-api-client")

    testImplementation "com.siteminder:sm-spring-boot-api-starter-test:${smLibraryVersion}"
    testImplementation "com.siteminder:sm-spring-boot-starter-test:${smLibraryVersion}"
}

// These directives handle Apache Arrow issues with Java 17 modules
test {
    doFirst {
        jvmArgs '--add-opens', 'java.base/java.nio=ALL-UNNAMED'
    }
}

bootJar {
    manifest {
        attributes["Add-Opens"] = "java.base/java.nio"
    }
}

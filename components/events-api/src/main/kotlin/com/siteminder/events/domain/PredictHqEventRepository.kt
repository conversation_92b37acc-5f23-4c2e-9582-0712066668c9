package com.siteminder.events.domain

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.siteminder.events.Page
import com.siteminder.events.PageRequest
import com.siteminder.events.PredictHqEvent
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import java.sql.ResultSet
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*
import kotlin.math.ceil

class PredictHqEventRepository(private val databricksNamedParameterJdbcTemplate: NamedParameterJdbcTemplate, private val objectMapper: ObjectMapper) {

    fun getEventsWithRadiusMultiplier(searchParams: PredictHqEventSearchParams, pageRequest: PageRequest): Page<PredictHqEvent> {

        val sqlQueryBuilder = SqlQueryBuilder(EVENTS_TABLE, SELECT_COLUMNS)

        locationWithRadiusMultiplierFilter(sqlQueryBuilder, searchParams.latitude, searchParams.longitude, searchParams.radius)

        addCommonFilters(sqlQueryBuilder, searchParams)

        return getEventsPage(sqlQueryBuilder, pageRequest)
    }

    fun getEvents(searchParams: PredictHqEventSearchParams, pageRequest: PageRequest): Page<PredictHqEvent> {

        val sqlQueryBuilder = SqlQueryBuilder(EVENTS_TABLE, SELECT_COLUMNS)

        locationFilter(sqlQueryBuilder, searchParams.latitude, searchParams.longitude, searchParams.radius)

        addCommonFilters(sqlQueryBuilder, searchParams)

        return getEventsPage(sqlQueryBuilder, pageRequest)
    }

    private fun getEventsPage(sqlQueryBuilder: SqlQueryBuilder, pageRequest: PageRequest): Page<PredictHqEvent> {

        sqlQueryBuilder.paginate(
            offset = pageRequest.page.minus(1).times(pageRequest.size),
            limit = pageRequest.size
        )

        val events = databricksNamedParameterJdbcTemplate.query(sqlQueryBuilder.buildQuery(), sqlQueryBuilder.params, rowMapper)

        // Avoid count query if we are on the first page and the total number of events is less than the page size
        val totalRowCount = if (pageRequest.page == 1 && events.size < pageRequest.size) {
            events.size.toLong()
        } else {
            databricksNamedParameterJdbcTemplate.queryForList(sqlQueryBuilder.buildCountQuery(), sqlQueryBuilder.params, Long::class.java).firstOrNull() ?: 0
        }

        return Page(
            content = events,
            page = pageRequest.page,
            totalPages = ceil(totalRowCount.div(pageRequest.size.toDouble())).toInt(),
            totalElements = totalRowCount
        )
    }

    fun getEvent(eventId: String): PredictHqEvent? {

        val sqlQueryBuilder = SqlQueryBuilder(EVENTS_TABLE, SELECT_COLUMNS)
            .filter("EVENT_ID = :eventId", "eventId" to eventId)
            .filter("STATUS IN ('active', 'postponed', 'cancelled')")
            .singleRow()

        return databricksNamedParameterJdbcTemplate.query(sqlQueryBuilder.buildQuery(), sqlQueryBuilder.params, rowMapper).firstOrNull()
    }

    fun getEvents(eventIds: List<String>): List<PredictHqEvent> {

        val sqlQueryBuilder = SqlQueryBuilder(EVENTS_TABLE, SELECT_COLUMNS)
            .filter("EVENT_ID IN (:eventIds)", "eventIds" to eventIds)
            .filter("STATUS IN ('active', 'postponed', 'cancelled')")

        return databricksNamedParameterJdbcTemplate.query(sqlQueryBuilder.buildQuery(), sqlQueryBuilder.params, rowMapper)
    }

    private fun addCommonFilters(sqlQueryBuilder: SqlQueryBuilder, searchParams: PredictHqEventSearchParams) {
        eventDateFilter(sqlQueryBuilder, searchParams.start, searchParams.end)
        createdDateFilter(sqlQueryBuilder, searchParams.createdStart, searchParams.createdEnd)

        includeCategoriesFilter(sqlQueryBuilder, searchParams.includeCategories)
        excludeCategoriesFilter(sqlQueryBuilder, searchParams.excludeCategories)

        includeStatusFilter(sqlQueryBuilder, searchParams.includeStatuses)
        excludeStatusFilter(sqlQueryBuilder, searchParams.excludeStatuses)

        includeCountryCodesFilter(sqlQueryBuilder, searchParams.includeCountryCodes)
        excludeCountryCodesFilter(sqlQueryBuilder, searchParams.excludeCountryCodes)
    }

    private fun eventDateFilter(sqlQueryBuilder: SqlQueryBuilder, start: ZonedDateTime?, end: ZonedDateTime?) {
        if (start != null && end != null) {
            sqlQueryBuilder.filter(
                "EVENT_END_DT >= :eventStart AND EVENT_START_DT <= :eventEnd",
                mapOf(
                    "eventStart" to start.toTimestamp(),
                    "eventEnd" to end.toTimestamp()
                )
            )
        } else if (start != null) {
            sqlQueryBuilder.filter(
                "EVENT_START_DT >= :eventStart",
                "eventStart" to start.toTimestamp()
            )
        } else if (end != null) {
            sqlQueryBuilder.filter(
                "EVENT_END_DT <= :eventEnd",
                "eventEnd" to end.toTimestamp()
            )
        }
    }

    private fun createdDateFilter(sqlQueryBuilder: SqlQueryBuilder, createdStart: ZonedDateTime?, createdEnd: ZonedDateTime?) {
        if (createdStart != null) {
            sqlQueryBuilder.filter(
                "CREATE_DT >= :createdStart",
                "createdStart" to createdStart.toTimestamp()
            )
        }

        if (createdEnd != null) {
            sqlQueryBuilder.filter(
                "CREATE_DT <= :createdEnd",
                "createdEnd" to createdEnd.toTimestamp()
            )
        }
    }

    private fun includeCategoriesFilter(sqlQueryBuilder: SqlQueryBuilder, includeCategories: List<PredictHqEvent.Category>) {
        if (includeCategories.isNotEmpty()) {
            sqlQueryBuilder.filter(
                "CATEGORY IN (:includeCategories)",
                "includeCategories" to includeCategories.map { it.value }
            )
        }
    }

    private fun excludeCategoriesFilter(sqlQueryBuilder: SqlQueryBuilder, excludeCategories: List<PredictHqEvent.Category>) {
        if (excludeCategories.isNotEmpty()) {
            sqlQueryBuilder.filter(
                "CATEGORY NOT IN (:excludeCategories)",
                "excludeCategories" to excludeCategories.map { it.value }
            )
        }
    }

    private fun includeStatusFilter(sqlQueryBuilder: SqlQueryBuilder, includeStatuses: List<PredictHqEvent.Status>) {
        if (includeStatuses.isNotEmpty()) {
            sqlQueryBuilder.filter(
                "STATUS IN (:includeStatuses)",
                "includeStatuses" to includeStatuses.map { it.name }
            )
        }
    }

    private fun excludeStatusFilter(sqlQueryBuilder: SqlQueryBuilder, excludeStatuses: List<PredictHqEvent.Status>) {
        if (excludeStatuses.isNotEmpty()) {
            sqlQueryBuilder.filter(
                "STATUS NOT IN (:excludeStatuses)",
                "excludeStatuses" to excludeStatuses.map { it.name }
            )
        }
    }

    private fun includeCountryCodesFilter(sqlQueryBuilder: SqlQueryBuilder, includeCountryCodes: List<String>) {
        if (includeCountryCodes.isNotEmpty()) {
            sqlQueryBuilder.filter(
                "COUNTRY_CODE IN (:includeCountryCodes)",
                "includeCountryCodes" to includeCountryCodes
            )
        }
    }

    private fun excludeCountryCodesFilter(sqlQueryBuilder: SqlQueryBuilder, excludeCountryCodes: List<String>) {
        if (excludeCountryCodes.isNotEmpty()) {
            sqlQueryBuilder.filter(
                "COUNTRY_CODE NOT IN (:excludeCountryCodes)",
                "excludeCountryCodes" to excludeCountryCodes
            )
        }
    }

    private fun locationWithRadiusMultiplierFilter(sqlQueryBuilder: SqlQueryBuilder, lat: Double?, lon: Double?, radius: Double?) {
        if (lat != null && lon != null && radius != null) {
            sqlQueryBuilder.filter("LAT IS NOT NULL")
            sqlQueryBuilder.filter("LON IS NOT NULL")
            sqlQueryBuilder.filter(
                """
                    (
                      ((PHQ_RANK > 70 OR LOCAL_RANK > 80) AND (111.111 *
                        DEGREES(ACOS(LEAST(1.0, COS(RADIANS(:latitude))
                          * COS(RADIANS(LAT))
                          * COS(RADIANS(:longitude - LON))
                          + SIN(RADIANS(:latitude))
                          * SIN(RADIANS(LAT)))))) < :radius)
                      OR ((LOCAL_RANK >= 95) AND (111.111 *
                        DEGREES(ACOS(LEAST(1.0, COS(RADIANS(:latitude))
                          * COS(RADIANS(LAT))
                          * COS(RADIANS(:longitude - LON))
                          + SIN(RADIANS(:latitude))
                          * SIN(RADIANS(LAT)))))) < (:radius * 3))
                      OR ((LOCAL_RANK >= 99) AND (111.111 *
                        DEGREES(ACOS(LEAST(1.0, COS(RADIANS(:latitude))
                          * COS(RADIANS(LAT))
                          * COS(RADIANS(:longitude - LON))
                          + SIN(RADIANS(:latitude))
                          * SIN(RADIANS(LAT)))))) < (:radius * 5))
                    )
                """.trimIndent(),
                mapOf(
                    "latitude" to lat,
                    "longitude" to lon,
                    "radius" to radius
                )
            )
        }
    }

    private fun locationFilter(sqlQueryBuilder: SqlQueryBuilder, lat: Double?, lon: Double?, radius: Double?) {
        if (lat != null && lon != null && radius != null) {
            sqlQueryBuilder.filter("LAT IS NOT NULL")
            sqlQueryBuilder.filter("LON IS NOT NULL")
            sqlQueryBuilder.filter("(PHQ_RANK > 70 OR LOCAL_RANK > 80)")
            sqlQueryBuilder.filter(
                """
                (111.111 *
                DEGREES(ACOS(LEAST(1.0, COS(RADIANS(:latitude))
                  * COS(RADIANS(LAT))
                  * COS(RADIANS(:longitude - LON))
                  + SIN(RADIANS(:latitude))
                  * SIN(RADIANS(LAT)))))) < :radius
                """,
                mapOf(
                    "latitude" to lat,
                    "longitude" to lon,
                    "radius" to radius
                )
            )
        }
    }

    private val rowMapper = RowMapper { rs, _ ->
        PredictHqEvent(
            eventId = rs.getString("EVENT_ID"),
            createdDate = rs.getZonedDateTime("CREATE_DT")!!,
            updatedDate = rs.getZonedDateTime("UPDATE_DT")!!,
            title = rs.getString("TITLE"),
            category = rs.getCategory("CATEGORY"),
            description = rs.getString("DESCRIPTION"),
            eventStart = rs.getZonedDateTime("EVENT_START_DT")!!,
            eventEnd = rs.getZonedDateTime("EVENT_END_DT")!!,
            predictedEnd = rs.getZonedDateTime("PREDICTED_END_DT"),
            timezone = rs.getString("TIMEZONE"),
            entities = rs.getString("ENTITIES")?.let { objectMapper.readValue<List<PredictHqEvent.Entity>>(it) } ?: emptyList(),
            scope = rs.getString("SCOPE")?.let(PredictHqEvent.Scope::valueOf),
            countryCode = rs.getString("COUNTRY_CODE"),
            phqAttendance = rs.getLongNullable("PHQ_ATTENDANCE"),
            phqRank = rs.getLong("PHQ_RANK"),
            localRank = rs.getLongNullable("LOCAL_RANK"),
            aviationRank = rs.getLongNullable("AVIATION_RANK"),
            status = rs.getString("STATUS").let(PredictHqEvent.Status::valueOf),
            brandSafe = rs.getBoolean("BRAND_SAFE"),
            cancelledDate = rs.getZonedDateTime("CANCELLED_DT"),
            postponedDate = rs.getZonedDateTime("POSTPONED_DT"),
            predictedEventSpendAccommodation = rs.getLongNullable("PREDICTED_EVENT_SPEND_ACCOMMODATION"),
            predictedEventSpendHospitality = rs.getLongNullable("PREDICTED_EVENT_SPEND_HOSPITALITY"),
            predictedEventSpendTransportation = rs.getLongNullable("PREDICTED_EVENT_SPEND_TRANSPORTATION"),
            lon = rs.getBigDecimal("LON"),
            lat = rs.getBigDecimal("LAT"),
            impactPatterns = rs.getString("IMPACT_PATTERNS")?.let { objectMapper.readValue<List<PredictHqEvent.ImpactPattern>>(it) } ?: emptyList()
        )
    }

    private fun ResultSet.getLongNullable(column: String): Long? {
        val value = this.getLong(column)

        if (this.wasNull()) {
            return null
        }

        return value
    }

    private fun ResultSet.getZonedDateTime(column: String): ZonedDateTime? {
        val value = this.getTimestamp(column)

        if (this.wasNull()) {
            return null
        }

        return value.toInstant().atZone(ZoneId.of("UTC"))
    }

    private fun ResultSet.getCategory(column: String): PredictHqEvent.Category {

        val value = this.getString(column)

        return PredictHqEvent.Category.values().find { it.value == value } ?: throw IllegalArgumentException("Invalid category value: $value")
    }

    private fun ZonedDateTime.toTimestamp() = Date.from(this.toInstant())

    private val EVENTS_TABLE = "events"

    private val SELECT_COLUMNS = listOf(
        "EVENT_ID",
        "CREATE_DT",
        "UPDATE_DT",
        "TITLE",
        "CATEGORY",
        "DESCRIPTION",
        "EVENT_START_DT",
        "EVENT_END_DT",
        "PREDICTED_END_DT",
        "TIMEZONE",
        "ENTITIES",
        "SCOPE",
        "COUNTRY_CODE",
        "PHQ_ATTENDANCE",
        "PHQ_RANK",
        "LOCAL_RANK",
        "AVIATION_RANK",
        "STATUS",
        "BRAND_SAFE",
        "CANCELLED_DT",
        "POSTPONED_DT",
        "PREDICTED_EVENT_SPEND_ACCOMMODATION",
        "PREDICTED_EVENT_SPEND_HOSPITALITY",
        "PREDICTED_EVENT_SPEND_TRANSPORTATION",
        "LON",
        "LAT",
        "IMPACT_PATTERNS"
    )
}

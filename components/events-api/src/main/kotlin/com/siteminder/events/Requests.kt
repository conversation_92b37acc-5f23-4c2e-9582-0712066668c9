package com.siteminder.events

import com.fasterxml.jackson.annotation.JsonValue
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.Size

enum class CategoryRequest(@get:JsonValue val value: String) {

    observances("observances"),
    public_holidays("public-holidays"),
    concerts("concerts"),
    sports("sports"),
    community("community"),
    expos("expos"),
    performing_arts("performing-arts"),
    conferences("conferences"),
    school_holidays("school-holidays"),
    festivals("festivals")
}

enum class StatusRequest {
    active, postponed, cancelled, predicted, archived
}

data class GetEventsByIdsRequest(
    @field:NotEmpty
    @field:Size(min = 1, max = 5000)
    val eventIds: List<String>?
)
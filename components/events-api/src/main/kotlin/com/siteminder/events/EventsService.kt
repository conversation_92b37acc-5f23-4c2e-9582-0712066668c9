package com.siteminder.events

import com.siteminder.api.NotFoundException
import com.siteminder.api.TraceTokenContextUtils
import com.siteminder.events.domain.PredictHqEventRepository
import com.siteminder.events.domain.PredictHqEventSearchParams
import com.siteminder.hotel.HotelApiClient
import java.time.ZonedDateTime

class EventsService(private val predictHqEventRepository: PredictHqEventRepository, private val hotelApiClient: HotelApiClient) {

    fun getEvents(spid: String?, latitude: Double?, longitude: Double?, radius: Double?, start: ZonedDateTime?, end: ZonedDateTime?, createdStart: ZonedDateTime?, createdEnd: ZonedDateTime?, categories: List<CategoryRequest>, excludeCategories: List<CategoryRequest>, statuses: List<StatusRequest>, excludeStatuses: List<StatusRequest>, countryCodes: List<String> = emptyList(), excludeCountryCodes: List<String> = emptyList(), pageRequest: PageRequest): Page<PredictHqEvent> {
        val params = PredictHqEventSearchParams(
            latitude = latitude,
            longitude = longitude,
            radius = radius,
            start = start,
            end = end,
            createdStart = createdStart,
            createdEnd = createdEnd,
            includeCategories = categories.map { PredictHqEvent.Category.valueOf(it.name) },
            excludeCategories = excludeCategories.map { PredictHqEvent.Category.valueOf(it.name) },
            includeStatuses = statuses.map { PredictHqEvent.Status.valueOf(it.name) },
            excludeStatuses = excludeStatuses.map { PredictHqEvent.Status.valueOf(it.name) },
            includeCountryCodes = countryCodes,
            excludeCountryCodes = excludeCountryCodes
        )

        if (spid != null) {
            val hotel = hotelApiClient.getHotel(spid, TraceTokenContextUtils.getTraceToken()) ?: throw NotFoundException("Hotel not found", mapOf("spid" to spid))
            params.latitude = hotel.latitude.toDouble()
            params.longitude = hotel.longitude.toDouble()
            params.radius = hotel.suggestedRadius.toDouble()
            params.includeCountryCodes = listOf(hotel.countryCode)

            return predictHqEventRepository.getEventsWithRadiusMultiplier(params, pageRequest)
        }

        return predictHqEventRepository.getEvents(params, pageRequest)
    }

    fun getEvents(eventIds: List<String>): List<PredictHqEvent> {
        return predictHqEventRepository.getEvents(eventIds)
    }

    fun getEvent(eventId: String): PredictHqEvent? = predictHqEventRepository.getEvent(eventId)
}

package com.siteminder.events.domain

class SqlQueryBuilder(table: String, columns: List<String>) {

    val params: MutableMap<String, Any> = mutableMapOf()
    private val filters: MutableList<String> = mutableListOf()
    private var limit: Int = -1
    private var offset: Int = -1

    private val sql: String = columns.joinToString(prefix = "SELECT\n  ", separator = ",\n  ", postfix = "\nFROM $table")
    private val countSql: String = "SELECT COUNT(*) FROM $table"

    init {
        require(table.isNotBlank()) { "Table cannot be blank" }
        require(columns.isNotEmpty()) { "Columns cannot be empty" }
    }

    fun filter(sqlFilter: String): SqlQueryBuilder {
        filters.add(sqlFilter)
        return this
    }

    fun filter(sqlFilter: String, param: Pair<String, Any>): SqlQueryBuilder {
        filters.add(sqlFilter)
        params[param.first] = param.second
        return this
    }

    fun filter(sqlFilter: String, params: Map<String, Any>): SqlQueryBuilder {
        filters.add(sqlFilter)
        params.forEach { (name, value) -> this.params[name] = value }
        return this
    }

    fun singleRow(): SqlQueryBuilder {
        this.limit = 1
        return this
    }

    fun paginate(offset: Int, limit: Int): SqlQueryBuilder {

        require(offset >= 0) { "Offset must be greater than or equal to 0" }
        require(limit > 0) { "Limit must be greater than 0" }

        this.offset = offset
        this.limit = limit
        return this
    }

    fun buildQuery(): String {
        val sql = if (filters.isEmpty()) sql else "$sql\nWHERE " + filters.joinToString(separator = " AND ")
        if (offset != -1 && limit != -1) {
            return "$sql\nLIMIT $limit OFFSET $offset"
        } else if (limit != -1) {
            return "$sql\nLIMIT $limit"
        }
        return sql
    }

    fun buildCountQuery(): String {
        return if (filters.isEmpty()) countSql else "$countSql WHERE " + filters.joinToString(separator = " AND ")
    }
}

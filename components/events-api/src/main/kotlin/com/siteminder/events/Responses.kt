package com.siteminder.events

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonValue
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime

@JsonInclude(JsonInclude.Include.NON_NULL)
data class EventResponse(

    @field:Schema(required = true)
    val eventId: String,

    @field:Schema(required = true)
    val createdDate: ZonedDateTime,

    @field:Schema(required = true)
    val updatedDate: ZonedDateTime,

    @field:Schema(required = true)
    val title: String,

    @field:Schema(required = true)
    val category: Category,

    @field:Schema(required = true)
    val eventStart: ZonedDateTime,

    @field:Schema(required = true)
    val eventEnd: ZonedDateTime,

    @field:Schema(required = false, nullable = true)
    val predictedEnd: ZonedDateTime?,

    @field:Schema(required = false, nullable = true)
    val timezone: String?,

    @field:Schema(required = true)
    val entities: List<Entity>,

    @field:Schema(required = false, nullable = true)
    val scope: Scope?,

    @field:Schema(required = false, nullable = true, pattern = "ISO 3166-1 alpha-2")
    val countryCode: String?,

    @field:Schema(required = false, nullable = true)
    val phqAttendance: Long?,

    @field:Schema(required = true)
    val phqRank: Long,

    @field:Schema(required = false, nullable = true)
    val localRank: Long?,

    @field:Schema(required = false, nullable = true)
    val aviationRank: Long?,

    @field:Schema(required = true)
    val status: Status,

    @field:Schema(required = true)
    val brandSafe: Boolean,

    @field:Schema(required = false, nullable = true)
    val cancelledDate: ZonedDateTime?,

    @field:Schema(required = false, nullable = true)
    val postponedDate: ZonedDateTime?,

    @field:Schema(required = false, nullable = true, description = "Predicted event spend for Accommodation in USD")
    val predictedEventSpendAccommodation: Long?,

    @field:Schema(required = false, nullable = true, description = "Predicted event spend for Hospitality in USD")
    val predictedEventSpendHospitality: Long?,

    @field:Schema(required = false, nullable = true, description = "Predicted event spend for Transportation in USD")
    val predictedEventSpendTransportation: Long?,

    @field:Schema(required = true)
    val lon: BigDecimal,

    @field:Schema(required = true)
    val lat: BigDecimal,

    @field:Schema(required = true)
    val impactPatterns: List<ImpactPattern>

) {

    enum class Category(@get:JsonValue val value: String) {
        observances("observances"),
        public_holidays("public-holidays"),
        concerts("concerts"),
        sports("sports"),
        community("community"),
        expos("expos"),
        performing_arts("performing-arts"),
        conferences("conferences"),
        school_holidays("school-holidays"),
        festivals("festivals")
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class Entity(

        @field:Schema(required = true)
        val entityId: String,

        @field:Schema(required = false, nullable = true)
        val category: Category?,

        @field:Schema(required = false, nullable = true)
        val description: String?,

        @field:Schema(required = false, nullable = true)
        val labels: List<String>?,

        @field:Schema(required = false, nullable = true)
        val formattedAddress: String?,

        @field:Schema(required = true)
        val name: String,

        @field:Schema(required = false, nullable = true)
        val timezone: String?,

        @field:Schema(required = false, nullable = true)
        val recurring: Recurring?,

        @field:Schema(required = true)
        val type: Type
    ) {
        data class Recurring(
            @field:Schema(required = true)
            val ical: String
        )

        enum class Type(@get:JsonValue val value: String) {
            event_group("event-group"),
            venue("venue")
        }
    }

    enum class Scope {
        locality, localadmin, county, region, country
    }

    enum class Status {
        active, postponed, cancelled, predicted, archived
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class ImpactPattern(

        @field:Schema(required = true)
        val impactType: ImpactType,

        @field:Schema(required = true)
        val impacts: List<Impact>,

        @field:Schema(required = true)
        val vertical: Vertical
    ) {

        enum class ImpactType {
            phq_rank, phq_attendance
        }

        @JsonInclude(JsonInclude.Include.NON_NULL)
        data class Impact(
            @field:Schema(required = true)
            val dateLocal: LocalDate,

            @field:Schema(required = true)
            val position: Position,

            @field:Schema(required = true)
            val value: Int
        ) {

            enum class Position {
                leading, event_day, lagging
            }
        }

        enum class Vertical {
            retail, accommodation, hospitality
        }
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class PagedResponse<T>(
    @field:Schema(required = true) val items: List<T>,
    @field:Schema(required = true) val page: Int,
    @field:Schema(required = true) val pages: Int,
    @field:Schema(required = true) val total: Long
)

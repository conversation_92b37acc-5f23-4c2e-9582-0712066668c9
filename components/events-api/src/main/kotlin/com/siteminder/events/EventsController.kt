package com.siteminder.events

import com.siteminder.api.BadRequestException
import com.siteminder.api.NotFoundException
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime

@Validated
@RestController
class EventsController(private val eventsService: EventsService) {

    @GetMapping("/api/events")
    @PreAuthorize("hasPermission(null, 'events', 'read')")
    @Operation(summary = "Requires events:read permission")
    fun getEvents(
        @RequestParam(required = false) spid: String?,
        @RequestParam(required = false) @Min(-90) @Max(90) latitude: Double?,
        @RequestParam(required = false) @Min(-180) @Max(180) longitude: Double?,
        @RequestParam(required = false) @Min(1) radius: Double?,
        @Schema(description = "Start of range for Event dates (inclusive)", format = "date-time") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam(required = false) start: ZonedDateTime?,
        @Schema(description = "End of range for Event dates (inclusive)", format = "date-time") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam(required = false) end: ZonedDateTime?,
        @Schema(description = "Start of range for createdDate (inclusive)", format = "date-time") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam(required = false) createdStart: ZonedDateTime?,
        @Schema(description = "End of range for createdDate (inclusive)", format = "date-time") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam(required = false) createdEnd: ZonedDateTime?,
        @RequestParam(required = false) countryCode: List<String>?,
        @RequestParam(required = false) category: List<CategoryRequest>?,
        @RequestParam(required = false) excludeCategory: List<CategoryRequest>?,
        @RequestParam(required = false) status: List<StatusRequest>?,
        @RequestParam(required = false) excludeStatus: List<StatusRequest>?,
        @RequestParam(required = false) @Min(1) page: Int?, @RequestParam(required = false) @Min(1) @Max(500) pageSize: Int?
    ): PagedResponse<EventResponse> {

        if (start != null && end != null) {
            if (start.isAfter(end)) {
                throw BadRequestException("start must be before end", mapOf("field" to "start"))
            }
        }

        if (spid != null) {
            if (latitude != null || longitude != null || radius != null || !countryCode.isNullOrEmpty()) {
                throw BadRequestException("latitude, longitude, radius and countryCodes should not be provided when filtering by spid")
            }
        } else {
            if (!countryCode.isNullOrEmpty() && (latitude != null || longitude != null || radius != null)) {
                throw BadRequestException("latitude, longitude and radius should not be provided when filtering by countryCodes")
            }

            if (countryCode.isNullOrEmpty() && (latitude == null || longitude == null || radius == null)) {
                throw BadRequestException("latitude, longitude and radius are required when not filtering by countryCodes")
            }
        }

        if (createdStart != null && createdEnd != null) {
            if (createdStart.isAfter(createdEnd)) {
                throw BadRequestException("createdStart must be before createdEnd", mapOf("field" to "createdStart"))
            }
        }

        val response = eventsService.getEvents(
            spid,
            latitude = latitude,
            longitude = longitude,
            radius = radius,
            start = start,
            end = end,
            createdStart = createdStart,
            createdEnd = createdEnd,
            categories = category ?: emptyList(),
            excludeCategories = excludeCategory ?: emptyList(),
            statuses = status ?: emptyList(),
            excludeStatuses = excludeStatus ?: emptyList(),
            countryCodes = countryCode ?: emptyList(),
            pageRequest = PageRequest(page ?: 1, pageSize ?: 200)
        )

        return PagedResponse(
            items = response.content.map { it.toResponse() },
            page = response.page,
            pages = response.totalPages,
            total = response.totalElements
        )
    }

    @GetMapping("/api/events/{eventId}")
    @PreAuthorize("hasPermission(null, 'events', 'read')")
    @Operation(summary = "Requires events:read permission")
    fun getEvent(@PathVariable eventId: String): EventResponse {

        val event = eventsService.getEvent(eventId) ?: throw NotFoundException("Event not found", mapOf("eventId" to eventId))

        return event.toResponse()
    }

    @PostMapping("/api/events")
    @PreAuthorize("hasPermission(null, 'events', 'read')")
    @Operation(summary = "Requires events:read permission")
    fun getEventsByIds(@Valid @RequestBody eventsByIdsRequest: GetEventsByIdsRequest): List<EventResponse> {
        val events = eventsService.getEvents(eventsByIdsRequest.eventIds!!)
        return events.map { it.toResponse() }
    }

    private fun PredictHqEvent.toResponse() = EventResponse(
        eventId = this.eventId,
        createdDate = this.createdDate,
        updatedDate = this.updatedDate,
        title = this.title,
        category = this.category.toResponse(),
        eventStart = this.eventStart,
        eventEnd = this.eventEnd,
        predictedEnd = this.predictedEnd,
        timezone = this.timezone,
        entities = this.entities.map { it.toResponse() },
        scope = this.scope?.toResponse(),
        countryCode = this.countryCode,
        phqAttendance = this.phqAttendance,
        phqRank = this.phqRank,
        localRank = this.localRank,
        aviationRank = this.aviationRank,
        status = this.status.toResponse(),
        brandSafe = this.brandSafe,
        cancelledDate = this.cancelledDate,
        postponedDate = this.postponedDate,
        predictedEventSpendAccommodation = this.predictedEventSpendAccommodation,
        predictedEventSpendHospitality = this.predictedEventSpendHospitality,
        predictedEventSpendTransportation = this.predictedEventSpendTransportation,
        lon = this.lon,
        lat = this.lat,
        impactPatterns = this.impactPatterns.map { it.toResponse() }
    )

    private fun PredictHqEvent.Category.toResponse() = EventResponse.Category.valueOf(this.name)

    private fun PredictHqEvent.Entity.toResponse() = EventResponse.Entity(
        entityId = this.entityId,
        category = this.category?.toResponse(),
        description = this.description,
        labels = this.labels,
        formattedAddress = this.formattedAddress,
        name = this.name,
        timezone = this.timezone,
        recurring = this.recurring?.let { EventResponse.Entity.Recurring(ical = it.ical) },
        type = this.type.toResponse()
    )

    private fun PredictHqEvent.Entity.Type.toResponse() = EventResponse.Entity.Type.valueOf(this.name)

    private fun PredictHqEvent.Scope.toResponse() = EventResponse.Scope.valueOf(this.name)

    private fun PredictHqEvent.Status.toResponse() = EventResponse.Status.valueOf(this.name)

    private fun PredictHqEvent.ImpactPattern.toResponse() = EventResponse.ImpactPattern(
        impactType = this.impactType.toResponse(),
        impacts = this.impacts.map { it.toResponse() },
        vertical = this.vertical.toResponse()
    )

    private fun PredictHqEvent.ImpactPattern.ImpactType.toResponse() = EventResponse.ImpactPattern.ImpactType.valueOf(this.name)

    private fun PredictHqEvent.ImpactPattern.Impact.toResponse() = EventResponse.ImpactPattern.Impact(
        dateLocal = this.dateLocal,
        position = this.position.toResponse(),
        value = this.value
    )

    private fun PredictHqEvent.ImpactPattern.Impact.Position.toResponse() = EventResponse.ImpactPattern.Impact.Position.valueOf(this.name)

    private fun PredictHqEvent.ImpactPattern.Vertical.toResponse() = EventResponse.ImpactPattern.Vertical.valueOf(this.name)
}

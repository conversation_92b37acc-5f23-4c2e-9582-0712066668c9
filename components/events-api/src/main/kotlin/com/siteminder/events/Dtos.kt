package com.siteminder.events

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime

data class PredictHqEvent(
    val eventId: String,
    val createdDate: ZonedDateTime,
    val updatedDate: ZonedDateTime,
    val title: String,
    val category: Category,
    val description: String?,
    val eventStart: ZonedDateTime,
    val eventEnd: ZonedDateTime,
    val predictedEnd: ZonedDateTime?,
    val timezone: String?,
    val entities: List<Entity>,
    val scope: Scope?,
    val countryCode: String?,
    val phqAttendance: Long?,
    val phqRank: Long,
    val localRank: Long?,
    val aviationRank: Long?,
    val status: Status,
    val brandSafe: Boolean,
    val cancelledDate: ZonedDateTime?,
    val postponedDate: ZonedDateTime?,
    val predictedEventSpendAccommodation: Long?,
    val predictedEventSpendHospitality: Long?,
    val predictedEventSpendTransportation: Long?,
    val lon: BigDecimal,
    val lat: BigDecimal,
    val impactPatterns: List<ImpactPattern>
) {

    enum class Category(@get:JsonValue val value: String) {
        observances("observances"),
        public_holidays("public-holidays"),
        concerts("concerts"),
        sports("sports"),
        community("community"),
        expos("expos"),
        performing_arts("performing-arts"),
        conferences("conferences"),
        school_holidays("school-holidays"),
        festivals("festivals")

        // TODO MW: Validate upstream to ensure all categories are valid before it gets to the app.
    }

    data class Entity(
        @field:JsonProperty("entity_id")
        val entityId: String,
        val category: Category?,
        val description: String?,
        val labels: List<String>?,
        @field:JsonProperty("formatted_address")
        val formattedAddress: String?,
        val name: String,
        val timezone: String?,
        val recurring: Recurring?,
        val type: Type,
    ) {
        data class Recurring(
            val ical: String
        )

        enum class Type(@get:JsonValue val value: String) {
            event_group("event-group"),
            venue("venue")
        }
    }

    enum class Scope {
        locality, localadmin, county, region, country // TODO MW: Validate upstream to ensure all scopes are valid before it gets to the app.
    }

    enum class Status {
        active, postponed, cancelled, predicted, archived // TODO MW: Validate upstream to ensure all statuses are valid before it gets to the app.
    }

    data class ImpactPattern(
        @field:JsonProperty("impact_type")
        val impactType: ImpactType,
        val impacts: List<Impact>,
        val vertical: Vertical
    ) {

        enum class ImpactType {
            phq_rank, phq_attendance
        }

        data class Impact(
            @field:JsonProperty("date_local")
            val dateLocal: LocalDate,
            val position: Position,
            val value: Int
        ) {

            enum class Position {
                leading, event_day, lagging
            }
        }

        enum class Vertical {
            retail, accommodation, hospitality
        }
    }
}

package com.siteminder.events.domain

import com.siteminder.events.PredictHqEvent
import java.time.ZonedDateTime

data class PredictHqEventSearchParams(
    var latitude: Double? = null,
    var longitude: Double? = null,
    var radius: Double? = null,
    val start: ZonedDateTime? = null,
    val end: ZonedDateTime? = null,
    val createdStart: ZonedDateTime? = null,
    val createdEnd: ZonedDateTime? = null,
    val includeCategories: List<PredictHqEvent.Category> = emptyList(),
    val excludeCategories: List<PredictHqEvent.Category> = emptyList(),
    val includeStatuses: List<PredictHqEvent.Status> = emptyList(),
    val excludeStatuses: List<PredictHqEvent.Status> = emptyList(),
    var includeCountryCodes: List<String> = emptyList(),
    val excludeCountryCodes: List<String> = emptyList()
)

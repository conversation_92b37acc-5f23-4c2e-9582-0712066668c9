package com.siteminder

import com.fasterxml.jackson.databind.ObjectMapper
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.events.CategoryRequest
import com.siteminder.events.EventsService
import com.siteminder.events.domain.PredictHqEventRepository
import com.siteminder.hotel.HotelApiClient
import com.siteminder.hotel.HotelApiProperties
import com.zaxxer.hikari.HikariDataSource
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.convert.converter.Converter
import org.springframework.format.FormatterRegistry
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import javax.sql.DataSource


@Configuration
@EnableConfigurationProperties(HotelApiProperties::class)
class ApplicationConfiguration : WebMvcConfigurer {

    @Bean
    fun apiAutoConfigurationProperties(@Value("\${system.jwt-secret}") secret: String): ApiAutoConfigurationProperties {
        return ApiAutoConfigurationProperties("predicthq", "events-api", secret, "/api/**")
    }

    @Bean
    fun eventsService(predictHqEventRepository: PredictHqEventRepository, hotelApiClient: HotelApiClient) = EventsService(predictHqEventRepository, hotelApiClient)

    @Bean
    fun predictHqEventRepository(databricksNamedParameterJdbcTemplate: NamedParameterJdbcTemplate, objectMapper: ObjectMapper) = PredictHqEventRepository(databricksNamedParameterJdbcTemplate, objectMapper)

    @Bean
    @ConfigurationProperties("spring.datasource.databricks")
    fun databricksDataSourceProperties() = DataSourceProperties()

    @Bean
    fun databricksDataSource(
        @Value("\${spring.datasource.databricks.maximum-pool-size:10}") poolSize: Int,
        databricksDataSourceProperties: DataSourceProperties
    ) = (databricksDataSourceProperties.initializeDataSourceBuilder().build() as HikariDataSource).apply {
        poolName = "databricks"
        maximumPoolSize = poolSize
    }

    @Bean
    fun databricksNamedParameterJdbcTemplate(databricksDataSource: DataSource) = NamedParameterJdbcTemplate(databricksDataSource)

    // Handle kebab-case enum values
    override fun addFormatters(registry: FormatterRegistry) {

        val converter = object : Converter<String, CategoryRequest> {
            override fun convert(source: String): CategoryRequest {
                return CategoryRequest.values().find { it.value == source } ?: throw IllegalArgumentException("Invalid category value: $source")
            }
        }

        registry.addConverter(converter)
    }

}

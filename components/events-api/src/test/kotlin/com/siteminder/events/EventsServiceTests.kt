package com.siteminder.events

import io.kotlintest.shouldBe
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest
class EventsServiceTests {

    @Autowired
    lateinit var eventsService: EventsService

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvents() returns expected result`() {

        val latitude = -37.840935
        val longitude = 144.946457
        val radius = 5.0

        val pageEvents = eventsService.getEvents(
            spid = "123",
            latitude = latitude,
            longitude = longitude,
            radius = radius,
            start = null, end = null,
            createdStart = null, createdEnd = null,
            categories = emptyList(),
            excludeCategories = emptyList(),
            statuses = emptyList(),
            excludeStatuses = emptyList(),
            countryCodes = emptyList(),
            pageRequest = PageRequest(1, 1000)
        )

        pageEvents.content.size shouldBe 757

        pageEvents.content[0].eventId shouldBe "EVT-0000"
    }
}

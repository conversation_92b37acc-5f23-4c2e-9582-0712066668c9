package com.siteminder.events

import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.api.TraceTokenContextExtension
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.metrics.ResetMetricsExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@SpringBootTest
@AutoConfigureMockMvc
@ExtendWith(ResetMetricsExtension::class, TraceTokenContextExtension::class)
class EventsControllerGetEventsByIdsTests {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("events:read")
            .sign()
    }

    @Test
    fun `getEventsByIds should return 401 when access token is missing`() {
        val getEventsByIdsRequest = """
            {
              "eventIds": ["gbkHHCS5oMBC9B6bbP"]
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/events", jwt = null,getEventsByIdsRequest))
            .andExpect(status().isUnauthorized)
            .andExpect(content().string("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `getEventsByIds should return 401 when access token is invalid`() {
        val getEventsByIdsRequest = """
            {
              "eventIds": ["gbkHHCS5oMBC9B6bbP"]
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/events", "invalid-jwt", getEventsByIdsRequest))
            .andExpect(status().isUnauthorized)
            .andExpect(content().string("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }


    @Test
    fun `getEventsByIds should return 403 when access token has insufficient permissions`() {
        val getEventsByIdsRequest = """
            {
              "eventIds": ["gbkHHCS5oMBC9B6bbP"]
            }
        """.trimIndent()

        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("events:something-else")
            .sign()

        mockMvc.perform(smPost("/api/events", jwt = invalidJwt,getEventsByIdsRequest))
            .andExpect(status().isForbidden)
            .andExpect(content().string("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `getEventsByIds should return 400 when eventIds is missing`() {
        val getEventsByIdsRequest = """
            {
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/events", jwt, getEventsByIdsRequest))
            .andExpect(status().isBadRequest)
            .andExpect(content().string("""{"errors":[{"code":"NOT_EMPTY","message":"must not be empty","meta":{"field":"eventIds"}}]}"""))
    }

    @Test
    fun `getEventsByIds should return 400 when eventIds is empty`() {
        val getEventsByIdsRequest = """
            {
              "eventIds": []
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/events", jwt, getEventsByIdsRequest))
            .andExpect(status().isBadRequest)
            .andExpect(content().json("""{"errors":[{"code":"INVALID","message":"size must be between 1 and 5000","meta":{"field":"eventIds"}},{"code":"NOT_EMPTY","message":"must not be empty","meta":{"field":"eventIds"}}]}"""))
    }

    @Test
    fun `getEventsByIds should return 400 when eventIds contains more than 1000 items`() {
        val eventIds = (1..5001).joinToString(",") { "\"eventId-$it\"" }

        val getEventsByIdsRequest = """
            {
              "eventIds": [$eventIds]
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/events", jwt, getEventsByIdsRequest))
            .andExpect(status().isBadRequest)
            .andExpect(content().string("""{"errors":[{"code":"INVALID","message":"size must be between 1 and 5000","meta":{"field":"eventIds"}}]}"""))
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEventsByIds should return 200 with valid response`() {
        val getEventsByIdsRequest = """
            {
              "eventIds": ["7QgQfuPZkkjJSj8284", "gbkHHCS5oMBC9B6bbP"]
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/events", jwt, getEventsByIdsRequest))
            .andExpect(status().isOk)
            .andExpect(content().json("""
                [
                  {
                    "eventId": "7QgQfuPZkkjJSj8284",
                    "createdDate": "2018-02-28T14:54:17Z",
                    "updatedDate": "2023-10-31T02:31:32.4Z",
                    "title": "Kevin Hart",
                    "category": "performing-arts",
                    "eventStart": "2018-12-05T21:30:00Z",
                    "eventEnd": "2018-12-05T21:30:00Z",
                    "predictedEnd": "2018-12-05T23:20:00Z",
                    "timezone": "Australia/Melbourne",
                    "entities": [
                      {
                        "entityId": "382DYpw3czabQLF8BmuJixP",
                        "formattedAddress": "Olympic Boulevard\nMelbourne VIC 3001\nAustralia",
                        "name": "Rod Laver Arena",
                        "type": "venue"
                      }
                    ],
                    "scope": "locality",
                    "countryCode": "AU",
                    "phqAttendance": 13487,
                    "phqRank": 73,
                    "localRank": 85,
                    "aviationRank": 0,
                    "status": "active",
                    "brandSafe": true,
                    "predictedEventSpendAccommodation": 52947,
                    "predictedEventSpendHospitality": 196828,
                    "predictedEventSpendTransportation": 128634,
                    "lon": 144.9785584,
                    "lat": -37.8216161,
                    "impactPatterns": []
                  },
                  {
                    "eventId": "gbkHHCS5oMBC9B6bbP",
                    "createdDate": "2018-04-05T04:28:57Z",
                    "updatedDate": "2024-03-14T23:43:20.439Z",
                    "title": "Michael McIntyre",
                    "category": "performing-arts",
                    "eventStart": "2019-03-21T22:00:00Z",
                    "eventEnd": "2019-03-21T22:00:00Z",
                    "predictedEnd": "2019-03-21T23:50:00Z",
                    "timezone": "Australia/Melbourne",
                    "entities": [
                      {
                        "entityId": "382DYpw3czabQLF8BmuJixP",
                        "formattedAddress": "Olympic Boulevard\nMelbourne VIC 3001\nAustralia",
                        "name": "Rod Laver Arena",
                        "type": "venue"
                      }
                    ],
                    "scope": "locality",
                    "countryCode": "AU",
                    "phqAttendance": 12608,
                    "phqRank": 72,
                    "localRank": 85,
                    "aviationRank": 0,
                    "status": "active",
                    "brandSafe": true,
                    "predictedEventSpendAccommodation": 49616,
                    "predictedEventSpendHospitality": 184445,
                    "predictedEventSpendTransportation": 120541,
                    "lon": 144.9785584,
                    "lat": -37.8216161,
                    "impactPatterns": [
                      {
                        "impactType": "phq_attendance",
                        "impacts": [
                          {
                            "dateLocal": "2019-03-21",
                            "position": "leading",
                            "value": 1892
                          },
                          {
                            "dateLocal": "2019-03-22",
                            "position": "event_day",
                            "value": 12608
                          },
                          {
                            "dateLocal": "2019-03-23",
                            "position": "lagging",
                            "value": 1261
                          }
                        ],
                        "vertical": "accommodation"
                      },
                      {
                        "impactType": "phq_attendance",
                        "impacts": [
                          {
                            "dateLocal": "2019-03-21",
                            "position": "leading",
                            "value": 4413
                          },
                          {
                            "dateLocal": "2019-03-22",
                            "position": "event_day",
                            "value": 12608
                          },
                          {
                            "dateLocal": "2019-03-23",
                            "position": "lagging",
                            "value": 1640
                          }
                        ],
                        "vertical": "hospitality"
                      }
                    ]
                  }
                ]
            """.trimIndent()))
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEventsByIds should return 200 with empty array if events not found`() {
        val getEventsByIdsRequest = """
            {
              "eventIds": ["invalid-event-id", "another-invalid-event-id"]
            }
        """.trimIndent()

        mockMvc.perform(smPost("/api/events", jwt, getEventsByIdsRequest))
            .andExpect(status().isOk)
            .andExpect(content().json("""
                []
            """.trimIndent()))
    }

    private fun smPost(url: String, jwt: String?, body: String): MockHttpServletRequestBuilder {
        val requestBuilder = MockMvcRequestBuilders.post(url)
            .header("X-SM-TRACE-TOKEN", "trace-token-123")
            .contentType(MediaType.APPLICATION_JSON)
            .content(body)

        if (jwt != null) {
            requestBuilder.header(HttpHeaders.AUTHORIZATION, "Bearer $jwt")
        }

        return requestBuilder
    }
}

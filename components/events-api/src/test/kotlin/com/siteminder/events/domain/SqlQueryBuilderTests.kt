package com.siteminder.events.domain

import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.Test

class SqlQueryBuilderTests {

    @Test
    fun `constructor should reject blank table`() {

        val e = shouldThrow<IllegalArgumentException> {
            SqlQueryBuilder(table = "", columns = listOf("col1", "col2"))
        }

        e.message shouldBe "Table cannot be blank"
    }

    @Test
    fun `constructor should reject empty columns`() {

        val e = shouldThrow<IllegalArgumentException> {
            SqlQueryBuilder(table = "test_table", columns = emptyList())
        }

        e.message shouldBe "Columns cannot be empty"
    }

    @Test
    fun `generated query should match expected when no filters or pagination`() {

        val builder = SqlQueryBuilder(table = "test_table", columns = listOf("col1", "col2"))

        builder.buildQuery() shouldBe """
            SELECT
              col1,
              col2
            FROM test_table
        """.trimIndent()
        builder.buildCountQuery() shouldBe "SELECT COUNT(*) FROM test_table"
        builder.params shouldBe emptyMap()
    }

    @Test
    fun `generated query should match expected with single row`() {

        val builder = SqlQueryBuilder(table = "test_table", columns = listOf("col1", "col2"))
            .singleRow()

        builder.buildQuery() shouldBe """
            SELECT
              col1,
              col2
            FROM test_table
            LIMIT 1
        """.trimIndent()
        builder.buildCountQuery() shouldBe "SELECT COUNT(*) FROM test_table"
        builder.params shouldBe emptyMap()
    }

    @Test
    fun `generated query should match expected with pagination`() {

        val builder = SqlQueryBuilder(table = "test_table", columns = listOf("col1", "col2"))
            .paginate(offset = 1, limit = 10)

        builder.buildQuery() shouldBe """
            SELECT
              col1,
              col2
            FROM test_table
            LIMIT 10 OFFSET 1
        """.trimIndent()
        builder.buildCountQuery() shouldBe "SELECT COUNT(*) FROM test_table"
        builder.params shouldBe emptyMap()
    }

    @Test
    fun `paginate should reject negative offset`() {

        val builder = SqlQueryBuilder(table = "test_table", columns = listOf("col1", "col2"))

        val e = shouldThrow<IllegalArgumentException> {
            builder.paginate(offset = -1, limit = 10)
        }

        e.message shouldBe "Offset must be greater than or equal to 0"
    }

    @Test
    fun `paginate should reject zero limit`() {

        val builder = SqlQueryBuilder(table = "test_table", columns = listOf("col1", "col2"))

        val e = shouldThrow<IllegalArgumentException> {
            builder.paginate(offset = 1, limit = 0)
        }

        e.message shouldBe "Limit must be greater than 0"
    }

    @Test
    fun `generated query should match expected with filters`() {

        val builder = SqlQueryBuilder(table = "test_table", columns = listOf("col1", "col2", "col3"))
            .filter("col1 = 'value1'")
            .filter("col2 = :col2", "col2" to "value2")
            .filter("col3 = :col3", mapOf("col3" to "value3"))

        builder.buildQuery() shouldBe """
            SELECT
              col1,
              col2,
              col3
            FROM test_table
            WHERE col1 = 'value1' AND col2 = :col2 AND col3 = :col3
        """.trimIndent()
        builder.buildCountQuery() shouldBe "SELECT COUNT(*) FROM test_table WHERE col1 = 'value1' AND col2 = :col2 AND col3 = :col3"
        builder.params shouldBe mapOf("col2" to "value2", "col3" to "value3")
    }

    @Test
    fun `generated query should match expected with filters and pagination`() {

        val builder = SqlQueryBuilder(table = "test_table", columns = listOf("col1", "col2", "col3"))
            .filter("col1 = 'value1'")
            .filter("col2 = :col2", "col2" to "value2")
            .filter("col3 = :col3", mapOf("col3" to "value3"))
            .paginate(offset = 1, limit = 10)

        builder.buildQuery() shouldBe """
            SELECT
              col1,
              col2,
              col3
            FROM test_table
            WHERE col1 = 'value1' AND col2 = :col2 AND col3 = :col3
            LIMIT 10 OFFSET 1
        """.trimIndent()
        builder.buildCountQuery() shouldBe "SELECT COUNT(*) FROM test_table WHERE col1 = 'value1' AND col2 = :col2 AND col3 = :col3"
        builder.params shouldBe mapOf("col2" to "value2", "col3" to "value3")
    }
}

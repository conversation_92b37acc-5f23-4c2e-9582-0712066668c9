package com.siteminder.events.domain

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.events.Page
import com.siteminder.events.PageRequest
import com.siteminder.events.PredictHqEvent
import io.kotlintest.shouldBe
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import java.time.ZonedDateTime
import java.util.*

class PredictHqEventRepositoryTests {

    @Test
    fun `getEventsWithRadiusMultiplier should generate expected query string with params`() {

        val jdbcTemplate = mock<NamedParameterJdbcTemplate>()

        val sqlArgCaptor = argumentCaptor<String>()
        val paramsArgCaptor = argumentCaptor<Map<String, Any?>>()

        whenever(jdbcTemplate.query(sqlArgCaptor.capture(), paramsArgCaptor.capture(), any<RowMapper<Any>>())).thenReturn(emptyList())

        val repository = PredictHqEventRepository(jdbcTemplate, jacksonObjectMapper())

        val searchParams = PredictHqEventSearchParams(
            latitude = -33.8688,
            longitude = 151.2093,
            radius = 10.0,
            includeCountryCodes = listOf("AU")
        )

        val events = repository.getEventsWithRadiusMultiplier(searchParams, PageRequest(1, 10))

        events shouldBe Page(
            content = emptyList(),
            page = 1,
            totalPages = 0,
            totalElements = 0
        )

        sqlArgCaptor.allValues.size shouldBe 1
        paramsArgCaptor.allValues.size shouldBe 1

        sqlArgCaptor.firstValue shouldBe """
            SELECT
              EVENT_ID,
              CREATE_DT,
              UPDATE_DT,
              TITLE,
              CATEGORY,
              DESCRIPTION,
              EVENT_START_DT,
              EVENT_END_DT,
              PREDICTED_END_DT,
              TIMEZONE,
              ENTITIES,
              SCOPE,
              COUNTRY_CODE,
              PHQ_ATTENDANCE,
              PHQ_RANK,
              LOCAL_RANK,
              AVIATION_RANK,
              STATUS,
              BRAND_SAFE,
              CANCELLED_DT,
              POSTPONED_DT,
              PREDICTED_EVENT_SPEND_ACCOMMODATION,
              PREDICTED_EVENT_SPEND_HOSPITALITY,
              PREDICTED_EVENT_SPEND_TRANSPORTATION,
              LON,
              LAT,
              IMPACT_PATTERNS
            FROM events
            WHERE LAT IS NOT NULL AND LON IS NOT NULL AND (
              ((PHQ_RANK > 70 OR LOCAL_RANK > 80) AND (111.111 *
                DEGREES(ACOS(LEAST(1.0, COS(RADIANS(:latitude))
                  * COS(RADIANS(LAT))
                  * COS(RADIANS(:longitude - LON))
                  + SIN(RADIANS(:latitude))
                  * SIN(RADIANS(LAT)))))) < :radius)
              OR ((LOCAL_RANK >= 95) AND (111.111 *
                DEGREES(ACOS(LEAST(1.0, COS(RADIANS(:latitude))
                  * COS(RADIANS(LAT))
                  * COS(RADIANS(:longitude - LON))
                  + SIN(RADIANS(:latitude))
                  * SIN(RADIANS(LAT)))))) < (:radius * 3))
              OR ((LOCAL_RANK >= 99) AND (111.111 *
                DEGREES(ACOS(LEAST(1.0, COS(RADIANS(:latitude))
                  * COS(RADIANS(LAT))
                  * COS(RADIANS(:longitude - LON))
                  + SIN(RADIANS(:latitude))
                  * SIN(RADIANS(LAT)))))) < (:radius * 5))
            ) AND COUNTRY_CODE IN (:includeCountryCodes)
            LIMIT 10 OFFSET 0
        """.trimIndent()

        paramsArgCaptor.firstValue shouldBe mapOf(
            "latitude" to -33.8688,
            "longitude" to 151.2093,
            "radius" to 10.0,
            "includeCountryCodes" to listOf("AU")
        )
    }

    @Test
    fun `getEvents should generate expected query string with params`() {

        val jdbcTemplate = mock<NamedParameterJdbcTemplate>()

        val sqlArgCaptor = argumentCaptor<String>()
        val paramsArgCaptor = argumentCaptor<Map<String, Any?>>()

        whenever(jdbcTemplate.query(sqlArgCaptor.capture(), paramsArgCaptor.capture(), any<RowMapper<Any>>())).thenReturn(emptyList())

        val repository = PredictHqEventRepository(jdbcTemplate, jacksonObjectMapper())

        val start = ZonedDateTime.now()

        val searchParams = PredictHqEventSearchParams(
            start = start,
            end = start.plusDays(10),
            includeCategories = listOf(PredictHqEvent.Category.sports),
            includeStatuses = listOf(PredictHqEvent.Status.active),
            excludeCountryCodes = listOf("NZ")
        )

        val events = repository.getEvents(searchParams, PageRequest(1, 10))

        events shouldBe Page(
            content = emptyList(),
            page = 1,
            totalPages = 0,
            totalElements = 0
        )

        sqlArgCaptor.allValues.size shouldBe 1
        paramsArgCaptor.allValues.size shouldBe 1

        sqlArgCaptor.firstValue shouldBe """
            SELECT
              EVENT_ID,
              CREATE_DT,
              UPDATE_DT,
              TITLE,
              CATEGORY,
              DESCRIPTION,
              EVENT_START_DT,
              EVENT_END_DT,
              PREDICTED_END_DT,
              TIMEZONE,
              ENTITIES,
              SCOPE,
              COUNTRY_CODE,
              PHQ_ATTENDANCE,
              PHQ_RANK,
              LOCAL_RANK,
              AVIATION_RANK,
              STATUS,
              BRAND_SAFE,
              CANCELLED_DT,
              POSTPONED_DT,
              PREDICTED_EVENT_SPEND_ACCOMMODATION,
              PREDICTED_EVENT_SPEND_HOSPITALITY,
              PREDICTED_EVENT_SPEND_TRANSPORTATION,
              LON,
              LAT,
              IMPACT_PATTERNS
            FROM events
            WHERE EVENT_END_DT >= :eventStart AND EVENT_START_DT <= :eventEnd AND CATEGORY IN (:includeCategories) AND STATUS IN (:includeStatuses) AND COUNTRY_CODE NOT IN (:excludeCountryCodes)
            LIMIT 10 OFFSET 0
        """.trimIndent()

        paramsArgCaptor.firstValue shouldBe mapOf(
            "eventStart" to start.toInstant().let(Date::from),
            "eventEnd" to start.plusDays(10).toInstant().let(Date::from),
            "includeCategories" to listOf("sports"),
            "includeStatuses" to listOf("active"),
            "excludeCountryCodes" to listOf("NZ")
        )
    }

    @Test
    fun `getEvent should generate expected query string with params`() {

        val jdbcTemplate = mock<NamedParameterJdbcTemplate>()

        val sqlArgCaptor = argumentCaptor<String>()
        val paramsArgCaptor = argumentCaptor<Map<String, Any?>>()

        whenever(jdbcTemplate.query(sqlArgCaptor.capture(), paramsArgCaptor.capture(), any<RowMapper<Any>>())).thenReturn(emptyList())

        val repository = PredictHqEventRepository(jdbcTemplate, jacksonObjectMapper())

        val eventId = "event-id"

        val event = repository.getEvent(eventId)

        event shouldBe null

        sqlArgCaptor.allValues.size shouldBe 1
        paramsArgCaptor.allValues.size shouldBe 1

        sqlArgCaptor.firstValue shouldBe """
            SELECT
              EVENT_ID,
              CREATE_DT,
              UPDATE_DT,
              TITLE,
              CATEGORY,
              DESCRIPTION,
              EVENT_START_DT,
              EVENT_END_DT,
              PREDICTED_END_DT,
              TIMEZONE,
              ENTITIES,
              SCOPE,
              COUNTRY_CODE,
              PHQ_ATTENDANCE,
              PHQ_RANK,
              LOCAL_RANK,
              AVIATION_RANK,
              STATUS,
              BRAND_SAFE,
              CANCELLED_DT,
              POSTPONED_DT,
              PREDICTED_EVENT_SPEND_ACCOMMODATION,
              PREDICTED_EVENT_SPEND_HOSPITALITY,
              PREDICTED_EVENT_SPEND_TRANSPORTATION,
              LON,
              LAT,
              IMPACT_PATTERNS
            FROM events
            WHERE EVENT_ID = :eventId AND STATUS IN ('active', 'postponed', 'cancelled')
            LIMIT 1
        """.trimIndent()

        paramsArgCaptor.firstValue shouldBe mapOf("eventId" to eventId)
    }
}

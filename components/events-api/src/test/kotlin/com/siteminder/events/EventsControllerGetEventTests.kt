package com.siteminder.events

import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.api.ApiConstants
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.metrics.ResetMetricsExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@SpringBootTest
@AutoConfigureMockMvc
@ExtendWith(ResetMetricsExtension::class)
class EventsControllerGetEventTests {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("events:read")
            .sign()
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvent should return 200 with valid response`() {
        mockMvc.perform(smGet("/api/events/gbkHHCS5oMBC9B6bbP"))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                {
                  "eventId":"gbkHHCS5oMBC9B6bbP",
                  "createdDate":"2018-04-05T14:28:57Z",
                  "updatedDate":"2023-12-31T23:53:54.311Z",
                  "title":"Michael McIntyre",
                  "category":"performing-arts",
                  "eventStart":"2019-03-22T09:00:00Z",
                  "eventEnd":"2019-03-22T09:00:00Z",
                  "predictedEnd":"2019-03-22T10:50:00Z",
                  "timezone":"Australia/Melbourne",
                  "entities":[
                    {
                      "entityId":"382DYpw3czabQLF8BmuJixP",
                      "formattedAddress":"Olympic Boulevard\nMelbourne VIC 3001\nAustralia",
                      "name":"Rod Laver Arena",
                      "type":"venue"
                    }
                  ],
                  "scope":"locality",
                  "countryCode":"AU",
                  "phqAttendance":12608,
                  "phqRank":72,
                  "localRank":85,
                  "aviationRank":0,
                  "status":"active",
                  "predictedEventSpendAccommodation":49616,
                  "predictedEventSpendHospitality":184445,
                  "predictedEventSpendTransportation":120541,
                  "lon":144.9785584,
                  "lat":-37.8216161,
                  "impactPatterns":[
                    {
                      "impactType":"phq_attendance",
                      "impacts":[
                        {
                          "dateLocal":"2019-03-21",
                          "position":"leading",
                          "value":1892
                        },
                        {
                          "dateLocal":"2019-03-22",
                          "position":"event_day",
                          "value":12608
                        },
                        {
                          "dateLocal":"2019-03-23",
                          "position":"lagging",
                          "value":1261
                        }
                      ],
                      "vertical":"accommodation"
                    },
                    {
                      "impactType":"phq_attendance",
                      "impacts":[
                        {
                          "dateLocal":"2019-03-21",
                          "position":"leading",
                          "value":4413
                        },
                        {
                          "dateLocal":"2019-03-22",
                          "position":"event_day",
                          "value":12608
                        },
                        {
                          "dateLocal":"2019-03-23",
                          "position":"lagging",
                          "value":1640
                        }
                      ],
                      "vertical":"hospitality"
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvent should return 401 when access token is invalid`() {
        mockMvc.perform(smGet("/api/events/xxx", jwt = "invalid_token"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `getEvent should return 403 when jwt has insufficient permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()

        mockMvc.perform(smGet("/api/events/xxx", invalidJwt))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvent should return 404 when event is not found`() {
        mockMvc.perform(smGet("/api/events/xxx"))
            .andExpect(status().isNotFound)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"NOT_FOUND",
                      "message":"Event not found",
                      "meta":{"eventId":"xxx"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvent should return 401 when access token is missing`() {
        mockMvc.perform(smGet("/api/events/xxx", jwt = null))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    private fun smGet(url: String, jwt: String? = this.jwt): MockHttpServletRequestBuilder {
        val requestBuilder = MockMvcRequestBuilders.get(url)
            .header(ApiConstants.SM_TRACE_TOKEN_HEADER, "trace-token-123")
            .contentType(MediaType.APPLICATION_JSON)

        if (jwt != null) {
            requestBuilder.header(HttpHeaders.AUTHORIZATION, "Bearer $jwt")
        }

        return requestBuilder
    }
}

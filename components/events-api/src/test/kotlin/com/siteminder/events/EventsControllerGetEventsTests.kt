package com.siteminder.events

import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.api.ApiConstants
import com.siteminder.api.TraceTokenContextExtension
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.metrics.ResetMetricsExtension
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.matchers.string.shouldContain
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@SpringBootTest
@AutoConfigureMockMvc
@ExtendWith(ResetMetricsExtension::class, MockServerExtension::class, TraceTokenContextExtension::class)
class EventsControllerGetEventsTests {

    @Autowired
    private lateinit var mockMvc: MockMvc
    private lateinit var mockServerClient: MockServerClient

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("events:read")
            .sign()
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvents should return 200 with valid response for retrieving holidays`() {
        mockMvc.perform(smGet("/api/events?countryCode=PH&category=public-holidays,school-holidays&status=active&start=2024-01-01T00:00:00.726597Z&end=2026-05-27T00:38:42.726597Z"))
            .andExpect(status().isOk)
            .andExpect { result ->
                val response = result.response.contentAsString
                response shouldContain "\"total\":42"
            }
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvents should return 200 with valid response provided spid`() {
        val spid = "455933a0-8114-11e5-8827-02b1347ffa5b"
        mockServerClient.`when`(apiRequest(path = "/api/hotels/$spid")).respond(
            apiResponse(
                body = """
                {
                  "spid": "$spid",
                  "latitude": -33.8689927,
                  "longitude": 151.2202741,
                  "suggestedRadius": 3.870,
                  "countryCode": "AU",
                  "createdAt": "2021-08-31T00:00:00Z",
                  "updatedAt": "2021-08-31T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        mockMvc.perform(smGet("/api/events?spid=$spid&excludeCategory=observances,public-holidays,school-holidays&status=active&start=2024-05-06T00:00:00.726597Z&end=2025-05-27T00:38:42.726597Z"))
            .andExpect(status().isOk)
            .andExpect { result ->
                val response = result.response.contentAsString
                response shouldContain "\"total\":42"
            }
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvents should return 200 with valid response payload`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0"))
            .andExpect(status().isOk)
            .andExpect { result ->
                val response = result.response.contentAsString
                response shouldContain "\"total\":42"
            }
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvents for category should return 200 with valid response payload`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&category=performing-arts"))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                  [
                    {
                      "eventId":"7QgQfuPZkkjJSj8284",
                      "createdDate":"2018-03-01T01:54:17Z",
                      "updatedDate":"2023-10-31T13:31:32.4Z",
                      "title":"Kevin Hart",
                      "category":"performing-arts",
                      "eventStart":"2018-12-06T08:30:00Z",
                      "eventEnd":"2018-12-06T08:30:00Z",
                      "timezone":"Australia/Melbourne",
                      "scope":"locality",
                      "countryCode":"AU",
                      "phqRank":73,
                      "localRank":85,
                      "aviationRank":0,
                      "status":"active",
                      "lon":144.9785584,
                      "lat":-37.8216161
                    },
                    {
                      "eventId":"gbkHHCS5oMBC9B6bbP",
                      "createdDate":"2018-04-05T14:28:57Z",
                      "updatedDate":"2023-12-31T23:53:54.311Z",
                      "title":"Michael McIntyre",
                      "category":"performing-arts",
                      "eventStart":"2019-03-22T09:00:00Z",
                      "eventEnd":"2019-03-22T09:00:00Z",
                      "timezone":"Australia/Melbourne",
                      "scope":"locality",
                      "countryCode":"AU",
                      "phqRank":72,
                      "localRank":85,
                      "aviationRank":0,
                      "status":"active",
                      "lon":144.9785584,
                      "lat":-37.8216161
                    },
                    {
                      "eventId":"gHH4wZWHsktLAzk4tr",
                      "createdDate":"2018-04-20T03:01:47Z",
                      "updatedDate":"2023-12-31T23:59:55.25Z",
                      "title":"Michael McIntyre",
                      "category":"performing-arts",
                      "eventStart":"2019-06-15T10:00:00Z",
                      "eventEnd":"2019-06-15T10:00:00Z",
                      "timezone":"Australia/Melbourne",
                      "scope":"locality",
                      "countryCode":"AU",
                      "phqRank":72,
                      "localRank":85,
                      "aviationRank":0,
                      "status":"active",
                      "lon":144.9785584,
                      "lat":-37.8216161
                    },
                    {
                      "eventId":"PsbPZUXwsQMpFSzhfB",
                      "createdDate":"2018-02-15T01:44:16Z",
                      "updatedDate":"2023-10-31T12:24:04.955Z",
                      "title":"Kevin Hart",
                      "category":"performing-arts",
                      "eventStart":"2018-12-05T08:30:00Z",
                      "eventEnd":"2018-12-05T08:30:00Z",
                      "timezone":"Australia/Melbourne",
                      "scope":"locality",
                      "countryCode":"AU",
                      "phqRank":73,
                      "localRank":85,
                      "aviationRank":0,
                      "status":"active",
                      "lon":144.9785584,
                      "lat":-37.8216161
                    }
                  ]
                """.trimIndent()
                )
            )
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvents for time range should return 200 with valid response payload`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&start=2024-02-05T00:00:00Z&end=2024-02-06T23:59:59Z"))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                  [
                    {
                      "eventId":"9nkBzs5Hk9z8zcvLnS",
                      "createdDate":"2023-08-01T20:08:15.026132Z",
                      "updatedDate":"2024-01-11T11:00:48.414Z",
                      "title":"Melanie Martinez and UPSAHL",
                      "category":"concerts",
                      "eventStart":"2024-02-06T08:30:00Z",
                      "eventEnd":"2024-02-06T08:30:00Z",
                      "timezone":"Australia/Melbourne",
                      "countryCode":"AU",
                      "phqRank":62,
                      "status":"active"
                    },
                    {
                      "eventId":"BWmSpRy5s7pahwMy4v",
                      "createdDate":"2023-06-08T20:12:12.479681Z",
                      "updatedDate":"2024-01-11T12:54:27.517Z",
                      "title":"Melanie Martinez and UPSAHL",
                      "category":"concerts",
                      "eventStart":"2024-02-05T08:30:00Z",
                      "eventEnd":"2024-02-05T08:30:00Z",
                      "timezone":"Australia/Melbourne",
                      "countryCode":"AU",
                      "phqRank":62,
                      "status":"active"
                    }
                  ]
                """.trimIndent()
                )
            )
    }

    @Test
    @Disabled("This test is failing because the Databricks connection is not configured correctly")
    fun `getEvents for status should return 200 with valid response payload`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=10.0&category=performing-arts&status=cancelled"))
            .andExpect(status().isOk)
            .andExpect(
                content().json(
                    """
                  [
                    {
                      "eventId":"3WawixybruhTnAfnpZ",
                      "createdDate":"2019-03-23T03:15:24.86549Z",
                      "updatedDate":"2023-08-28T00:47:08.546Z",
                      "title":"Cirque du Soleil Kurios Melbourne",
                      "category":"performing-arts",
                      "eventStart":"2020-03-29T02:30:00Z",
                      "eventEnd":"2020-03-29T02:30:00Z",
                      "timezone":"Australia/Melbourne",
                      "countryCode":"AU",
                      "phqRank":63,
                      "status":"cancelled"
                    }
                  ]
                """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 401 when access token is missing`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0", jwt = null))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `getEvents should return 401 when access token is invalid`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0", jwt = "invalid_token"))
            .andExpect(status().isUnauthorized)
            .andExpect(content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `getEvents should return 403 when jwt has insufficient permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()

        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0", invalidJwt))
            .andExpect(status().isForbidden)
            .andExpect(content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `getEvents should return 400 when combination of country codes and location params are present`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&countryCode=AU"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"latitude, longitude and radius should not be provided when filtering by countryCodes"
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when combination of country codes is not present and location params is incomplete or are not present`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&radius=5.0"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"latitude, longitude and radius are required when not filtering by countryCodes"
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when invalid location params are passed`() {
        mockMvc.perform(smGet("/api/events?latitude=-91&longitude=181&radius=0"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"MIN",
                      "message":"must be greater than or equal to -90",
                      "meta":{"field":"getEvents.latitude"}
                    },
                    {
                      "code":"MAX",
                      "message":"must be less than or equal to 180",
                      "meta":{"field":"getEvents.longitude"}
                    },
                    {
                      "code":"MIN",
                      "message":"must be greater than or equal to 1",
                      "meta":{"field":"getEvents.radius"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when invalid category is passed`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&category=xxx&category=yyy"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"Must be one of [observances, public-holidays, concerts, sports, community, expos, performing-arts, conferences, school-holidays, festivals], but was [xxx, yyy]",
                      "meta":{"field":"category"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when invalid excludeCategory is passed`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&excludeCategory=zzz"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"Must be one of [observances, public-holidays, concerts, sports, community, expos, performing-arts, conferences, school-holidays, festivals], but was zzz",
                      "meta":{"field":"excludeCategory"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when invalid status is passed`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&status=xxx&status=yyy"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"Must be one of [active, postponed, cancelled, predicted, archived], but was [xxx, yyy]",
                      "meta":{"field":"status"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when invalid exclude status is passed`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&excludeStatus=zzz"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"Must be one of [active, postponed, cancelled, predicted, archived], but was zzz",
                      "meta":{"field":"excludeStatus"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when invalid date range is passed`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&start=2024-02-06T00:00:00Z&end=2024-02-05T23:59:59Z"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"start must be before end",
                      "meta":{"field":"start"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when invalid page number is passed`() {
        mockMvc.perform(smGet("/api/events?countryCode=AU&page=0"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"MIN",
                      "message":"must be greater than or equal to 1",
                      "meta":{"field":"getEvents.page"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when invalid pageSize is passed`() {
        mockMvc.perform(smGet("/api/events?countryCode=AU&pageSize=501"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"MAX",
                      "message":"must be less than or equal to 500",
                      "meta":{"field":"getEvents.pageSize"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when combination of spid country code and location is provided`() {
        mockMvc.perform(smGet("/api/events?countryCode=AU,NZ&spid=123"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"latitude, longitude, radius and countryCodes should not be provided when filtering by spid"
                    }
                  ]
                }
            """.trimIndent()
                )
            )

        mockMvc.perform(smGet("/api/events?spid=123&latitude=-37.840935&longitude=144.946457&radius=5.0"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"latitude, longitude, radius and countryCodes should not be provided when filtering by spid"
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    @Test
    fun `getEvents should return 400 when createdStart is after createdEnd`() {
        mockMvc.perform(smGet("/api/events?latitude=-37.840935&longitude=144.946457&radius=5.0&createdStart=2024-02-07T00:00:00Z&createdEnd=2024-02-06T00:00:00Z"))
            .andExpect(status().isBadRequest)
            .andExpect(
                content().json(
                    """
                {
                  "errors":[
                    {
                      "code":"INVALID",
                      "message":"createdStart must be before createdEnd",
                      "meta":{"field":"createdStart"}
                    }
                  ]
                }
            """.trimIndent()
                )
            )
    }

    private fun smGet(url: String, jwt: String? = this.jwt): MockHttpServletRequestBuilder {
        val requestBuilder = MockMvcRequestBuilders.get(url)
            .header(ApiConstants.SM_TRACE_TOKEN_HEADER, "trace-token-123")
            .contentType(MediaType.APPLICATION_JSON)

        if (jwt != null) {
            requestBuilder.header(HttpHeaders.AUTHORIZATION, "Bearer $jwt")
        }

        return requestBuilder
    }
}

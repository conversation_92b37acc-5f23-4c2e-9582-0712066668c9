package com.siteminder.hotel

import com.siteminder.context.TraceTokenContextUtils
import com.siteminder.job.Task
import com.siteminder.metrics.Metrics
import com.siteminder.nxs.HotelResponse
import com.siteminder.nxs.NxsApiClient
import com.siteminder.nxs.NxsStreamingApiClient
import com.siteminder.nxs.PropertyResponse
import org.slf4j.LoggerFactory
import org.slf4j.MDC

class HotelSeedTask(
    private val nxsStreamingApiClient: NxsStreamingApiClient,
    private val nxsApiClient: NxsApiClient,
    private val hotelApiClient: HotelApiClient,
    private val sleepMillis: Long
) : Task {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun run() {
        val hotels = nxsStreamingApiClient.getHotels()
            .filter(::isEligible)

        val total = hotels.size
        logger.info("$total hotels to seed")

        hotels
            .forEachIndexed { idx, hotel ->
                try {
                    val spid = hotel.id

                    MDC.put("spid", spid)

                    logger.info("[${idx + 1}/$total] Migrating $spid")

                    val existingHotel = hotelApiClient.getHotel(spid, TraceTokenContextUtils.getTraceToken())
                    if (existingHotel == null) {
                        hotelApiClient.createHotel(hotel.toCreateHotelRequest(), TraceTokenContextUtils.getTraceToken())
                        logger.info("hotel created")
                    } else {
                        hotelApiClient.updateHotel(spid, hotel.toUpdateHotelRequest(), TraceTokenContextUtils.getTraceToken())
                        logger.info("hotel updated")
                    }

                    Thread.sleep(sleepMillis)

                } catch (e: Exception) {
                    logger.error("Error migrating hotel", e)
                    Metrics.meterException(e, this::class.java)
                } finally {
                    MDC.remove("spid")
                }
            }

        logger.info("Task complete")
    }

    private fun isEligible(hotel: HotelResponse): Boolean {

        if (hotel.country?.uppercase() !in listOf("US", "GB", "ES", "MX", "DE")) {
            return false
        }

        if (hotel.latitude == null || hotel.longitude == null) {
            return false
        }

        if (!hotel.platformEnabled) {
            return false
        }

        if (hotel.status == HotelResponse.PropertyStatus.dropped_off) {
            return false
        }

        Thread.sleep(sleepMillis)

        val property = nxsApiClient.getProperty(hotel.id) ?: return false

        if (property.accountType != PropertyResponse.AccountType.customer) {
            return false
        }

        if (property.status == PropertyResponse.PropertyStatus.suspended && property.suspendedReason == PropertyResponse.SuspendedReason.other) {
            return false
        }

        return true

    }
}

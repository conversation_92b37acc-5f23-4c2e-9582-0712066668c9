package com.siteminder

import com.fasterxml.jackson.databind.ObjectMapper
import com.siteminder.api.client.error.ErrorResponseParser
import com.siteminder.hotel.HotelApiClient
import com.siteminder.hotel.HotelSeedTask
import com.siteminder.http.RestTemplateBuilderCustomiser
import com.siteminder.metrics.MetricsAutoConfigurationProperties
import com.siteminder.nxs.NxsApiClient
import com.siteminder.nxs.NxsApiProperties
import com.siteminder.nxs.NxsStreamingApiClient
import com.siteminder.nxs.NxsStreamingApiProperties
import com.siteminder.security.JwtRepository
import com.siteminder.security.JwtRequestInterceptors
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ApplicationConfiguration {

    @Bean
    fun metricsAutoConfigurationProperties() = MetricsAutoConfigurationProperties("hotel-seed-job")

    @Bean
    fun hotelSeedTask(
        nxsStreamingApiClient: NxsStreamingApiClient,
        nxsApiClient: NxsApiClient,
        hotelApiClient: HotelApiClient,
        @Value("\${predicthq.hotel-seed-job.sleep-time-in-millis:200}")
        sleepMillis: Long
    ) = HotelSeedTask(nxsStreamingApiClient, nxsApiClient, hotelApiClient, sleepMillis)

    @Bean
    fun errorResponseParser(objectMapper: ObjectMapper) = ErrorResponseParser(objectMapper)

    @Bean
    fun nxsStreamingApiClient(
        properties: NxsStreamingApiProperties,
        restTemplateBuilderCustomiser: RestTemplateBuilderCustomiser,
        jwtRepository: JwtRepository,
        objectMapper: ObjectMapper
    ): NxsStreamingApiClient {

        val restTemplate = restTemplateBuilderCustomiser.custom()
            .maxConnections(properties.concurrency)
            .connectTimeoutInSeconds(properties.timeoutInSeconds)
            .socketTimeoutInSeconds(properties.timeoutInSeconds)
            .build()
            .rootUri(properties.baseUrl)
            .interceptors(JwtRequestInterceptors.simpleJwtInterceptor(jwtRepository, properties.tokenPath))
            .build()

        return NxsStreamingApiClient(restTemplate, objectMapper)
    }

    @Bean
    fun nxsApiClient(
        properties: NxsApiProperties,
        restTemplateBuilderCustomiser: RestTemplateBuilderCustomiser,
        jwtRepository: JwtRepository,
        objectMapper: ObjectMapper
    ): NxsApiClient {

        val restTemplate = restTemplateBuilderCustomiser.custom()
            .maxConnections(properties.concurrency)
            .connectTimeoutInSeconds(properties.timeoutInSeconds)
            .socketTimeoutInSeconds(properties.timeoutInSeconds)
            .build()
            .rootUri(properties.baseUrl)
            .interceptors(JwtRequestInterceptors.simpleJwtInterceptor(jwtRepository, properties.tokenPath))
            .build()

        return NxsApiClient(restTemplate, objectMapper)
    }
}

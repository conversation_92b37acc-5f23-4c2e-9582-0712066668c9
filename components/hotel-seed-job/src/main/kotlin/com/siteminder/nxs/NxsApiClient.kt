package com.siteminder.nxs

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.siteminder.context.TraceTokenContextUtils
import com.siteminder.metrics.Metrics
import com.siteminder.security.JwtRetrievalException
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatusCode
import org.springframework.web.client.HttpStatusCodeException
import org.springframework.web.client.RestClientException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.exchange

class NxsApiClient(private val restTemplate: RestTemplate, private val objectMapper: ObjectMapper) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getProperty(spid: String): PropertyResponse? {
        try {

            return restTemplate.exchange<PropertyResponse>("/properties/{spid}", HttpMethod.GET, HttpEntity<String>(headers()), mapOf("spid" to spid)).body ?: throw RuntimeException("nxs/api response body was null")

        } catch (e: JwtRetrievalException) {

            Metrics.meterException(e, NxsApiClient::class.java)

            logger.error("Error loading JWT", e)

            throw RuntimeException("Failed to retrieve valid JWT ${e.message}")

        } catch (e: HttpStatusCodeException) {

            if (isNotFoundErrorResponse(e.statusCode, e.responseBodyAsString)) {
                return null
            }

            logger.error("Error calling nxs/api, get property", e)
            throw RuntimeException("Failed request to nxs/api. ${e.message}", e)

        } catch (e: RestClientException) {
            logger.error("Error calling nxs/api, get property", e)
            throw RuntimeException("Failed request to nxs/api. ${e.message}", e)
        }
    }

    private fun headers() = HttpHeaders().apply {
        set("X-SM-TRACE-TOKEN", TraceTokenContextUtils.getTraceToken())
    }

    private fun isNotFoundErrorResponse(httpStatus: HttpStatusCode, response: String): Boolean {

        if (httpStatus != HttpStatusCode.valueOf(HttpStatus.NOT_FOUND.value())) {
            return false
        }

        try {
            val errorResponse = objectMapper.readValue<ErrorResponse>(response)
            return errorResponse.type == "NotFound"
        } catch (e: Exception) {

            // Potentially a bad url

            return false
        }
    }
}

@ConfigurationProperties("nxs.api")
data class NxsApiProperties(
    val baseUrl: String,
    val tokenPath: String,
    val concurrency: Int = 500,
    val timeoutInSeconds: Int = 60
)

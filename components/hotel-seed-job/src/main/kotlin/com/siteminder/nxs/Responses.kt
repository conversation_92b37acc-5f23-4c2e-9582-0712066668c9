package com.siteminder.nxs

import com.siteminder.hotel.CreateHotelRequest
import com.siteminder.hotel.UpdateHotelRequest
import java.math.BigDecimal

data class HotelResponse(
    val id: String,
    val latitude: BigDecimal? = null,
    val longitude: BigDecimal? = null,
    val country: String? = null,
    val platformEnabled: Boolean,
    val status: PropertyStatus
) {

    enum class PropertyStatus {
        active,
        onboarding,
        provisioning,
        suspended,
        dropped_off
    }

    fun toCreateHotelRequest(): CreateHotelRequest {
        return CreateHotelRequest(
            spid = id,
            latitude = latitude!!,
            longitude = longitude!!,
            countryCode = country!!.uppercase()
        )
    }

    fun toUpdateHotelRequest(): UpdateHotelRequest {
        return UpdateHotelRequest(
            latitude = latitude!!,
            longitude = longitude!!,
            countryCode = country!!.uppercase()
        )
    }
}

data class PropertyResponse(
    val id: String,
    val latitude: BigDecimal? = null,
    val longitude: BigDecimal? = null,
    val country: String? = null,
    val platformEnabled: Boolean,
    val accountType: AccountType? = null,
    val suspendedReason: SuspendedReason? = null,
    val status: PropertyStatus
) {

    enum class AccountType {
        customer, temporary_test, test
    }

    enum class SuspendedReason {
        non_payment, other
    }

    enum class PropertyStatus {
        active,
        onboarding,
        provisioning,
        suspended,
        dropped_off
    }
}

data class ErrorResponse(
    val type: String
)

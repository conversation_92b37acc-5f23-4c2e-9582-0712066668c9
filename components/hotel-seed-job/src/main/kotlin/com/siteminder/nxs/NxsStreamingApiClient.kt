package com.siteminder.nxs

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.siteminder.context.TraceTokenContextUtils
import com.siteminder.metrics.Metrics
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.exchange

class NxsStreamingApiClient(private val restTemplate: RestTemplate, private val objectMapper: ObjectMapper) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getHotels(): List<HotelResponse> {
        try {
            val httpResponse = restTemplate.exchange<ByteArray>("/properties", HttpMethod.GET, HttpEntity<String>(headers()))
            if (httpResponse.hasBody().not()) throw RuntimeException("nxs/streaming-api response body was null")

            return buildList {
                httpResponse.body!!.inputStream().bufferedReader().use {
                    it.lines().forEach { line ->
                        add(objectMapper.readValue(line))
                    }
                }
            }
        } catch (e: Exception) {
            Metrics.meterException(e, this::class.java)
            logger.error("Error calling nxs/streaming-api, get hotels", e)
            throw RuntimeException("Failed request to nxs/streaming-api. ${e.message}", e)
        }
    }

    private fun headers() = HttpHeaders().apply {
        contentType = MediaType.APPLICATION_JSON
        set("X-SM-TRACE-TOKEN", TraceTokenContextUtils.getTraceToken())
    }
}

@ConfigurationProperties("nxs.streaming-api")
data class NxsStreamingApiProperties(
    val baseUrl: String,
    val tokenPath: String,
    val concurrency: Int = 500,
    val timeoutInSeconds: Int = 60
)

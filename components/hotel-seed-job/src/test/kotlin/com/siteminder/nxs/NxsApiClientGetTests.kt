package com.siteminder.nxs

import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpStatus

@SpringBootTest
@ExtendWith(MockServerExtension::class)
class NxsApiClientGetTests {

    @Autowired
    private lateinit var client: NxsApiClient

    private lateinit var mockServerClient: MockServerClient

    val spid = "spid123"
    val latitude = 234.098.toBigDecimal()
    val longitude = (-73.456).toBigDecimal()

    @Test
    fun `get property should return valid response`() {
        val request = apiRequest(path = "/nxs-api/properties/$spid")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = """
                    {
                      "id": "$spid",
                      "latitude": $latitude,
                      "longitude": $longitude,
                      "country": "AU",
                      "platformEnabled": true,
                      "accountType": "customer",
                      "status": "active"
                    }
                    """.trimIndent()
                )
            )

        val response = client.getProperty(spid)

        response shouldBe PropertyResponse(
            id = spid,
            latitude = latitude,
            longitude = longitude,
            country = "AU",
            platformEnabled = true,
            accountType = PropertyResponse.AccountType.customer,
            status = PropertyResponse.PropertyStatus.active
        )
    }

    @Test
    fun `get property should return null when api returns 404`() {
        val request = apiRequest(path = "/nxs-api/properties/$spid")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.NOT_FOUND,
                    body = """
                        {
                          "type": "NotFound",
                          "message": "property with id=wer34 not found"
                        }
                    """.trimIndent()
                )
            )

        val response = client.getProperty(spid)

        response shouldBe null
    }

    @Test
    fun `get property should throw RuntimeException when response payload is null`() {
        val request = apiRequest(path = "/nxs-api/properties/$spid")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = null
                )
            )

        shouldThrow<RuntimeException> {
            client.getProperty(spid)
        }.also {
            it.message shouldBe "nxs/api response body was null"
        }
    }

    @Test
    fun `get property should throw RuntimeException when api respond with non-successful http code`() {
        val request = apiRequest(path = "/nxs-api/properties/$spid")

        mockServerClient.`when`(request)
            .respond(
                apiResponse<Void>(status = HttpStatus.BAD_REQUEST)
            )

        shouldThrow<RuntimeException> {
            client.getProperty(spid)
        }.also {
            it.message shouldBe "Failed request to nxs/api. 400 Bad Request: [no body]"
        }
    }
}

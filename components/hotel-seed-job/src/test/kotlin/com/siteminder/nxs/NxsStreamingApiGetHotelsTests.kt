package com.siteminder.nxs

import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpStatus

@SpringBootTest
@ExtendWith(MockServerExtension::class)
class NxsStreamingApiGetHotelsTests {

    @Autowired
    private lateinit var client: NxsStreamingApiClient

    private lateinit var mockServerClient: MockServerClient

    @Test
    fun `get hotels should return valid response`() {

        val request = apiRequest(path = "/streaming-api/properties")
        val nxsGetHotelsResponsePayload = """{"id":"spid123","name":"Hyatt ANDAZ VIENNA AM BELVEDERE","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"au","currency":"eur","timezone":"Europe/Vienna","latitude":"23.24","longitude":"-89.08","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":0}
            |{"id":"spid124","name":"Magnificent International Hotel Limited T/A Best Western Plus Hotel Hong Kong","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"nz","currency":"hkd","timezone":"Etc/GMT+8","latitude":"-14.76","longitude":"25.1843","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}""".trimMargin()


        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = nxsGetHotelsResponsePayload.toByteArray()
                )
            )

        val response = client.getHotels()

        response shouldBe listOf(
            HotelResponse(
                id = "spid123",
                latitude = 23.24.toBigDecimal(),
                longitude = (-89.08).toBigDecimal(),
                country = "au",
                platformEnabled = false,
                status = HotelResponse.PropertyStatus.active
            ),
            HotelResponse(
                id = "spid124",
                latitude = (-14.76).toBigDecimal(),
                longitude = 25.1843.toBigDecimal(),
                country = "nz",
                platformEnabled = true,
                status = HotelResponse.PropertyStatus.active
            )
        )
    }

    @Test
    fun `get demand data should throw exception when response payload is null`() {

        val request = apiRequest(path = "/streaming-api/properties")

        mockServerClient.`when`(request)
            .respond(apiResponse(body = null))

        shouldThrow<RuntimeException> {
            client.getHotels()
        }.also {
            it.message shouldBe "Failed request to nxs/streaming-api. nxs/streaming-api response body was null"
        }
    }

    @Test
    fun `create hotel should throw exception when api respond with non-successful http code`() {

        val request = apiRequest(path = "/streaming-api/properties")

        mockServerClient.`when`(request)
            .respond(apiResponse<Void>(status = HttpStatus.INTERNAL_SERVER_ERROR))

        shouldThrow<RuntimeException> {
            client.getHotels()
        }.also {
            it.message shouldBe "Failed request to nxs/streaming-api. 500 Internal Server Error: [no body]"
        }
    }
}

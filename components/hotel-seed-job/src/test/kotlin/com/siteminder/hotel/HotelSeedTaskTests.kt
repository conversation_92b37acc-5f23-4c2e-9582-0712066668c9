package com.siteminder.hotel

import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.context.TraceTokenContextUtils
import com.siteminder.mockserver.MockServerExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.mockserver.model.HttpRequest
import org.mockserver.verify.VerificationTimes
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

@SpringBootTest
@ExtendWith(MockServerExtension::class)
class HotelSeedTaskTests {

    private lateinit var mockServerClient: MockServerClient

    @Autowired
    private lateinit var task: HotelSeedTask

    @BeforeEach
    fun setUp() {
        TraceTokenContextUtils.setTraceToken(UUID.randomUUID().toString())
    }

    @Test
    fun `job run should ignore hotels that are not in the included countries`() {
        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")

        val nxsGetHotelsResponsePayload = """{"id":"spid123","name":"Hyatt ANDAZ VIENNA AM BELVEDERE","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"cn","currency":"eur","timezone":"Europe/Vienna","latitude":"23.24","longitude":"-89.08","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":0}
            |{"id":"spid124","name":"Magnificent International Hotel Limited T/A Best Western Plus Hotel Hong Kong","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"ph","currency":"hkd","timezone":"Etc/GMT+8","latitude":"-14.76","longitude":"25.1843","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":0}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withMethod("POST"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withMethod("PUT"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withPath("/hotel-api/api/hotels"), VerificationTimes.never())
    }

    @Test
    fun `job run should ignore hotels without either latitude or longitude`() {
        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")

        val nxsGetHotelsResponsePayload = """{"id":"spid123","name":"Hyatt ANDAZ VIENNA AM BELVEDERE","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"es","currency":"eur","timezone":"Europe/Vienna","latitude":null,"longitude":"-89.08","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":0}
            |{"id":"spid124","name":"Magnificent International Hotel Limited T/A Best Western Plus Hotel Hong Kong","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"us","currency":"hkd","timezone":"Etc/GMT+8","latitude":"-14.76","longitude":null,"status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":0}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withMethod("POST"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withMethod("PUT"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withPath("/hotel-api/api/hotels"), VerificationTimes.never())
    }

    @Test
    fun `job run should ignore hotels that are not platform-enabled`() {
        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")

        val nxsGetHotelsResponsePayload = """{"id":"spid123","name":"Hyatt ANDAZ VIENNA AM BELVEDERE","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"de","currency":"eur","timezone":"Europe/Vienna","latitude":"23.43","longitude":"-89.08","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":0}
            |{"id":"spid124","name":"Magnificent International Hotel Limited T/A Best Western Plus Hotel Hong Kong","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"gb","currency":"hkd","timezone":"Etc/GMT+8","latitude":"-14.76","longitude":"54.56","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":0}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withMethod("POST"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withMethod("PUT"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withPath("/hotel-api/api/hotels"), VerificationTimes.never())
    }

    @Test
    fun `job run should ignore drop off hotels`() {
        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")

        val nxsGetHotelsResponsePayload = """{"id":"spid123","name":"Hyatt ANDAZ VIENNA AM BELVEDERE","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"us","currency":"eur","timezone":"Europe/Vienna","latitude":"23.43","longitude":"-89.08","status":"dropped_off","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid124","name":"Magnificent International Hotel Limited T/A Best Western Plus Hotel Hong Kong","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"mx","currency":"hkd","timezone":"Etc/GMT+8","latitude":"-14.76","longitude":"54.56","status":"dropped_off","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withMethod("POST"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withMethod("PUT"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withPath("/hotel-api/api/hotels"), VerificationTimes.never())
    }

    @Test
    fun `job run should ignore hotels that are not of customer account type`() {
        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")

        val nxsGetHotelsResponsePayload = """{"id":"spid123","name":"Hyatt ANDAZ VIENNA AM BELVEDERE","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"us","currency":"eur","timezone":"Europe/Vienna","latitude":"23.43","longitude":"-89.08","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid124","name":"Magnificent International Hotel Limited T/A Best Western Plus Hotel Hong Kong","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"mx","currency":"hkd","timezone":"Etc/GMT+8","latitude":"-14.76","longitude":"54.56","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        val nxsGetPropertyRequest123 = mockNxsApiGetProperty("spid123", accountType = "test")
        val nxsGetPropertyRequest124 = mockNxsApiGetProperty("spid124", accountType = "temporary_test")

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withMethod("POST"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withMethod("PUT"), VerificationTimes.never())
        mockServerClient.verify(nxsGetPropertyRequest123)
        mockServerClient.verify(nxsGetPropertyRequest124)
        mockServerClient.verify(HttpRequest.request().withPath("/hotel-api/api/hotels"), VerificationTimes.never())
    }

    @Test
    fun `job run should ignore hotels suspended and reason is other`() {
        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")

        val nxsGetHotelsResponsePayload = """{"id":"spid123","name":"Hyatt ANDAZ VIENNA AM BELVEDERE","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"us","currency":"eur","timezone":"Europe/Vienna","latitude":"23.43","longitude":"-89.08","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid124","name":"Magnificent International Hotel Limited T/A Best Western Plus Hotel Hong Kong","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"mx","currency":"hkd","timezone":"Etc/GMT+8","latitude":"-14.76","longitude":"54.56","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        val nxsGetPropertyRequest123 = mockNxsApiGetProperty("spid123", status = "suspended", suspendedReason = "other")
        val nxsGetPropertyRequest124 = mockNxsApiGetProperty("spid124", status = "suspended", suspendedReason = "other")

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withMethod("POST"), VerificationTimes.never())
        mockServerClient.verify(HttpRequest.request().withMethod("PUT"), VerificationTimes.never())
        mockServerClient.verify(nxsGetPropertyRequest123)
        mockServerClient.verify(nxsGetPropertyRequest124)
        mockServerClient.verify(HttpRequest.request().withPath("/hotel-api/api/hotels"), VerificationTimes.never())
    }


    @Test
    fun `job run should create new hotels`() {
        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")
        val nxsGetHotelsResponsePayload = """{"id":"spid-us","name":"US Hotel","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"us","currency":"USD","timezone":"Europe/Vienna","latitude":"38.79","longitude":"106.53","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid-gb","name":"GB Hotel","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"gb","currency":"LB","timezone":"Etc/GMT+8","latitude":"55.37","longitude":"3.43","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid-es","name":"ES Hotel","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"es","currency":"EUR","timezone":"Etc/GMT+8","latitude":"40.46","longitude":"3.74","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid-mx","name":"MX Hotel","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"mx","currency":"MXN","timezone":"Etc/GMT+8","latitude":"23.63","longitude":"102.55","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid-de","name":"De Hotel","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"de","currency":"EUR","timezone":"Etc/GMT+8","latitude":"51.16","longitude":"10.45","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        mockNxsApiGetProperty("spid-us")
        mockNxsApiGetProperty("spid-gb")
        mockNxsApiGetProperty("spid-es")
        mockNxsApiGetProperty("spid-mx")
        mockNxsApiGetProperty("spid-de")

        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-us")).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND, body = """
            {
              "errors": [
                {
                  "code": "NOT_FOUND",
                  "message": "Hotel not found",
                  "meta": {
                    "spid": "spid-us",
                    "entity": "Hotel"
                  }
                }
              ]
            }
        """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-gb")).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
            {
              "errors": [
                {
                  "code": "NOT_FOUND",
                  "message": "Hotel not found",
                  "meta": {
                    "spid": "spid-gb",
                    "entity": "Hotel"
                  }
                }
              ]
            }
        """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-es")).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
            {
              "errors": [
                {
                  "code": "NOT_FOUND",
                  "message": "Hotel not found",
                  "meta": {
                    "spid": "spid-es",
                    "entity": "Hotel"
                  }
                }
              ]
            }
        """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-mx")).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
            {
              "errors": [
                {
                  "code": "NOT_FOUND",
                  "message": "Hotel not found",
                  "meta": {
                    "spid": "spid-mx",
                    "entity": "Hotel"
                  }
                }
              ]
            }
        """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-de")).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
            {
              "errors": [
                {
                  "code": "NOT_FOUND",
                  "message": "Hotel not found",
                  "meta": {
                    "spid": "spid-de",
                    "entity": "Hotel"
                  }
                }
              ]
            }
        """.trimIndent()
            )
        )

        val createHotelRequestUS = apiRequest(
            "/hotel-api/api/hotels", httpMethod = HttpMethod.POST, body = """
            {
                "spid": "spid-us",
                "latitude": 38.79,
                "longitude": 106.53,
                "countryCode": "US"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(createHotelRequestUS).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-us",
                  "latitude": 38.79,
                  "longitude": 106.53,
                  "countryCode": "US",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        val createHotelRequestGB = apiRequest(
            "/hotel-api/api/hotels", httpMethod = HttpMethod.POST, body = """
            {
                "spid": "spid-gb",
                "latitude": 55.37,
                "longitude": 3.43,
                "countryCode": "GB"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(createHotelRequestGB).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid124",
                  "latitude": 55.37,
                  "longitude": 3.43,
                  "countryCode": "GB",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        val createHotelRequestES = apiRequest(
            "/hotel-api/api/hotels", httpMethod = HttpMethod.POST, body = """
            {
                "spid": "spid-es",
                "latitude": 40.46,
                "longitude": 3.74,
                "countryCode": "ES"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(createHotelRequestES).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-es",
                  "latitude": 40.46,
                  "longitude": 3.74,
                  "countryCode": "ES",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        val createHotelRequestMX = apiRequest(
            "/hotel-api/api/hotels", httpMethod = HttpMethod.POST, body = """
            {
                "spid": "spid-mx",
                "latitude": 23.63,
                "longitude": 102.55,
                "countryCode": "MX"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(createHotelRequestMX).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-mx",
                  "latitude": 23.63,
                  "longitude": 102.55,
                  "countryCode": "MX",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        val createHotelRequestDE = apiRequest(
            "/hotel-api/api/hotels", httpMethod = HttpMethod.POST, body = """
            {
                "spid": "spid-de",
                "latitude": 51.16,
                "longitude": 10.45,
                "countryCode": "DE"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(createHotelRequestDE).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-de",
                  "latitude": 51.16,
                  "longitude": 10.45,
                  "countryCode": "DE",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(createHotelRequestUS, VerificationTimes.once())
        mockServerClient.verify(createHotelRequestGB, VerificationTimes.once())
        mockServerClient.verify(createHotelRequestES, VerificationTimes.once())
        mockServerClient.verify(createHotelRequestMX, VerificationTimes.once())
        mockServerClient.verify(createHotelRequestDE, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withMethod("PUT"), VerificationTimes.never())
    }

    @Test
    fun `job run should update existing hotels`() {
        val today = ZonedDateTime.now(ZoneId.of("UTC"))

        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")
        val nxsGetHotelsResponsePayload = """{"id":"spid-us","name":"US Hotel","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"us","currency":"USD","timezone":"Europe/Vienna","latitude":"38.79","longitude":"106.53","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid-gb","name":"GB Hotel","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"gb","currency":"LB","timezone":"Etc/GMT+8","latitude":"55.37","longitude":"3.43","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid-es","name":"ES Hotel","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"es","currency":"EUR","timezone":"Etc/GMT+8","latitude":"40.46","longitude":"3.74","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid-mx","name":"MX Hotel","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"mx","currency":"MXN","timezone":"Etc/GMT+8","latitude":"23.63","longitude":"102.55","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid-de","name":"De Hotel","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"de","currency":"EUR","timezone":"Etc/GMT+8","latitude":"51.16","longitude":"10.45","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        mockNxsApiGetProperty("spid-us")
        mockNxsApiGetProperty("spid-gb")
        mockNxsApiGetProperty("spid-es")
        mockNxsApiGetProperty("spid-mx")
        mockNxsApiGetProperty("spid-de")

        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-us")).respond(
            apiResponse(
                body = """
                {
                    "spid": "spid-us",
                    "latitude": 38.69,
                    "longitude": 106.53,
                    "countryCode": "US",
                    "suggestedRadius": 1.0,
                    "createdAt": "$today",
                    "updatedAt": "$today"
                }
            """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-gb")).respond(
            apiResponse(
                body = """
                {
                    "spid": "spid-gb",
                    "latitude": 55.27,
                    "longitude": 3.33,
                    "countryCode": "GB",
                    "suggestedRadius": 5.0,
                    "createdAt": "$today",
                    "updatedAt": "$today"
                }
            """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-es")).respond(
            apiResponse(
                body = """
                {
                    "spid": "spid-es",
                    "latitude": 40.36,
                    "longitude": 3.64,
                    "countryCode": "ES",
                    "suggestedRadius": 5.0,
                    "createdAt": "$today",
                    "updatedAt": "$today"
                }
            """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-mx")).respond(
            apiResponse(
                body = """
                {
                    "spid": "spid-mx",
                    "latitude": 23.53,
                    "longitude": 102.45,
                    "countryCode": "MX",
                    "suggestedRadius": 5.0,
                    "createdAt": "$today",
                    "updatedAt": "$today"
                }
            """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid-de")).respond(
            apiResponse(
                body = """
                {
                    "spid": "spid-de",
                    "latitude": 51.06,
                    "longitude": 10.35,
                    "countryCode": "DE",
                    "suggestedRadius": 5.0,
                    "createdAt": "$today",
                    "updatedAt": "$today"
                }
            """.trimIndent()
            )
        )

        val udpateHotelRequestUS = apiRequest(
            "/hotel-api/api/hotels/spid-us", httpMethod = HttpMethod.PUT, body = """
            {
                "latitude": 38.79,
                "longitude": 106.53,
                "countryCode": "US"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(udpateHotelRequestUS).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-us",
                  "latitude": 38.79,
                  "longitude": 106.53,
                  "countryCode": "US",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        val updateHotelRequestGB = apiRequest(
            "/hotel-api/api/hotels/spid-gb", httpMethod = HttpMethod.PUT, body = """
            {
                "latitude": 55.37,
                "longitude": 3.43,
                "countryCode": "GB"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(updateHotelRequestGB).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-gb",
                  "latitude": 55.37,
                  "longitude": 3.43,
                  "countryCode": "GB",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        val updateHotelRequestES = apiRequest(
            "/hotel-api/api/hotels/spid-es", httpMethod = HttpMethod.PUT, body = """
            {
                "latitude": 40.46,
                "longitude": 3.74,
                "countryCode": "ES"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(updateHotelRequestES).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-es",
                  "latitude": 40.46,
                  "longitude": 3.74,
                  "countryCode": "ES",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        val updateHotelRequestMX = apiRequest(
            "/hotel-api/api/hotels/spid-mx", httpMethod = HttpMethod.PUT, body = """
            {
                "latitude": 23.63,
                "longitude": 102.55,
                "countryCode": "MX"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(updateHotelRequestMX).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-mx",
                  "latitude": 23.63,
                  "longitude": 102.55,
                  "countryCode": "MX",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        val updateHotelRequestDE = apiRequest(
            "/hotel-api/api/hotels/spid-de", httpMethod = HttpMethod.PUT, body = """
            {
                "latitude": 51.16,
                "longitude": 10.45,
                "countryCode": "DE"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(updateHotelRequestDE).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid-de",
                  "latitude": 51.16,
                  "longitude": 10.45,
                  "countryCode": "DE",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(udpateHotelRequestUS, VerificationTimes.once())
        mockServerClient.verify(updateHotelRequestGB, VerificationTimes.once())
        mockServerClient.verify(updateHotelRequestMX, VerificationTimes.once())
        mockServerClient.verify(updateHotelRequestDE, VerificationTimes.once())
        mockServerClient.verify(updateHotelRequestES, VerificationTimes.once())
        mockServerClient.verify(HttpRequest.request().withMethod("POST"), VerificationTimes.never())
    }

    @Test
    fun `job run should continue running for the rest of the hotels even if it encounter error in one hotel`() {
        val today = ZonedDateTime.now(ZoneId.of("UTC"))

        val nxsGetHotelsRequest = apiRequest("/streaming-api/properties")
        val nxsGetHotelsResponsePayload = """{"id":"spid123","name":"Hyatt ANDAZ VIENNA AM BELVEDERE","street":"Arsenalstraße 10","suburb":"Wien","state":"*","postcode":"1100","country":"gb","currency":"eur","timezone":"Europe/Vienna","latitude":"23.24","longitude":"-89.08","status":"active","salesforceAccountId":"001D000002Jnks4IAB","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}
            |{"id":"spid124","name":"Magnificent International Hotel Limited T/A Best Western Plus Hotel Hong Kong","street":"308 Des Voeux Road West ","suburb":"*","state":"Western District ","postcode":"*","country":"us","currency":"USD","timezone":"Etc/GMT+8","latitude":"-14.76","longitude":"25.1843","status":"active","salesforceAccountId":"001D000001XRWG2IAP","enterpriseCode":"siteminder_independents","partnerCode":"siteminder","platformEnabled":1}""".trimMargin()
        mockServerClient.`when`(nxsGetHotelsRequest).respond(
            apiResponse(
                body = nxsGetHotelsResponsePayload.toByteArray()
            )
        )

        mockNxsApiGetProperty("spid123")
        mockNxsApiGetProperty("spid124")

        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid123")).respond(
            apiResponse(
                body = """
                {
                    "spid": "spid123",
                    "latitude": 324.043,
                    "longitude": -341.203,
                    "countryCode": "AU",
                    "suggestedRadius": 1.0,
                    "createdAt": "$today",
                    "updatedAt": "$today"
                }
            """.trimIndent()
            )
        )
        mockServerClient.`when`(apiRequest("/hotel-api/api/hotels/spid124")).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
                {
                  "errors": [
                    {
                      "code": "NOT_FOUND",
                      "message": "Hotel not found",
                      "meta": {
                        "spid": "spid124",
                        "entity": "Hotel"
                      }
                    }
                  ]
                }
            """.trimIndent()
            )
        )

        val udpateHotelRequest123 = apiRequest(
            "/hotel-api/api/hotels/spid123", httpMethod = HttpMethod.PUT, body = """
            {
                "latitude": 23.24,
                "longitude": -89.08,
                "countryCode": "GB"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(udpateHotelRequest123).respond(apiResponse<String>(status = HttpStatus.INTERNAL_SERVER_ERROR))

        val createHotelRequest124 = apiRequest(
            "/hotel-api/api/hotels", httpMethod = HttpMethod.POST, body = """
            {
                "spid": "spid124",
                "latitude": -14.76,
                "longitude": 25.1843,
                "countryCode": "US"
            }
        """.trimIndent()
        )
        mockServerClient.`when`(createHotelRequest124).respond(
            apiResponse(
                body = """
                {
                  "spid": "spid124",
                  "latitude": -14.76,
                  "longitude": 25.1843,
                  "countryCode": "NZ",
                  "suggestedRadius": 3.3,
                  "createdAt": "2024-03-29T00:00:00Z",
                  "updatedAt": "2024-03-29T00:00:00Z"
                }
            """.trimIndent()
            )
        )

        task.run()

        mockServerClient.verify(nxsGetHotelsRequest, VerificationTimes.once())
        mockServerClient.verify(udpateHotelRequest123, VerificationTimes.once())
        mockServerClient.verify(createHotelRequest124, VerificationTimes.once())
    }

    private fun mockNxsApiGetProperty(spid: String, country: String = "AU", status: String = "active", suspendedReason: String = "non_payment", accountType: String = "customer"): HttpRequest {

        val getPropertyRequest = apiRequest(path = "/nxs-api/properties/$spid")
        mockServerClient.`when`(getPropertyRequest).respond(
            apiResponse(
                body = """
                    {
                      "id": "$spid",
                      "latitude": -1.09,
                      "longitude": 3.46,
                      "country": "$country",
                      "platformEnabled": true,
                      "status": "$status",
                      "accountType": "$accountType",
                      "suspendedReason": "$suspendedReason"
                    }
                """.trimIndent()
            )
        )

        return getPropertyRequest
    }

}

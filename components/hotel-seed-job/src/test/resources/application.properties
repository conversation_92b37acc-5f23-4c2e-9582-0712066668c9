spring.profiles.include=localstack

predicthq.hotel-api.base-url=http://mockserver:1080/hotel-api
predicthq.hotel-api.token-path=tokens/predicthq-hotel-api-token
predicthq.hotel-api.concurrency=100
predicthq.hotel-api.timeout-in-seconds=60

nxs.streaming-api.base-url=http://mockserver:1080/streaming-api
nxs.streaming-api.token-path=tokens/nxs-streaming-api-token
nxs.streaming-api.concurrency=100
nxs.streaming-api.timeout-in-seconds=60

nxs.api.base-url=http://mockserver:1080/nxs-api
nxs.api.token-path=tokens/nxs-api-token
nxs.api.concurrency=100
nxs.api.timeout-in-seconds=60

secrets.bucketName=sm.secrets.dev

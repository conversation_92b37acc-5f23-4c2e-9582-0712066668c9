apply plugin: 'springboot-kotlin-configuration-plugin' // https://github.com/siteminder-au/sm-gradle-plugins/tree/master/sm-build-plugin

dependencies {
    implementation "com.siteminder:sm-spring-boot-api-error-client-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-consumer-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-http-client-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-job-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-jwt-client-starter:${smLibraryVersion}"

    implementation project(":libs:hotel-api-client")

    testImplementation "com.siteminder:sm-spring-boot-starter-test:${smLibraryVersion}"
}

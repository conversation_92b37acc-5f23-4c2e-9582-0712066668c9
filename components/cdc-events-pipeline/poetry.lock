# This file is automatically @generated by Poetry 1.8.3 and should not be changed by hand.

[[package]]
name = "boto3"
version = "1.38.22"
description = "The AWS SDK for Python"
optional = false
python-versions = ">=3.9"
files = [
    {file = "boto3-1.38.22-py3-none-any.whl", hash = "sha256:760c85ab6dd78f12aa669269ca917d313fe02378722dc3b8ab41a8dc13b2a999"},
    {file = "boto3-1.38.22.tar.gz", hash = "sha256:008f6a7c2f9f306984f9bd00c331d70341124aaa7dfebcb0466ecbda6619884a"},
]

[package.dependencies]
botocore = ">=1.38.22,<1.39.0"
jmespath = ">=0.7.1,<2.0.0"
s3transfer = ">=0.13.0,<0.14.0"

[package.extras]
crt = ["botocore[crt] (>=1.21.0,<2.0a0)"]

[[package]]
name = "boto3-stubs"
version = "1.36.26"
description = "Type annotations for boto3 1.36.26 generated with mypy-boto3-builder 8.9.2"
optional = false
python-versions = ">=3.8"
files = [
    {file = "boto3_stubs-1.36.26-py3-none-any.whl", hash = "sha256:ebeb4537c2cebf591ed003b0004db2ec8400718f404ac19ce070844b009633e5"},
    {file = "boto3_stubs-1.36.26.tar.gz", hash = "sha256:4d9cfbe7c6b55dac5d4509a2e44df1263be9a684c7723d297baffb5f579098a1"},
]

[package.dependencies]
botocore-stubs = "*"
types-s3transfer = "*"
typing-extensions = {version = ">=4.1.0", markers = "python_version < \"3.12\""}

[package.extras]
accessanalyzer = ["mypy-boto3-accessanalyzer (>=1.36.0,<1.37.0)"]
account = ["mypy-boto3-account (>=1.36.0,<1.37.0)"]
acm = ["mypy-boto3-acm (>=1.36.0,<1.37.0)"]
acm-pca = ["mypy-boto3-acm-pca (>=1.36.0,<1.37.0)"]
all = ["mypy-boto3-accessanalyzer (>=1.36.0,<1.37.0)", "mypy-boto3-account (>=1.36.0,<1.37.0)", "mypy-boto3-acm (>=1.36.0,<1.37.0)", "mypy-boto3-acm-pca (>=1.36.0,<1.37.0)", "mypy-boto3-amp (>=1.36.0,<1.37.0)", "mypy-boto3-amplify (>=1.36.0,<1.37.0)", "mypy-boto3-amplifybackend (>=1.36.0,<1.37.0)", "mypy-boto3-amplifyuibuilder (>=1.36.0,<1.37.0)", "mypy-boto3-apigateway (>=1.36.0,<1.37.0)", "mypy-boto3-apigatewaymanagementapi (>=1.36.0,<1.37.0)", "mypy-boto3-apigatewayv2 (>=1.36.0,<1.37.0)", "mypy-boto3-appconfig (>=1.36.0,<1.37.0)", "mypy-boto3-appconfigdata (>=1.36.0,<1.37.0)", "mypy-boto3-appfabric (>=1.36.0,<1.37.0)", "mypy-boto3-appflow (>=1.36.0,<1.37.0)", "mypy-boto3-appintegrations (>=1.36.0,<1.37.0)", "mypy-boto3-application-autoscaling (>=1.36.0,<1.37.0)", "mypy-boto3-application-insights (>=1.36.0,<1.37.0)", "mypy-boto3-application-signals (>=1.36.0,<1.37.0)", "mypy-boto3-applicationcostprofiler (>=1.36.0,<1.37.0)", "mypy-boto3-appmesh (>=1.36.0,<1.37.0)", "mypy-boto3-apprunner (>=1.36.0,<1.37.0)", "mypy-boto3-appstream (>=1.36.0,<1.37.0)", "mypy-boto3-appsync (>=1.36.0,<1.37.0)", "mypy-boto3-apptest (>=1.36.0,<1.37.0)", "mypy-boto3-arc-zonal-shift (>=1.36.0,<1.37.0)", "mypy-boto3-artifact (>=1.36.0,<1.37.0)", "mypy-boto3-athena (>=1.36.0,<1.37.0)", "mypy-boto3-auditmanager (>=1.36.0,<1.37.0)", "mypy-boto3-autoscaling (>=1.36.0,<1.37.0)", "mypy-boto3-autoscaling-plans (>=1.36.0,<1.37.0)", "mypy-boto3-b2bi (>=1.36.0,<1.37.0)", "mypy-boto3-backup (>=1.36.0,<1.37.0)", "mypy-boto3-backup-gateway (>=1.36.0,<1.37.0)", "mypy-boto3-backupsearch (>=1.36.0,<1.37.0)", "mypy-boto3-batch (>=1.36.0,<1.37.0)", "mypy-boto3-bcm-data-exports (>=1.36.0,<1.37.0)", "mypy-boto3-bcm-pricing-calculator (>=1.36.0,<1.37.0)", "mypy-boto3-bedrock (>=1.36.0,<1.37.0)", "mypy-boto3-bedrock-agent (>=1.36.0,<1.37.0)", "mypy-boto3-bedrock-agent-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-bedrock-data-automation (>=1.36.0,<1.37.0)", "mypy-boto3-bedrock-data-automation-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-bedrock-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-billing (>=1.36.0,<1.37.0)", "mypy-boto3-billingconductor (>=1.36.0,<1.37.0)", "mypy-boto3-braket (>=1.36.0,<1.37.0)", "mypy-boto3-budgets (>=1.36.0,<1.37.0)", "mypy-boto3-ce (>=1.36.0,<1.37.0)", "mypy-boto3-chatbot (>=1.36.0,<1.37.0)", "mypy-boto3-chime (>=1.36.0,<1.37.0)", "mypy-boto3-chime-sdk-identity (>=1.36.0,<1.37.0)", "mypy-boto3-chime-sdk-media-pipelines (>=1.36.0,<1.37.0)", "mypy-boto3-chime-sdk-meetings (>=1.36.0,<1.37.0)", "mypy-boto3-chime-sdk-messaging (>=1.36.0,<1.37.0)", "mypy-boto3-chime-sdk-voice (>=1.36.0,<1.37.0)", "mypy-boto3-cleanrooms (>=1.36.0,<1.37.0)", "mypy-boto3-cleanroomsml (>=1.36.0,<1.37.0)", "mypy-boto3-cloud9 (>=1.36.0,<1.37.0)", "mypy-boto3-cloudcontrol (>=1.36.0,<1.37.0)", "mypy-boto3-clouddirectory (>=1.36.0,<1.37.0)", "mypy-boto3-cloudformation (>=1.36.0,<1.37.0)", "mypy-boto3-cloudfront (>=1.36.0,<1.37.0)", "mypy-boto3-cloudfront-keyvaluestore (>=1.36.0,<1.37.0)", "mypy-boto3-cloudhsm (>=1.36.0,<1.37.0)", "mypy-boto3-cloudhsmv2 (>=1.36.0,<1.37.0)", "mypy-boto3-cloudsearch (>=1.36.0,<1.37.0)", "mypy-boto3-cloudsearchdomain (>=1.36.0,<1.37.0)", "mypy-boto3-cloudtrail (>=1.36.0,<1.37.0)", "mypy-boto3-cloudtrail-data (>=1.36.0,<1.37.0)", "mypy-boto3-cloudwatch (>=1.36.0,<1.37.0)", "mypy-boto3-codeartifact (>=1.36.0,<1.37.0)", "mypy-boto3-codebuild (>=1.36.0,<1.37.0)", "mypy-boto3-codecatalyst (>=1.36.0,<1.37.0)", "mypy-boto3-codecommit (>=1.36.0,<1.37.0)", "mypy-boto3-codeconnections (>=1.36.0,<1.37.0)", "mypy-boto3-codedeploy (>=1.36.0,<1.37.0)", "mypy-boto3-codeguru-reviewer (>=1.36.0,<1.37.0)", "mypy-boto3-codeguru-security (>=1.36.0,<1.37.0)", "mypy-boto3-codeguruprofiler (>=1.36.0,<1.37.0)", "mypy-boto3-codepipeline (>=1.36.0,<1.37.0)", "mypy-boto3-codestar-connections (>=1.36.0,<1.37.0)", "mypy-boto3-codestar-notifications (>=1.36.0,<1.37.0)", "mypy-boto3-cognito-identity (>=1.36.0,<1.37.0)", "mypy-boto3-cognito-idp (>=1.36.0,<1.37.0)", "mypy-boto3-cognito-sync (>=1.36.0,<1.37.0)", "mypy-boto3-comprehend (>=1.36.0,<1.37.0)", "mypy-boto3-comprehendmedical (>=1.36.0,<1.37.0)", "mypy-boto3-compute-optimizer (>=1.36.0,<1.37.0)", "mypy-boto3-config (>=1.36.0,<1.37.0)", "mypy-boto3-connect (>=1.36.0,<1.37.0)", "mypy-boto3-connect-contact-lens (>=1.36.0,<1.37.0)", "mypy-boto3-connectcampaigns (>=1.36.0,<1.37.0)", "mypy-boto3-connectcampaignsv2 (>=1.36.0,<1.37.0)", "mypy-boto3-connectcases (>=1.36.0,<1.37.0)", "mypy-boto3-connectparticipant (>=1.36.0,<1.37.0)", "mypy-boto3-controlcatalog (>=1.36.0,<1.37.0)", "mypy-boto3-controltower (>=1.36.0,<1.37.0)", "mypy-boto3-cost-optimization-hub (>=1.36.0,<1.37.0)", "mypy-boto3-cur (>=1.36.0,<1.37.0)", "mypy-boto3-customer-profiles (>=1.36.0,<1.37.0)", "mypy-boto3-databrew (>=1.36.0,<1.37.0)", "mypy-boto3-dataexchange (>=1.36.0,<1.37.0)", "mypy-boto3-datapipeline (>=1.36.0,<1.37.0)", "mypy-boto3-datasync (>=1.36.0,<1.37.0)", "mypy-boto3-datazone (>=1.36.0,<1.37.0)", "mypy-boto3-dax (>=1.36.0,<1.37.0)", "mypy-boto3-deadline (>=1.36.0,<1.37.0)", "mypy-boto3-detective (>=1.36.0,<1.37.0)", "mypy-boto3-devicefarm (>=1.36.0,<1.37.0)", "mypy-boto3-devops-guru (>=1.36.0,<1.37.0)", "mypy-boto3-directconnect (>=1.36.0,<1.37.0)", "mypy-boto3-discovery (>=1.36.0,<1.37.0)", "mypy-boto3-dlm (>=1.36.0,<1.37.0)", "mypy-boto3-dms (>=1.36.0,<1.37.0)", "mypy-boto3-docdb (>=1.36.0,<1.37.0)", "mypy-boto3-docdb-elastic (>=1.36.0,<1.37.0)", "mypy-boto3-drs (>=1.36.0,<1.37.0)", "mypy-boto3-ds (>=1.36.0,<1.37.0)", "mypy-boto3-ds-data (>=1.36.0,<1.37.0)", "mypy-boto3-dsql (>=1.36.0,<1.37.0)", "mypy-boto3-dynamodb (>=1.36.0,<1.37.0)", "mypy-boto3-dynamodbstreams (>=1.36.0,<1.37.0)", "mypy-boto3-ebs (>=1.36.0,<1.37.0)", "mypy-boto3-ec2 (>=1.36.0,<1.37.0)", "mypy-boto3-ec2-instance-connect (>=1.36.0,<1.37.0)", "mypy-boto3-ecr (>=1.36.0,<1.37.0)", "mypy-boto3-ecr-public (>=1.36.0,<1.37.0)", "mypy-boto3-ecs (>=1.36.0,<1.37.0)", "mypy-boto3-efs (>=1.36.0,<1.37.0)", "mypy-boto3-eks (>=1.36.0,<1.37.0)", "mypy-boto3-eks-auth (>=1.36.0,<1.37.0)", "mypy-boto3-elastic-inference (>=1.36.0,<1.37.0)", "mypy-boto3-elasticache (>=1.36.0,<1.37.0)", "mypy-boto3-elasticbeanstalk (>=1.36.0,<1.37.0)", "mypy-boto3-elastictranscoder (>=1.36.0,<1.37.0)", "mypy-boto3-elb (>=1.36.0,<1.37.0)", "mypy-boto3-elbv2 (>=1.36.0,<1.37.0)", "mypy-boto3-emr (>=1.36.0,<1.37.0)", "mypy-boto3-emr-containers (>=1.36.0,<1.37.0)", "mypy-boto3-emr-serverless (>=1.36.0,<1.37.0)", "mypy-boto3-entityresolution (>=1.36.0,<1.37.0)", "mypy-boto3-es (>=1.36.0,<1.37.0)", "mypy-boto3-events (>=1.36.0,<1.37.0)", "mypy-boto3-evidently (>=1.36.0,<1.37.0)", "mypy-boto3-finspace (>=1.36.0,<1.37.0)", "mypy-boto3-finspace-data (>=1.36.0,<1.37.0)", "mypy-boto3-firehose (>=1.36.0,<1.37.0)", "mypy-boto3-fis (>=1.36.0,<1.37.0)", "mypy-boto3-fms (>=1.36.0,<1.37.0)", "mypy-boto3-forecast (>=1.36.0,<1.37.0)", "mypy-boto3-forecastquery (>=1.36.0,<1.37.0)", "mypy-boto3-frauddetector (>=1.36.0,<1.37.0)", "mypy-boto3-freetier (>=1.36.0,<1.37.0)", "mypy-boto3-fsx (>=1.36.0,<1.37.0)", "mypy-boto3-gamelift (>=1.36.0,<1.37.0)", "mypy-boto3-geo-maps (>=1.36.0,<1.37.0)", "mypy-boto3-geo-places (>=1.36.0,<1.37.0)", "mypy-boto3-geo-routes (>=1.36.0,<1.37.0)", "mypy-boto3-glacier (>=1.36.0,<1.37.0)", "mypy-boto3-globalaccelerator (>=1.36.0,<1.37.0)", "mypy-boto3-glue (>=1.36.0,<1.37.0)", "mypy-boto3-grafana (>=1.36.0,<1.37.0)", "mypy-boto3-greengrass (>=1.36.0,<1.37.0)", "mypy-boto3-greengrassv2 (>=1.36.0,<1.37.0)", "mypy-boto3-groundstation (>=1.36.0,<1.37.0)", "mypy-boto3-guardduty (>=1.36.0,<1.37.0)", "mypy-boto3-health (>=1.36.0,<1.37.0)", "mypy-boto3-healthlake (>=1.36.0,<1.37.0)", "mypy-boto3-iam (>=1.36.0,<1.37.0)", "mypy-boto3-identitystore (>=1.36.0,<1.37.0)", "mypy-boto3-imagebuilder (>=1.36.0,<1.37.0)", "mypy-boto3-importexport (>=1.36.0,<1.37.0)", "mypy-boto3-inspector (>=1.36.0,<1.37.0)", "mypy-boto3-inspector-scan (>=1.36.0,<1.37.0)", "mypy-boto3-inspector2 (>=1.36.0,<1.37.0)", "mypy-boto3-internetmonitor (>=1.36.0,<1.37.0)", "mypy-boto3-invoicing (>=1.36.0,<1.37.0)", "mypy-boto3-iot (>=1.36.0,<1.37.0)", "mypy-boto3-iot-data (>=1.36.0,<1.37.0)", "mypy-boto3-iot-jobs-data (>=1.36.0,<1.37.0)", "mypy-boto3-iotanalytics (>=1.36.0,<1.37.0)", "mypy-boto3-iotdeviceadvisor (>=1.36.0,<1.37.0)", "mypy-boto3-iotevents (>=1.36.0,<1.37.0)", "mypy-boto3-iotevents-data (>=1.36.0,<1.37.0)", "mypy-boto3-iotfleethub (>=1.36.0,<1.37.0)", "mypy-boto3-iotfleetwise (>=1.36.0,<1.37.0)", "mypy-boto3-iotsecuretunneling (>=1.36.0,<1.37.0)", "mypy-boto3-iotsitewise (>=1.36.0,<1.37.0)", "mypy-boto3-iotthingsgraph (>=1.36.0,<1.37.0)", "mypy-boto3-iottwinmaker (>=1.36.0,<1.37.0)", "mypy-boto3-iotwireless (>=1.36.0,<1.37.0)", "mypy-boto3-ivs (>=1.36.0,<1.37.0)", "mypy-boto3-ivs-realtime (>=1.36.0,<1.37.0)", "mypy-boto3-ivschat (>=1.36.0,<1.37.0)", "mypy-boto3-kafka (>=1.36.0,<1.37.0)", "mypy-boto3-kafkaconnect (>=1.36.0,<1.37.0)", "mypy-boto3-kendra (>=1.36.0,<1.37.0)", "mypy-boto3-kendra-ranking (>=1.36.0,<1.37.0)", "mypy-boto3-keyspaces (>=1.36.0,<1.37.0)", "mypy-boto3-kinesis (>=1.36.0,<1.37.0)", "mypy-boto3-kinesis-video-archived-media (>=1.36.0,<1.37.0)", "mypy-boto3-kinesis-video-media (>=1.36.0,<1.37.0)", "mypy-boto3-kinesis-video-signaling (>=1.36.0,<1.37.0)", "mypy-boto3-kinesis-video-webrtc-storage (>=1.36.0,<1.37.0)", "mypy-boto3-kinesisanalytics (>=1.36.0,<1.37.0)", "mypy-boto3-kinesisanalyticsv2 (>=1.36.0,<1.37.0)", "mypy-boto3-kinesisvideo (>=1.36.0,<1.37.0)", "mypy-boto3-kms (>=1.36.0,<1.37.0)", "mypy-boto3-lakeformation (>=1.36.0,<1.37.0)", "mypy-boto3-lambda (>=1.36.0,<1.37.0)", "mypy-boto3-launch-wizard (>=1.36.0,<1.37.0)", "mypy-boto3-lex-models (>=1.36.0,<1.37.0)", "mypy-boto3-lex-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-lexv2-models (>=1.36.0,<1.37.0)", "mypy-boto3-lexv2-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-license-manager (>=1.36.0,<1.37.0)", "mypy-boto3-license-manager-linux-subscriptions (>=1.36.0,<1.37.0)", "mypy-boto3-license-manager-user-subscriptions (>=1.36.0,<1.37.0)", "mypy-boto3-lightsail (>=1.36.0,<1.37.0)", "mypy-boto3-location (>=1.36.0,<1.37.0)", "mypy-boto3-logs (>=1.36.0,<1.37.0)", "mypy-boto3-lookoutequipment (>=1.36.0,<1.37.0)", "mypy-boto3-lookoutmetrics (>=1.36.0,<1.37.0)", "mypy-boto3-lookoutvision (>=1.36.0,<1.37.0)", "mypy-boto3-m2 (>=1.36.0,<1.37.0)", "mypy-boto3-machinelearning (>=1.36.0,<1.37.0)", "mypy-boto3-macie2 (>=1.36.0,<1.37.0)", "mypy-boto3-mailmanager (>=1.36.0,<1.37.0)", "mypy-boto3-managedblockchain (>=1.36.0,<1.37.0)", "mypy-boto3-managedblockchain-query (>=1.36.0,<1.37.0)", "mypy-boto3-marketplace-agreement (>=1.36.0,<1.37.0)", "mypy-boto3-marketplace-catalog (>=1.36.0,<1.37.0)", "mypy-boto3-marketplace-deployment (>=1.36.0,<1.37.0)", "mypy-boto3-marketplace-entitlement (>=1.36.0,<1.37.0)", "mypy-boto3-marketplace-reporting (>=1.36.0,<1.37.0)", "mypy-boto3-marketplacecommerceanalytics (>=1.36.0,<1.37.0)", "mypy-boto3-mediaconnect (>=1.36.0,<1.37.0)", "mypy-boto3-mediaconvert (>=1.36.0,<1.37.0)", "mypy-boto3-medialive (>=1.36.0,<1.37.0)", "mypy-boto3-mediapackage (>=1.36.0,<1.37.0)", "mypy-boto3-mediapackage-vod (>=1.36.0,<1.37.0)", "mypy-boto3-mediapackagev2 (>=1.36.0,<1.37.0)", "mypy-boto3-mediastore (>=1.36.0,<1.37.0)", "mypy-boto3-mediastore-data (>=1.36.0,<1.37.0)", "mypy-boto3-mediatailor (>=1.36.0,<1.37.0)", "mypy-boto3-medical-imaging (>=1.36.0,<1.37.0)", "mypy-boto3-memorydb (>=1.36.0,<1.37.0)", "mypy-boto3-meteringmarketplace (>=1.36.0,<1.37.0)", "mypy-boto3-mgh (>=1.36.0,<1.37.0)", "mypy-boto3-mgn (>=1.36.0,<1.37.0)", "mypy-boto3-migration-hub-refactor-spaces (>=1.36.0,<1.37.0)", "mypy-boto3-migrationhub-config (>=1.36.0,<1.37.0)", "mypy-boto3-migrationhuborchestrator (>=1.36.0,<1.37.0)", "mypy-boto3-migrationhubstrategy (>=1.36.0,<1.37.0)", "mypy-boto3-mq (>=1.36.0,<1.37.0)", "mypy-boto3-mturk (>=1.36.0,<1.37.0)", "mypy-boto3-mwaa (>=1.36.0,<1.37.0)", "mypy-boto3-neptune (>=1.36.0,<1.37.0)", "mypy-boto3-neptune-graph (>=1.36.0,<1.37.0)", "mypy-boto3-neptunedata (>=1.36.0,<1.37.0)", "mypy-boto3-network-firewall (>=1.36.0,<1.37.0)", "mypy-boto3-networkflowmonitor (>=1.36.0,<1.37.0)", "mypy-boto3-networkmanager (>=1.36.0,<1.37.0)", "mypy-boto3-networkmonitor (>=1.36.0,<1.37.0)", "mypy-boto3-notifications (>=1.36.0,<1.37.0)", "mypy-boto3-notificationscontacts (>=1.36.0,<1.37.0)", "mypy-boto3-oam (>=1.36.0,<1.37.0)", "mypy-boto3-observabilityadmin (>=1.36.0,<1.37.0)", "mypy-boto3-omics (>=1.36.0,<1.37.0)", "mypy-boto3-opensearch (>=1.36.0,<1.37.0)", "mypy-boto3-opensearchserverless (>=1.36.0,<1.37.0)", "mypy-boto3-opsworks (>=1.36.0,<1.37.0)", "mypy-boto3-opsworkscm (>=1.36.0,<1.37.0)", "mypy-boto3-organizations (>=1.36.0,<1.37.0)", "mypy-boto3-osis (>=1.36.0,<1.37.0)", "mypy-boto3-outposts (>=1.36.0,<1.37.0)", "mypy-boto3-panorama (>=1.36.0,<1.37.0)", "mypy-boto3-partnercentral-selling (>=1.36.0,<1.37.0)", "mypy-boto3-payment-cryptography (>=1.36.0,<1.37.0)", "mypy-boto3-payment-cryptography-data (>=1.36.0,<1.37.0)", "mypy-boto3-pca-connector-ad (>=1.36.0,<1.37.0)", "mypy-boto3-pca-connector-scep (>=1.36.0,<1.37.0)", "mypy-boto3-pcs (>=1.36.0,<1.37.0)", "mypy-boto3-personalize (>=1.36.0,<1.37.0)", "mypy-boto3-personalize-events (>=1.36.0,<1.37.0)", "mypy-boto3-personalize-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-pi (>=1.36.0,<1.37.0)", "mypy-boto3-pinpoint (>=1.36.0,<1.37.0)", "mypy-boto3-pinpoint-email (>=1.36.0,<1.37.0)", "mypy-boto3-pinpoint-sms-voice (>=1.36.0,<1.37.0)", "mypy-boto3-pinpoint-sms-voice-v2 (>=1.36.0,<1.37.0)", "mypy-boto3-pipes (>=1.36.0,<1.37.0)", "mypy-boto3-polly (>=1.36.0,<1.37.0)", "mypy-boto3-pricing (>=1.36.0,<1.37.0)", "mypy-boto3-privatenetworks (>=1.36.0,<1.37.0)", "mypy-boto3-proton (>=1.36.0,<1.37.0)", "mypy-boto3-qapps (>=1.36.0,<1.37.0)", "mypy-boto3-qbusiness (>=1.36.0,<1.37.0)", "mypy-boto3-qconnect (>=1.36.0,<1.37.0)", "mypy-boto3-qldb (>=1.36.0,<1.37.0)", "mypy-boto3-qldb-session (>=1.36.0,<1.37.0)", "mypy-boto3-quicksight (>=1.36.0,<1.37.0)", "mypy-boto3-ram (>=1.36.0,<1.37.0)", "mypy-boto3-rbin (>=1.36.0,<1.37.0)", "mypy-boto3-rds (>=1.36.0,<1.37.0)", "mypy-boto3-rds-data (>=1.36.0,<1.37.0)", "mypy-boto3-redshift (>=1.36.0,<1.37.0)", "mypy-boto3-redshift-data (>=1.36.0,<1.37.0)", "mypy-boto3-redshift-serverless (>=1.36.0,<1.37.0)", "mypy-boto3-rekognition (>=1.36.0,<1.37.0)", "mypy-boto3-repostspace (>=1.36.0,<1.37.0)", "mypy-boto3-resiliencehub (>=1.36.0,<1.37.0)", "mypy-boto3-resource-explorer-2 (>=1.36.0,<1.37.0)", "mypy-boto3-resource-groups (>=1.36.0,<1.37.0)", "mypy-boto3-resourcegroupstaggingapi (>=1.36.0,<1.37.0)", "mypy-boto3-robomaker (>=1.36.0,<1.37.0)", "mypy-boto3-rolesanywhere (>=1.36.0,<1.37.0)", "mypy-boto3-route53 (>=1.36.0,<1.37.0)", "mypy-boto3-route53-recovery-cluster (>=1.36.0,<1.37.0)", "mypy-boto3-route53-recovery-control-config (>=1.36.0,<1.37.0)", "mypy-boto3-route53-recovery-readiness (>=1.36.0,<1.37.0)", "mypy-boto3-route53domains (>=1.36.0,<1.37.0)", "mypy-boto3-route53profiles (>=1.36.0,<1.37.0)", "mypy-boto3-route53resolver (>=1.36.0,<1.37.0)", "mypy-boto3-rum (>=1.36.0,<1.37.0)", "mypy-boto3-s3 (>=1.36.0,<1.37.0)", "mypy-boto3-s3control (>=1.36.0,<1.37.0)", "mypy-boto3-s3outposts (>=1.36.0,<1.37.0)", "mypy-boto3-s3tables (>=1.36.0,<1.37.0)", "mypy-boto3-sagemaker (>=1.36.0,<1.37.0)", "mypy-boto3-sagemaker-a2i-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-sagemaker-edge (>=1.36.0,<1.37.0)", "mypy-boto3-sagemaker-featurestore-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-sagemaker-geospatial (>=1.36.0,<1.37.0)", "mypy-boto3-sagemaker-metrics (>=1.36.0,<1.37.0)", "mypy-boto3-sagemaker-runtime (>=1.36.0,<1.37.0)", "mypy-boto3-savingsplans (>=1.36.0,<1.37.0)", "mypy-boto3-scheduler (>=1.36.0,<1.37.0)", "mypy-boto3-schemas (>=1.36.0,<1.37.0)", "mypy-boto3-sdb (>=1.36.0,<1.37.0)", "mypy-boto3-secretsmanager (>=1.36.0,<1.37.0)", "mypy-boto3-security-ir (>=1.36.0,<1.37.0)", "mypy-boto3-securityhub (>=1.36.0,<1.37.0)", "mypy-boto3-securitylake (>=1.36.0,<1.37.0)", "mypy-boto3-serverlessrepo (>=1.36.0,<1.37.0)", "mypy-boto3-service-quotas (>=1.36.0,<1.37.0)", "mypy-boto3-servicecatalog (>=1.36.0,<1.37.0)", "mypy-boto3-servicecatalog-appregistry (>=1.36.0,<1.37.0)", "mypy-boto3-servicediscovery (>=1.36.0,<1.37.0)", "mypy-boto3-ses (>=1.36.0,<1.37.0)", "mypy-boto3-sesv2 (>=1.36.0,<1.37.0)", "mypy-boto3-shield (>=1.36.0,<1.37.0)", "mypy-boto3-signer (>=1.36.0,<1.37.0)", "mypy-boto3-simspaceweaver (>=1.36.0,<1.37.0)", "mypy-boto3-sms (>=1.36.0,<1.37.0)", "mypy-boto3-sms-voice (>=1.36.0,<1.37.0)", "mypy-boto3-snow-device-management (>=1.36.0,<1.37.0)", "mypy-boto3-snowball (>=1.36.0,<1.37.0)", "mypy-boto3-sns (>=1.36.0,<1.37.0)", "mypy-boto3-socialmessaging (>=1.36.0,<1.37.0)", "mypy-boto3-sqs (>=1.36.0,<1.37.0)", "mypy-boto3-ssm (>=1.36.0,<1.37.0)", "mypy-boto3-ssm-contacts (>=1.36.0,<1.37.0)", "mypy-boto3-ssm-incidents (>=1.36.0,<1.37.0)", "mypy-boto3-ssm-quicksetup (>=1.36.0,<1.37.0)", "mypy-boto3-ssm-sap (>=1.36.0,<1.37.0)", "mypy-boto3-sso (>=1.36.0,<1.37.0)", "mypy-boto3-sso-admin (>=1.36.0,<1.37.0)", "mypy-boto3-sso-oidc (>=1.36.0,<1.37.0)", "mypy-boto3-stepfunctions (>=1.36.0,<1.37.0)", "mypy-boto3-storagegateway (>=1.36.0,<1.37.0)", "mypy-boto3-sts (>=1.36.0,<1.37.0)", "mypy-boto3-supplychain (>=1.36.0,<1.37.0)", "mypy-boto3-support (>=1.36.0,<1.37.0)", "mypy-boto3-support-app (>=1.36.0,<1.37.0)", "mypy-boto3-swf (>=1.36.0,<1.37.0)", "mypy-boto3-synthetics (>=1.36.0,<1.37.0)", "mypy-boto3-taxsettings (>=1.36.0,<1.37.0)", "mypy-boto3-textract (>=1.36.0,<1.37.0)", "mypy-boto3-timestream-influxdb (>=1.36.0,<1.37.0)", "mypy-boto3-timestream-query (>=1.36.0,<1.37.0)", "mypy-boto3-timestream-write (>=1.36.0,<1.37.0)", "mypy-boto3-tnb (>=1.36.0,<1.37.0)", "mypy-boto3-transcribe (>=1.36.0,<1.37.0)", "mypy-boto3-transfer (>=1.36.0,<1.37.0)", "mypy-boto3-translate (>=1.36.0,<1.37.0)", "mypy-boto3-trustedadvisor (>=1.36.0,<1.37.0)", "mypy-boto3-verifiedpermissions (>=1.36.0,<1.37.0)", "mypy-boto3-voice-id (>=1.36.0,<1.37.0)", "mypy-boto3-vpc-lattice (>=1.36.0,<1.37.0)", "mypy-boto3-waf (>=1.36.0,<1.37.0)", "mypy-boto3-waf-regional (>=1.36.0,<1.37.0)", "mypy-boto3-wafv2 (>=1.36.0,<1.37.0)", "mypy-boto3-wellarchitected (>=1.36.0,<1.37.0)", "mypy-boto3-wisdom (>=1.36.0,<1.37.0)", "mypy-boto3-workdocs (>=1.36.0,<1.37.0)", "mypy-boto3-workmail (>=1.36.0,<1.37.0)", "mypy-boto3-workmailmessageflow (>=1.36.0,<1.37.0)", "mypy-boto3-workspaces (>=1.36.0,<1.37.0)", "mypy-boto3-workspaces-thin-client (>=1.36.0,<1.37.0)", "mypy-boto3-workspaces-web (>=1.36.0,<1.37.0)", "mypy-boto3-xray (>=1.36.0,<1.37.0)"]
amp = ["mypy-boto3-amp (>=1.36.0,<1.37.0)"]
amplify = ["mypy-boto3-amplify (>=1.36.0,<1.37.0)"]
amplifybackend = ["mypy-boto3-amplifybackend (>=1.36.0,<1.37.0)"]
amplifyuibuilder = ["mypy-boto3-amplifyuibuilder (>=1.36.0,<1.37.0)"]
apigateway = ["mypy-boto3-apigateway (>=1.36.0,<1.37.0)"]
apigatewaymanagementapi = ["mypy-boto3-apigatewaymanagementapi (>=1.36.0,<1.37.0)"]
apigatewayv2 = ["mypy-boto3-apigatewayv2 (>=1.36.0,<1.37.0)"]
appconfig = ["mypy-boto3-appconfig (>=1.36.0,<1.37.0)"]
appconfigdata = ["mypy-boto3-appconfigdata (>=1.36.0,<1.37.0)"]
appfabric = ["mypy-boto3-appfabric (>=1.36.0,<1.37.0)"]
appflow = ["mypy-boto3-appflow (>=1.36.0,<1.37.0)"]
appintegrations = ["mypy-boto3-appintegrations (>=1.36.0,<1.37.0)"]
application-autoscaling = ["mypy-boto3-application-autoscaling (>=1.36.0,<1.37.0)"]
application-insights = ["mypy-boto3-application-insights (>=1.36.0,<1.37.0)"]
application-signals = ["mypy-boto3-application-signals (>=1.36.0,<1.37.0)"]
applicationcostprofiler = ["mypy-boto3-applicationcostprofiler (>=1.36.0,<1.37.0)"]
appmesh = ["mypy-boto3-appmesh (>=1.36.0,<1.37.0)"]
apprunner = ["mypy-boto3-apprunner (>=1.36.0,<1.37.0)"]
appstream = ["mypy-boto3-appstream (>=1.36.0,<1.37.0)"]
appsync = ["mypy-boto3-appsync (>=1.36.0,<1.37.0)"]
apptest = ["mypy-boto3-apptest (>=1.36.0,<1.37.0)"]
arc-zonal-shift = ["mypy-boto3-arc-zonal-shift (>=1.36.0,<1.37.0)"]
artifact = ["mypy-boto3-artifact (>=1.36.0,<1.37.0)"]
athena = ["mypy-boto3-athena (>=1.36.0,<1.37.0)"]
auditmanager = ["mypy-boto3-auditmanager (>=1.36.0,<1.37.0)"]
autoscaling = ["mypy-boto3-autoscaling (>=1.36.0,<1.37.0)"]
autoscaling-plans = ["mypy-boto3-autoscaling-plans (>=1.36.0,<1.37.0)"]
b2bi = ["mypy-boto3-b2bi (>=1.36.0,<1.37.0)"]
backup = ["mypy-boto3-backup (>=1.36.0,<1.37.0)"]
backup-gateway = ["mypy-boto3-backup-gateway (>=1.36.0,<1.37.0)"]
backupsearch = ["mypy-boto3-backupsearch (>=1.36.0,<1.37.0)"]
batch = ["mypy-boto3-batch (>=1.36.0,<1.37.0)"]
bcm-data-exports = ["mypy-boto3-bcm-data-exports (>=1.36.0,<1.37.0)"]
bcm-pricing-calculator = ["mypy-boto3-bcm-pricing-calculator (>=1.36.0,<1.37.0)"]
bedrock = ["mypy-boto3-bedrock (>=1.36.0,<1.37.0)"]
bedrock-agent = ["mypy-boto3-bedrock-agent (>=1.36.0,<1.37.0)"]
bedrock-agent-runtime = ["mypy-boto3-bedrock-agent-runtime (>=1.36.0,<1.37.0)"]
bedrock-data-automation = ["mypy-boto3-bedrock-data-automation (>=1.36.0,<1.37.0)"]
bedrock-data-automation-runtime = ["mypy-boto3-bedrock-data-automation-runtime (>=1.36.0,<1.37.0)"]
bedrock-runtime = ["mypy-boto3-bedrock-runtime (>=1.36.0,<1.37.0)"]
billing = ["mypy-boto3-billing (>=1.36.0,<1.37.0)"]
billingconductor = ["mypy-boto3-billingconductor (>=1.36.0,<1.37.0)"]
boto3 = ["boto3 (==1.36.26)"]
braket = ["mypy-boto3-braket (>=1.36.0,<1.37.0)"]
budgets = ["mypy-boto3-budgets (>=1.36.0,<1.37.0)"]
ce = ["mypy-boto3-ce (>=1.36.0,<1.37.0)"]
chatbot = ["mypy-boto3-chatbot (>=1.36.0,<1.37.0)"]
chime = ["mypy-boto3-chime (>=1.36.0,<1.37.0)"]
chime-sdk-identity = ["mypy-boto3-chime-sdk-identity (>=1.36.0,<1.37.0)"]
chime-sdk-media-pipelines = ["mypy-boto3-chime-sdk-media-pipelines (>=1.36.0,<1.37.0)"]
chime-sdk-meetings = ["mypy-boto3-chime-sdk-meetings (>=1.36.0,<1.37.0)"]
chime-sdk-messaging = ["mypy-boto3-chime-sdk-messaging (>=1.36.0,<1.37.0)"]
chime-sdk-voice = ["mypy-boto3-chime-sdk-voice (>=1.36.0,<1.37.0)"]
cleanrooms = ["mypy-boto3-cleanrooms (>=1.36.0,<1.37.0)"]
cleanroomsml = ["mypy-boto3-cleanroomsml (>=1.36.0,<1.37.0)"]
cloud9 = ["mypy-boto3-cloud9 (>=1.36.0,<1.37.0)"]
cloudcontrol = ["mypy-boto3-cloudcontrol (>=1.36.0,<1.37.0)"]
clouddirectory = ["mypy-boto3-clouddirectory (>=1.36.0,<1.37.0)"]
cloudformation = ["mypy-boto3-cloudformation (>=1.36.0,<1.37.0)"]
cloudfront = ["mypy-boto3-cloudfront (>=1.36.0,<1.37.0)"]
cloudfront-keyvaluestore = ["mypy-boto3-cloudfront-keyvaluestore (>=1.36.0,<1.37.0)"]
cloudhsm = ["mypy-boto3-cloudhsm (>=1.36.0,<1.37.0)"]
cloudhsmv2 = ["mypy-boto3-cloudhsmv2 (>=1.36.0,<1.37.0)"]
cloudsearch = ["mypy-boto3-cloudsearch (>=1.36.0,<1.37.0)"]
cloudsearchdomain = ["mypy-boto3-cloudsearchdomain (>=1.36.0,<1.37.0)"]
cloudtrail = ["mypy-boto3-cloudtrail (>=1.36.0,<1.37.0)"]
cloudtrail-data = ["mypy-boto3-cloudtrail-data (>=1.36.0,<1.37.0)"]
cloudwatch = ["mypy-boto3-cloudwatch (>=1.36.0,<1.37.0)"]
codeartifact = ["mypy-boto3-codeartifact (>=1.36.0,<1.37.0)"]
codebuild = ["mypy-boto3-codebuild (>=1.36.0,<1.37.0)"]
codecatalyst = ["mypy-boto3-codecatalyst (>=1.36.0,<1.37.0)"]
codecommit = ["mypy-boto3-codecommit (>=1.36.0,<1.37.0)"]
codeconnections = ["mypy-boto3-codeconnections (>=1.36.0,<1.37.0)"]
codedeploy = ["mypy-boto3-codedeploy (>=1.36.0,<1.37.0)"]
codeguru-reviewer = ["mypy-boto3-codeguru-reviewer (>=1.36.0,<1.37.0)"]
codeguru-security = ["mypy-boto3-codeguru-security (>=1.36.0,<1.37.0)"]
codeguruprofiler = ["mypy-boto3-codeguruprofiler (>=1.36.0,<1.37.0)"]
codepipeline = ["mypy-boto3-codepipeline (>=1.36.0,<1.37.0)"]
codestar-connections = ["mypy-boto3-codestar-connections (>=1.36.0,<1.37.0)"]
codestar-notifications = ["mypy-boto3-codestar-notifications (>=1.36.0,<1.37.0)"]
cognito-identity = ["mypy-boto3-cognito-identity (>=1.36.0,<1.37.0)"]
cognito-idp = ["mypy-boto3-cognito-idp (>=1.36.0,<1.37.0)"]
cognito-sync = ["mypy-boto3-cognito-sync (>=1.36.0,<1.37.0)"]
comprehend = ["mypy-boto3-comprehend (>=1.36.0,<1.37.0)"]
comprehendmedical = ["mypy-boto3-comprehendmedical (>=1.36.0,<1.37.0)"]
compute-optimizer = ["mypy-boto3-compute-optimizer (>=1.36.0,<1.37.0)"]
config = ["mypy-boto3-config (>=1.36.0,<1.37.0)"]
connect = ["mypy-boto3-connect (>=1.36.0,<1.37.0)"]
connect-contact-lens = ["mypy-boto3-connect-contact-lens (>=1.36.0,<1.37.0)"]
connectcampaigns = ["mypy-boto3-connectcampaigns (>=1.36.0,<1.37.0)"]
connectcampaignsv2 = ["mypy-boto3-connectcampaignsv2 (>=1.36.0,<1.37.0)"]
connectcases = ["mypy-boto3-connectcases (>=1.36.0,<1.37.0)"]
connectparticipant = ["mypy-boto3-connectparticipant (>=1.36.0,<1.37.0)"]
controlcatalog = ["mypy-boto3-controlcatalog (>=1.36.0,<1.37.0)"]
controltower = ["mypy-boto3-controltower (>=1.36.0,<1.37.0)"]
cost-optimization-hub = ["mypy-boto3-cost-optimization-hub (>=1.36.0,<1.37.0)"]
cur = ["mypy-boto3-cur (>=1.36.0,<1.37.0)"]
customer-profiles = ["mypy-boto3-customer-profiles (>=1.36.0,<1.37.0)"]
databrew = ["mypy-boto3-databrew (>=1.36.0,<1.37.0)"]
dataexchange = ["mypy-boto3-dataexchange (>=1.36.0,<1.37.0)"]
datapipeline = ["mypy-boto3-datapipeline (>=1.36.0,<1.37.0)"]
datasync = ["mypy-boto3-datasync (>=1.36.0,<1.37.0)"]
datazone = ["mypy-boto3-datazone (>=1.36.0,<1.37.0)"]
dax = ["mypy-boto3-dax (>=1.36.0,<1.37.0)"]
deadline = ["mypy-boto3-deadline (>=1.36.0,<1.37.0)"]
detective = ["mypy-boto3-detective (>=1.36.0,<1.37.0)"]
devicefarm = ["mypy-boto3-devicefarm (>=1.36.0,<1.37.0)"]
devops-guru = ["mypy-boto3-devops-guru (>=1.36.0,<1.37.0)"]
directconnect = ["mypy-boto3-directconnect (>=1.36.0,<1.37.0)"]
discovery = ["mypy-boto3-discovery (>=1.36.0,<1.37.0)"]
dlm = ["mypy-boto3-dlm (>=1.36.0,<1.37.0)"]
dms = ["mypy-boto3-dms (>=1.36.0,<1.37.0)"]
docdb = ["mypy-boto3-docdb (>=1.36.0,<1.37.0)"]
docdb-elastic = ["mypy-boto3-docdb-elastic (>=1.36.0,<1.37.0)"]
drs = ["mypy-boto3-drs (>=1.36.0,<1.37.0)"]
ds = ["mypy-boto3-ds (>=1.36.0,<1.37.0)"]
ds-data = ["mypy-boto3-ds-data (>=1.36.0,<1.37.0)"]
dsql = ["mypy-boto3-dsql (>=1.36.0,<1.37.0)"]
dynamodb = ["mypy-boto3-dynamodb (>=1.36.0,<1.37.0)"]
dynamodbstreams = ["mypy-boto3-dynamodbstreams (>=1.36.0,<1.37.0)"]
ebs = ["mypy-boto3-ebs (>=1.36.0,<1.37.0)"]
ec2 = ["mypy-boto3-ec2 (>=1.36.0,<1.37.0)"]
ec2-instance-connect = ["mypy-boto3-ec2-instance-connect (>=1.36.0,<1.37.0)"]
ecr = ["mypy-boto3-ecr (>=1.36.0,<1.37.0)"]
ecr-public = ["mypy-boto3-ecr-public (>=1.36.0,<1.37.0)"]
ecs = ["mypy-boto3-ecs (>=1.36.0,<1.37.0)"]
efs = ["mypy-boto3-efs (>=1.36.0,<1.37.0)"]
eks = ["mypy-boto3-eks (>=1.36.0,<1.37.0)"]
eks-auth = ["mypy-boto3-eks-auth (>=1.36.0,<1.37.0)"]
elastic-inference = ["mypy-boto3-elastic-inference (>=1.36.0,<1.37.0)"]
elasticache = ["mypy-boto3-elasticache (>=1.36.0,<1.37.0)"]
elasticbeanstalk = ["mypy-boto3-elasticbeanstalk (>=1.36.0,<1.37.0)"]
elastictranscoder = ["mypy-boto3-elastictranscoder (>=1.36.0,<1.37.0)"]
elb = ["mypy-boto3-elb (>=1.36.0,<1.37.0)"]
elbv2 = ["mypy-boto3-elbv2 (>=1.36.0,<1.37.0)"]
emr = ["mypy-boto3-emr (>=1.36.0,<1.37.0)"]
emr-containers = ["mypy-boto3-emr-containers (>=1.36.0,<1.37.0)"]
emr-serverless = ["mypy-boto3-emr-serverless (>=1.36.0,<1.37.0)"]
entityresolution = ["mypy-boto3-entityresolution (>=1.36.0,<1.37.0)"]
es = ["mypy-boto3-es (>=1.36.0,<1.37.0)"]
essential = ["mypy-boto3-cloudformation (>=1.36.0,<1.37.0)", "mypy-boto3-dynamodb (>=1.36.0,<1.37.0)", "mypy-boto3-ec2 (>=1.36.0,<1.37.0)", "mypy-boto3-lambda (>=1.36.0,<1.37.0)", "mypy-boto3-rds (>=1.36.0,<1.37.0)", "mypy-boto3-s3 (>=1.36.0,<1.37.0)", "mypy-boto3-sqs (>=1.36.0,<1.37.0)"]
events = ["mypy-boto3-events (>=1.36.0,<1.37.0)"]
evidently = ["mypy-boto3-evidently (>=1.36.0,<1.37.0)"]
finspace = ["mypy-boto3-finspace (>=1.36.0,<1.37.0)"]
finspace-data = ["mypy-boto3-finspace-data (>=1.36.0,<1.37.0)"]
firehose = ["mypy-boto3-firehose (>=1.36.0,<1.37.0)"]
fis = ["mypy-boto3-fis (>=1.36.0,<1.37.0)"]
fms = ["mypy-boto3-fms (>=1.36.0,<1.37.0)"]
forecast = ["mypy-boto3-forecast (>=1.36.0,<1.37.0)"]
forecastquery = ["mypy-boto3-forecastquery (>=1.36.0,<1.37.0)"]
frauddetector = ["mypy-boto3-frauddetector (>=1.36.0,<1.37.0)"]
freetier = ["mypy-boto3-freetier (>=1.36.0,<1.37.0)"]
fsx = ["mypy-boto3-fsx (>=1.36.0,<1.37.0)"]
full = ["boto3-stubs-full (>=1.36.0,<1.37.0)"]
gamelift = ["mypy-boto3-gamelift (>=1.36.0,<1.37.0)"]
geo-maps = ["mypy-boto3-geo-maps (>=1.36.0,<1.37.0)"]
geo-places = ["mypy-boto3-geo-places (>=1.36.0,<1.37.0)"]
geo-routes = ["mypy-boto3-geo-routes (>=1.36.0,<1.37.0)"]
glacier = ["mypy-boto3-glacier (>=1.36.0,<1.37.0)"]
globalaccelerator = ["mypy-boto3-globalaccelerator (>=1.36.0,<1.37.0)"]
glue = ["mypy-boto3-glue (>=1.36.0,<1.37.0)"]
grafana = ["mypy-boto3-grafana (>=1.36.0,<1.37.0)"]
greengrass = ["mypy-boto3-greengrass (>=1.36.0,<1.37.0)"]
greengrassv2 = ["mypy-boto3-greengrassv2 (>=1.36.0,<1.37.0)"]
groundstation = ["mypy-boto3-groundstation (>=1.36.0,<1.37.0)"]
guardduty = ["mypy-boto3-guardduty (>=1.36.0,<1.37.0)"]
health = ["mypy-boto3-health (>=1.36.0,<1.37.0)"]
healthlake = ["mypy-boto3-healthlake (>=1.36.0,<1.37.0)"]
iam = ["mypy-boto3-iam (>=1.36.0,<1.37.0)"]
identitystore = ["mypy-boto3-identitystore (>=1.36.0,<1.37.0)"]
imagebuilder = ["mypy-boto3-imagebuilder (>=1.36.0,<1.37.0)"]
importexport = ["mypy-boto3-importexport (>=1.36.0,<1.37.0)"]
inspector = ["mypy-boto3-inspector (>=1.36.0,<1.37.0)"]
inspector-scan = ["mypy-boto3-inspector-scan (>=1.36.0,<1.37.0)"]
inspector2 = ["mypy-boto3-inspector2 (>=1.36.0,<1.37.0)"]
internetmonitor = ["mypy-boto3-internetmonitor (>=1.36.0,<1.37.0)"]
invoicing = ["mypy-boto3-invoicing (>=1.36.0,<1.37.0)"]
iot = ["mypy-boto3-iot (>=1.36.0,<1.37.0)"]
iot-data = ["mypy-boto3-iot-data (>=1.36.0,<1.37.0)"]
iot-jobs-data = ["mypy-boto3-iot-jobs-data (>=1.36.0,<1.37.0)"]
iotanalytics = ["mypy-boto3-iotanalytics (>=1.36.0,<1.37.0)"]
iotdeviceadvisor = ["mypy-boto3-iotdeviceadvisor (>=1.36.0,<1.37.0)"]
iotevents = ["mypy-boto3-iotevents (>=1.36.0,<1.37.0)"]
iotevents-data = ["mypy-boto3-iotevents-data (>=1.36.0,<1.37.0)"]
iotfleethub = ["mypy-boto3-iotfleethub (>=1.36.0,<1.37.0)"]
iotfleetwise = ["mypy-boto3-iotfleetwise (>=1.36.0,<1.37.0)"]
iotsecuretunneling = ["mypy-boto3-iotsecuretunneling (>=1.36.0,<1.37.0)"]
iotsitewise = ["mypy-boto3-iotsitewise (>=1.36.0,<1.37.0)"]
iotthingsgraph = ["mypy-boto3-iotthingsgraph (>=1.36.0,<1.37.0)"]
iottwinmaker = ["mypy-boto3-iottwinmaker (>=1.36.0,<1.37.0)"]
iotwireless = ["mypy-boto3-iotwireless (>=1.36.0,<1.37.0)"]
ivs = ["mypy-boto3-ivs (>=1.36.0,<1.37.0)"]
ivs-realtime = ["mypy-boto3-ivs-realtime (>=1.36.0,<1.37.0)"]
ivschat = ["mypy-boto3-ivschat (>=1.36.0,<1.37.0)"]
kafka = ["mypy-boto3-kafka (>=1.36.0,<1.37.0)"]
kafkaconnect = ["mypy-boto3-kafkaconnect (>=1.36.0,<1.37.0)"]
kendra = ["mypy-boto3-kendra (>=1.36.0,<1.37.0)"]
kendra-ranking = ["mypy-boto3-kendra-ranking (>=1.36.0,<1.37.0)"]
keyspaces = ["mypy-boto3-keyspaces (>=1.36.0,<1.37.0)"]
kinesis = ["mypy-boto3-kinesis (>=1.36.0,<1.37.0)"]
kinesis-video-archived-media = ["mypy-boto3-kinesis-video-archived-media (>=1.36.0,<1.37.0)"]
kinesis-video-media = ["mypy-boto3-kinesis-video-media (>=1.36.0,<1.37.0)"]
kinesis-video-signaling = ["mypy-boto3-kinesis-video-signaling (>=1.36.0,<1.37.0)"]
kinesis-video-webrtc-storage = ["mypy-boto3-kinesis-video-webrtc-storage (>=1.36.0,<1.37.0)"]
kinesisanalytics = ["mypy-boto3-kinesisanalytics (>=1.36.0,<1.37.0)"]
kinesisanalyticsv2 = ["mypy-boto3-kinesisanalyticsv2 (>=1.36.0,<1.37.0)"]
kinesisvideo = ["mypy-boto3-kinesisvideo (>=1.36.0,<1.37.0)"]
kms = ["mypy-boto3-kms (>=1.36.0,<1.37.0)"]
lakeformation = ["mypy-boto3-lakeformation (>=1.36.0,<1.37.0)"]
lambda = ["mypy-boto3-lambda (>=1.36.0,<1.37.0)"]
launch-wizard = ["mypy-boto3-launch-wizard (>=1.36.0,<1.37.0)"]
lex-models = ["mypy-boto3-lex-models (>=1.36.0,<1.37.0)"]
lex-runtime = ["mypy-boto3-lex-runtime (>=1.36.0,<1.37.0)"]
lexv2-models = ["mypy-boto3-lexv2-models (>=1.36.0,<1.37.0)"]
lexv2-runtime = ["mypy-boto3-lexv2-runtime (>=1.36.0,<1.37.0)"]
license-manager = ["mypy-boto3-license-manager (>=1.36.0,<1.37.0)"]
license-manager-linux-subscriptions = ["mypy-boto3-license-manager-linux-subscriptions (>=1.36.0,<1.37.0)"]
license-manager-user-subscriptions = ["mypy-boto3-license-manager-user-subscriptions (>=1.36.0,<1.37.0)"]
lightsail = ["mypy-boto3-lightsail (>=1.36.0,<1.37.0)"]
location = ["mypy-boto3-location (>=1.36.0,<1.37.0)"]
logs = ["mypy-boto3-logs (>=1.36.0,<1.37.0)"]
lookoutequipment = ["mypy-boto3-lookoutequipment (>=1.36.0,<1.37.0)"]
lookoutmetrics = ["mypy-boto3-lookoutmetrics (>=1.36.0,<1.37.0)"]
lookoutvision = ["mypy-boto3-lookoutvision (>=1.36.0,<1.37.0)"]
m2 = ["mypy-boto3-m2 (>=1.36.0,<1.37.0)"]
machinelearning = ["mypy-boto3-machinelearning (>=1.36.0,<1.37.0)"]
macie2 = ["mypy-boto3-macie2 (>=1.36.0,<1.37.0)"]
mailmanager = ["mypy-boto3-mailmanager (>=1.36.0,<1.37.0)"]
managedblockchain = ["mypy-boto3-managedblockchain (>=1.36.0,<1.37.0)"]
managedblockchain-query = ["mypy-boto3-managedblockchain-query (>=1.36.0,<1.37.0)"]
marketplace-agreement = ["mypy-boto3-marketplace-agreement (>=1.36.0,<1.37.0)"]
marketplace-catalog = ["mypy-boto3-marketplace-catalog (>=1.36.0,<1.37.0)"]
marketplace-deployment = ["mypy-boto3-marketplace-deployment (>=1.36.0,<1.37.0)"]
marketplace-entitlement = ["mypy-boto3-marketplace-entitlement (>=1.36.0,<1.37.0)"]
marketplace-reporting = ["mypy-boto3-marketplace-reporting (>=1.36.0,<1.37.0)"]
marketplacecommerceanalytics = ["mypy-boto3-marketplacecommerceanalytics (>=1.36.0,<1.37.0)"]
mediaconnect = ["mypy-boto3-mediaconnect (>=1.36.0,<1.37.0)"]
mediaconvert = ["mypy-boto3-mediaconvert (>=1.36.0,<1.37.0)"]
medialive = ["mypy-boto3-medialive (>=1.36.0,<1.37.0)"]
mediapackage = ["mypy-boto3-mediapackage (>=1.36.0,<1.37.0)"]
mediapackage-vod = ["mypy-boto3-mediapackage-vod (>=1.36.0,<1.37.0)"]
mediapackagev2 = ["mypy-boto3-mediapackagev2 (>=1.36.0,<1.37.0)"]
mediastore = ["mypy-boto3-mediastore (>=1.36.0,<1.37.0)"]
mediastore-data = ["mypy-boto3-mediastore-data (>=1.36.0,<1.37.0)"]
mediatailor = ["mypy-boto3-mediatailor (>=1.36.0,<1.37.0)"]
medical-imaging = ["mypy-boto3-medical-imaging (>=1.36.0,<1.37.0)"]
memorydb = ["mypy-boto3-memorydb (>=1.36.0,<1.37.0)"]
meteringmarketplace = ["mypy-boto3-meteringmarketplace (>=1.36.0,<1.37.0)"]
mgh = ["mypy-boto3-mgh (>=1.36.0,<1.37.0)"]
mgn = ["mypy-boto3-mgn (>=1.36.0,<1.37.0)"]
migration-hub-refactor-spaces = ["mypy-boto3-migration-hub-refactor-spaces (>=1.36.0,<1.37.0)"]
migrationhub-config = ["mypy-boto3-migrationhub-config (>=1.36.0,<1.37.0)"]
migrationhuborchestrator = ["mypy-boto3-migrationhuborchestrator (>=1.36.0,<1.37.0)"]
migrationhubstrategy = ["mypy-boto3-migrationhubstrategy (>=1.36.0,<1.37.0)"]
mq = ["mypy-boto3-mq (>=1.36.0,<1.37.0)"]
mturk = ["mypy-boto3-mturk (>=1.36.0,<1.37.0)"]
mwaa = ["mypy-boto3-mwaa (>=1.36.0,<1.37.0)"]
neptune = ["mypy-boto3-neptune (>=1.36.0,<1.37.0)"]
neptune-graph = ["mypy-boto3-neptune-graph (>=1.36.0,<1.37.0)"]
neptunedata = ["mypy-boto3-neptunedata (>=1.36.0,<1.37.0)"]
network-firewall = ["mypy-boto3-network-firewall (>=1.36.0,<1.37.0)"]
networkflowmonitor = ["mypy-boto3-networkflowmonitor (>=1.36.0,<1.37.0)"]
networkmanager = ["mypy-boto3-networkmanager (>=1.36.0,<1.37.0)"]
networkmonitor = ["mypy-boto3-networkmonitor (>=1.36.0,<1.37.0)"]
notifications = ["mypy-boto3-notifications (>=1.36.0,<1.37.0)"]
notificationscontacts = ["mypy-boto3-notificationscontacts (>=1.36.0,<1.37.0)"]
oam = ["mypy-boto3-oam (>=1.36.0,<1.37.0)"]
observabilityadmin = ["mypy-boto3-observabilityadmin (>=1.36.0,<1.37.0)"]
omics = ["mypy-boto3-omics (>=1.36.0,<1.37.0)"]
opensearch = ["mypy-boto3-opensearch (>=1.36.0,<1.37.0)"]
opensearchserverless = ["mypy-boto3-opensearchserverless (>=1.36.0,<1.37.0)"]
opsworks = ["mypy-boto3-opsworks (>=1.36.0,<1.37.0)"]
opsworkscm = ["mypy-boto3-opsworkscm (>=1.36.0,<1.37.0)"]
organizations = ["mypy-boto3-organizations (>=1.36.0,<1.37.0)"]
osis = ["mypy-boto3-osis (>=1.36.0,<1.37.0)"]
outposts = ["mypy-boto3-outposts (>=1.36.0,<1.37.0)"]
panorama = ["mypy-boto3-panorama (>=1.36.0,<1.37.0)"]
partnercentral-selling = ["mypy-boto3-partnercentral-selling (>=1.36.0,<1.37.0)"]
payment-cryptography = ["mypy-boto3-payment-cryptography (>=1.36.0,<1.37.0)"]
payment-cryptography-data = ["mypy-boto3-payment-cryptography-data (>=1.36.0,<1.37.0)"]
pca-connector-ad = ["mypy-boto3-pca-connector-ad (>=1.36.0,<1.37.0)"]
pca-connector-scep = ["mypy-boto3-pca-connector-scep (>=1.36.0,<1.37.0)"]
pcs = ["mypy-boto3-pcs (>=1.36.0,<1.37.0)"]
personalize = ["mypy-boto3-personalize (>=1.36.0,<1.37.0)"]
personalize-events = ["mypy-boto3-personalize-events (>=1.36.0,<1.37.0)"]
personalize-runtime = ["mypy-boto3-personalize-runtime (>=1.36.0,<1.37.0)"]
pi = ["mypy-boto3-pi (>=1.36.0,<1.37.0)"]
pinpoint = ["mypy-boto3-pinpoint (>=1.36.0,<1.37.0)"]
pinpoint-email = ["mypy-boto3-pinpoint-email (>=1.36.0,<1.37.0)"]
pinpoint-sms-voice = ["mypy-boto3-pinpoint-sms-voice (>=1.36.0,<1.37.0)"]
pinpoint-sms-voice-v2 = ["mypy-boto3-pinpoint-sms-voice-v2 (>=1.36.0,<1.37.0)"]
pipes = ["mypy-boto3-pipes (>=1.36.0,<1.37.0)"]
polly = ["mypy-boto3-polly (>=1.36.0,<1.37.0)"]
pricing = ["mypy-boto3-pricing (>=1.36.0,<1.37.0)"]
privatenetworks = ["mypy-boto3-privatenetworks (>=1.36.0,<1.37.0)"]
proton = ["mypy-boto3-proton (>=1.36.0,<1.37.0)"]
qapps = ["mypy-boto3-qapps (>=1.36.0,<1.37.0)"]
qbusiness = ["mypy-boto3-qbusiness (>=1.36.0,<1.37.0)"]
qconnect = ["mypy-boto3-qconnect (>=1.36.0,<1.37.0)"]
qldb = ["mypy-boto3-qldb (>=1.36.0,<1.37.0)"]
qldb-session = ["mypy-boto3-qldb-session (>=1.36.0,<1.37.0)"]
quicksight = ["mypy-boto3-quicksight (>=1.36.0,<1.37.0)"]
ram = ["mypy-boto3-ram (>=1.36.0,<1.37.0)"]
rbin = ["mypy-boto3-rbin (>=1.36.0,<1.37.0)"]
rds = ["mypy-boto3-rds (>=1.36.0,<1.37.0)"]
rds-data = ["mypy-boto3-rds-data (>=1.36.0,<1.37.0)"]
redshift = ["mypy-boto3-redshift (>=1.36.0,<1.37.0)"]
redshift-data = ["mypy-boto3-redshift-data (>=1.36.0,<1.37.0)"]
redshift-serverless = ["mypy-boto3-redshift-serverless (>=1.36.0,<1.37.0)"]
rekognition = ["mypy-boto3-rekognition (>=1.36.0,<1.37.0)"]
repostspace = ["mypy-boto3-repostspace (>=1.36.0,<1.37.0)"]
resiliencehub = ["mypy-boto3-resiliencehub (>=1.36.0,<1.37.0)"]
resource-explorer-2 = ["mypy-boto3-resource-explorer-2 (>=1.36.0,<1.37.0)"]
resource-groups = ["mypy-boto3-resource-groups (>=1.36.0,<1.37.0)"]
resourcegroupstaggingapi = ["mypy-boto3-resourcegroupstaggingapi (>=1.36.0,<1.37.0)"]
robomaker = ["mypy-boto3-robomaker (>=1.36.0,<1.37.0)"]
rolesanywhere = ["mypy-boto3-rolesanywhere (>=1.36.0,<1.37.0)"]
route53 = ["mypy-boto3-route53 (>=1.36.0,<1.37.0)"]
route53-recovery-cluster = ["mypy-boto3-route53-recovery-cluster (>=1.36.0,<1.37.0)"]
route53-recovery-control-config = ["mypy-boto3-route53-recovery-control-config (>=1.36.0,<1.37.0)"]
route53-recovery-readiness = ["mypy-boto3-route53-recovery-readiness (>=1.36.0,<1.37.0)"]
route53domains = ["mypy-boto3-route53domains (>=1.36.0,<1.37.0)"]
route53profiles = ["mypy-boto3-route53profiles (>=1.36.0,<1.37.0)"]
route53resolver = ["mypy-boto3-route53resolver (>=1.36.0,<1.37.0)"]
rum = ["mypy-boto3-rum (>=1.36.0,<1.37.0)"]
s3 = ["mypy-boto3-s3 (>=1.36.0,<1.37.0)"]
s3control = ["mypy-boto3-s3control (>=1.36.0,<1.37.0)"]
s3outposts = ["mypy-boto3-s3outposts (>=1.36.0,<1.37.0)"]
s3tables = ["mypy-boto3-s3tables (>=1.36.0,<1.37.0)"]
sagemaker = ["mypy-boto3-sagemaker (>=1.36.0,<1.37.0)"]
sagemaker-a2i-runtime = ["mypy-boto3-sagemaker-a2i-runtime (>=1.36.0,<1.37.0)"]
sagemaker-edge = ["mypy-boto3-sagemaker-edge (>=1.36.0,<1.37.0)"]
sagemaker-featurestore-runtime = ["mypy-boto3-sagemaker-featurestore-runtime (>=1.36.0,<1.37.0)"]
sagemaker-geospatial = ["mypy-boto3-sagemaker-geospatial (>=1.36.0,<1.37.0)"]
sagemaker-metrics = ["mypy-boto3-sagemaker-metrics (>=1.36.0,<1.37.0)"]
sagemaker-runtime = ["mypy-boto3-sagemaker-runtime (>=1.36.0,<1.37.0)"]
savingsplans = ["mypy-boto3-savingsplans (>=1.36.0,<1.37.0)"]
scheduler = ["mypy-boto3-scheduler (>=1.36.0,<1.37.0)"]
schemas = ["mypy-boto3-schemas (>=1.36.0,<1.37.0)"]
sdb = ["mypy-boto3-sdb (>=1.36.0,<1.37.0)"]
secretsmanager = ["mypy-boto3-secretsmanager (>=1.36.0,<1.37.0)"]
security-ir = ["mypy-boto3-security-ir (>=1.36.0,<1.37.0)"]
securityhub = ["mypy-boto3-securityhub (>=1.36.0,<1.37.0)"]
securitylake = ["mypy-boto3-securitylake (>=1.36.0,<1.37.0)"]
serverlessrepo = ["mypy-boto3-serverlessrepo (>=1.36.0,<1.37.0)"]
service-quotas = ["mypy-boto3-service-quotas (>=1.36.0,<1.37.0)"]
servicecatalog = ["mypy-boto3-servicecatalog (>=1.36.0,<1.37.0)"]
servicecatalog-appregistry = ["mypy-boto3-servicecatalog-appregistry (>=1.36.0,<1.37.0)"]
servicediscovery = ["mypy-boto3-servicediscovery (>=1.36.0,<1.37.0)"]
ses = ["mypy-boto3-ses (>=1.36.0,<1.37.0)"]
sesv2 = ["mypy-boto3-sesv2 (>=1.36.0,<1.37.0)"]
shield = ["mypy-boto3-shield (>=1.36.0,<1.37.0)"]
signer = ["mypy-boto3-signer (>=1.36.0,<1.37.0)"]
simspaceweaver = ["mypy-boto3-simspaceweaver (>=1.36.0,<1.37.0)"]
sms = ["mypy-boto3-sms (>=1.36.0,<1.37.0)"]
sms-voice = ["mypy-boto3-sms-voice (>=1.36.0,<1.37.0)"]
snow-device-management = ["mypy-boto3-snow-device-management (>=1.36.0,<1.37.0)"]
snowball = ["mypy-boto3-snowball (>=1.36.0,<1.37.0)"]
sns = ["mypy-boto3-sns (>=1.36.0,<1.37.0)"]
socialmessaging = ["mypy-boto3-socialmessaging (>=1.36.0,<1.37.0)"]
sqs = ["mypy-boto3-sqs (>=1.36.0,<1.37.0)"]
ssm = ["mypy-boto3-ssm (>=1.36.0,<1.37.0)"]
ssm-contacts = ["mypy-boto3-ssm-contacts (>=1.36.0,<1.37.0)"]
ssm-incidents = ["mypy-boto3-ssm-incidents (>=1.36.0,<1.37.0)"]
ssm-quicksetup = ["mypy-boto3-ssm-quicksetup (>=1.36.0,<1.37.0)"]
ssm-sap = ["mypy-boto3-ssm-sap (>=1.36.0,<1.37.0)"]
sso = ["mypy-boto3-sso (>=1.36.0,<1.37.0)"]
sso-admin = ["mypy-boto3-sso-admin (>=1.36.0,<1.37.0)"]
sso-oidc = ["mypy-boto3-sso-oidc (>=1.36.0,<1.37.0)"]
stepfunctions = ["mypy-boto3-stepfunctions (>=1.36.0,<1.37.0)"]
storagegateway = ["mypy-boto3-storagegateway (>=1.36.0,<1.37.0)"]
sts = ["mypy-boto3-sts (>=1.36.0,<1.37.0)"]
supplychain = ["mypy-boto3-supplychain (>=1.36.0,<1.37.0)"]
support = ["mypy-boto3-support (>=1.36.0,<1.37.0)"]
support-app = ["mypy-boto3-support-app (>=1.36.0,<1.37.0)"]
swf = ["mypy-boto3-swf (>=1.36.0,<1.37.0)"]
synthetics = ["mypy-boto3-synthetics (>=1.36.0,<1.37.0)"]
taxsettings = ["mypy-boto3-taxsettings (>=1.36.0,<1.37.0)"]
textract = ["mypy-boto3-textract (>=1.36.0,<1.37.0)"]
timestream-influxdb = ["mypy-boto3-timestream-influxdb (>=1.36.0,<1.37.0)"]
timestream-query = ["mypy-boto3-timestream-query (>=1.36.0,<1.37.0)"]
timestream-write = ["mypy-boto3-timestream-write (>=1.36.0,<1.37.0)"]
tnb = ["mypy-boto3-tnb (>=1.36.0,<1.37.0)"]
transcribe = ["mypy-boto3-transcribe (>=1.36.0,<1.37.0)"]
transfer = ["mypy-boto3-transfer (>=1.36.0,<1.37.0)"]
translate = ["mypy-boto3-translate (>=1.36.0,<1.37.0)"]
trustedadvisor = ["mypy-boto3-trustedadvisor (>=1.36.0,<1.37.0)"]
verifiedpermissions = ["mypy-boto3-verifiedpermissions (>=1.36.0,<1.37.0)"]
voice-id = ["mypy-boto3-voice-id (>=1.36.0,<1.37.0)"]
vpc-lattice = ["mypy-boto3-vpc-lattice (>=1.36.0,<1.37.0)"]
waf = ["mypy-boto3-waf (>=1.36.0,<1.37.0)"]
waf-regional = ["mypy-boto3-waf-regional (>=1.36.0,<1.37.0)"]
wafv2 = ["mypy-boto3-wafv2 (>=1.36.0,<1.37.0)"]
wellarchitected = ["mypy-boto3-wellarchitected (>=1.36.0,<1.37.0)"]
wisdom = ["mypy-boto3-wisdom (>=1.36.0,<1.37.0)"]
workdocs = ["mypy-boto3-workdocs (>=1.36.0,<1.37.0)"]
workmail = ["mypy-boto3-workmail (>=1.36.0,<1.37.0)"]
workmailmessageflow = ["mypy-boto3-workmailmessageflow (>=1.36.0,<1.37.0)"]
workspaces = ["mypy-boto3-workspaces (>=1.36.0,<1.37.0)"]
workspaces-thin-client = ["mypy-boto3-workspaces-thin-client (>=1.36.0,<1.37.0)"]
workspaces-web = ["mypy-boto3-workspaces-web (>=1.36.0,<1.37.0)"]
xray = ["mypy-boto3-xray (>=1.36.0,<1.37.0)"]

[[package]]
name = "botocore"
version = "1.38.22"
description = "Low-level, data-driven core of boto 3."
optional = false
python-versions = ">=3.9"
files = [
    {file = "botocore-1.38.22-py3-none-any.whl", hash = "sha256:0e524cc763eced7c87ab256338ebd247ce10d1eb11d5cc4f71a3bd82611739e8"},
    {file = "botocore-1.38.22.tar.gz", hash = "sha256:3b464984674f97367ca1dfa29bdbce499327571208aaec2f9743f66e54d9ba05"},
]

[package.dependencies]
jmespath = ">=0.7.1,<2.0.0"
python-dateutil = ">=2.1,<3.0.0"
urllib3 = {version = ">=1.25.4,<2.2.0 || >2.2.0,<3", markers = "python_version >= \"3.10\""}

[package.extras]
crt = ["awscrt (==0.23.8)"]

[[package]]
name = "botocore-stubs"
version = "1.36.26"
description = "Type annotations and code completion for botocore"
optional = false
python-versions = ">=3.8"
files = [
    {file = "botocore_stubs-1.36.26-py3-none-any.whl", hash = "sha256:7f755b92521a9897b2de931ebcf268e28b2a2837da21fa054f55ac428e2a07cd"},
    {file = "botocore_stubs-1.36.26.tar.gz", hash = "sha256:a6a989096c44f172a4e0a9714a6a7d3f12fa653bbb0395b7a71a5ce27e954091"},
]

[package.dependencies]
types-awscrt = "*"

[package.extras]
botocore = ["botocore"]

[[package]]
name = "colorama"
version = "0.4.6"
description = "Cross-platform colored terminal text."
optional = false
python-versions = "!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*,>=2.7"
files = [
    {file = "colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6"},
    {file = "colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44"},
]

[[package]]
name = "databricks-dlt"
version = "0.2.1"
description = "Databricks DLT Library"
optional = false
python-versions = ">=3.9"
files = [
    {file = "databricks-dlt-0.2.1.tar.gz", hash = "sha256:ef1fd2ad95606a5600423bd7e94e20ba1f4ea5b60381f816ac1d3efbd5525a48"},
    {file = "databricks_dlt-0.2.1-py3-none-any.whl", hash = "sha256:e44ad487206942bccb602f1abaa21ff588333bac1b15c2cdfca32f3de79a05ac"},
]

[package.dependencies]
pyspark = ">=3.0"

[[package]]
name = "iniconfig"
version = "2.1.0"
description = "brain-dead simple config-ini parsing"
optional = false
python-versions = ">=3.8"
files = [
    {file = "iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760"},
    {file = "iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7"},
]

[[package]]
name = "jmespath"
version = "1.0.1"
description = "JSON Matching Expressions"
optional = false
python-versions = ">=3.7"
files = [
    {file = "jmespath-1.0.1-py3-none-any.whl", hash = "sha256:02e2e4cc71b5bcab88332eebf907519190dd9e6e82107fa7f83b1003a6252980"},
    {file = "jmespath-1.0.1.tar.gz", hash = "sha256:90261b206d6defd58fdd5e85f478bf633a2901798906be2ad389150c5c60edbe"},
]

[[package]]
name = "mypy"
version = "1.15.0"
description = "Optional static typing for Python"
optional = false
python-versions = ">=3.9"
files = [
    {file = "mypy-1.15.0-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:979e4e1a006511dacf628e36fadfecbcc0160a8af6ca7dad2f5025529e082c13"},
    {file = "mypy-1.15.0-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:c4bb0e1bd29f7d34efcccd71cf733580191e9a264a2202b0239da95984c5b559"},
    {file = "mypy-1.15.0-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:be68172e9fd9ad8fb876c6389f16d1c1b5f100ffa779f77b1fb2176fcc9ab95b"},
    {file = "mypy-1.15.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:c7be1e46525adfa0d97681432ee9fcd61a3964c2446795714699a998d193f1a3"},
    {file = "mypy-1.15.0-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:2e2c2e6d3593f6451b18588848e66260ff62ccca522dd231cd4dd59b0160668b"},
    {file = "mypy-1.15.0-cp310-cp310-win_amd64.whl", hash = "sha256:6983aae8b2f653e098edb77f893f7b6aca69f6cffb19b2cc7443f23cce5f4828"},
    {file = "mypy-1.15.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:2922d42e16d6de288022e5ca321cd0618b238cfc5570e0263e5ba0a77dbef56f"},
    {file = "mypy-1.15.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:2ee2d57e01a7c35de00f4634ba1bbf015185b219e4dc5909e281016df43f5ee5"},
    {file = "mypy-1.15.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:973500e0774b85d9689715feeffcc980193086551110fd678ebe1f4342fb7c5e"},
    {file = "mypy-1.15.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:5a95fb17c13e29d2d5195869262f8125dfdb5c134dc8d9a9d0aecf7525b10c2c"},
    {file = "mypy-1.15.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:1905f494bfd7d85a23a88c5d97840888a7bd516545fc5aaedff0267e0bb54e2f"},
    {file = "mypy-1.15.0-cp311-cp311-win_amd64.whl", hash = "sha256:c9817fa23833ff189db061e6d2eff49b2f3b6ed9856b4a0a73046e41932d744f"},
    {file = "mypy-1.15.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:aea39e0583d05124836ea645f412e88a5c7d0fd77a6d694b60d9b6b2d9f184fd"},
    {file = "mypy-1.15.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:2f2147ab812b75e5b5499b01ade1f4a81489a147c01585cda36019102538615f"},
    {file = "mypy-1.15.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:ce436f4c6d218a070048ed6a44c0bbb10cd2cc5e272b29e7845f6a2f57ee4464"},
    {file = "mypy-1.15.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:8023ff13985661b50a5928fc7a5ca15f3d1affb41e5f0a9952cb68ef090b31ee"},
    {file = "mypy-1.15.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:1124a18bc11a6a62887e3e137f37f53fbae476dc36c185d549d4f837a2a6a14e"},
    {file = "mypy-1.15.0-cp312-cp312-win_amd64.whl", hash = "sha256:171a9ca9a40cd1843abeca0e405bc1940cd9b305eaeea2dda769ba096932bb22"},
    {file = "mypy-1.15.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:93faf3fdb04768d44bf28693293f3904bbb555d076b781ad2530214ee53e3445"},
    {file = "mypy-1.15.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:811aeccadfb730024c5d3e326b2fbe9249bb7413553f15499a4050f7c30e801d"},
    {file = "mypy-1.15.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:98b7b9b9aedb65fe628c62a6dc57f6d5088ef2dfca37903a7d9ee374d03acca5"},
    {file = "mypy-1.15.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:c43a7682e24b4f576d93072216bf56eeff70d9140241f9edec0c104d0c515036"},
    {file = "mypy-1.15.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:baefc32840a9f00babd83251560e0ae1573e2f9d1b067719479bfb0e987c6357"},
    {file = "mypy-1.15.0-cp313-cp313-win_amd64.whl", hash = "sha256:b9378e2c00146c44793c98b8d5a61039a048e31f429fb0eb546d93f4b000bedf"},
    {file = "mypy-1.15.0-cp39-cp39-macosx_10_9_x86_64.whl", hash = "sha256:e601a7fa172c2131bff456bb3ee08a88360760d0d2f8cbd7a75a65497e2df078"},
    {file = "mypy-1.15.0-cp39-cp39-macosx_11_0_arm64.whl", hash = "sha256:712e962a6357634fef20412699a3655c610110e01cdaa6180acec7fc9f8513ba"},
    {file = "mypy-1.15.0-cp39-cp39-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl", hash = "sha256:f95579473af29ab73a10bada2f9722856792a36ec5af5399b653aa28360290a5"},
    {file = "mypy-1.15.0-cp39-cp39-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl", hash = "sha256:8f8722560a14cde92fdb1e31597760dc35f9f5524cce17836c0d22841830fd5b"},
    {file = "mypy-1.15.0-cp39-cp39-musllinux_1_2_x86_64.whl", hash = "sha256:1fbb8da62dc352133d7d7ca90ed2fb0e9d42bb1a32724c287d3c76c58cbaa9c2"},
    {file = "mypy-1.15.0-cp39-cp39-win_amd64.whl", hash = "sha256:d10d994b41fb3497719bbf866f227b3489048ea4bbbb5015357db306249f7980"},
    {file = "mypy-1.15.0-py3-none-any.whl", hash = "sha256:5469affef548bd1895d86d3bf10ce2b44e33d86923c29e4d675b3e323437ea3e"},
    {file = "mypy-1.15.0.tar.gz", hash = "sha256:404534629d51d3efea5c800ee7c42b72a6554d6c400e6a79eafe15d11341fd43"},
]

[package.dependencies]
mypy_extensions = ">=1.0.0"
typing_extensions = ">=4.6.0"

[package.extras]
dmypy = ["psutil (>=4.0)"]
faster-cache = ["orjson"]
install-types = ["pip"]
mypyc = ["setuptools (>=50)"]
reports = ["lxml"]

[[package]]
name = "mypy-extensions"
version = "1.0.0"
description = "Type system extensions for programs checked with the mypy type checker."
optional = false
python-versions = ">=3.5"
files = [
    {file = "mypy_extensions-1.0.0-py3-none-any.whl", hash = "sha256:4392f6c0eb8a5668a69e23d168ffa70f0be9ccfd32b5cc2d26a34ae5b844552d"},
    {file = "mypy_extensions-1.0.0.tar.gz", hash = "sha256:75dbf8955dc00442a438fc4d0666508a9a97b6bd41aa2f0ffe9d2f2725af0782"},
]

[[package]]
name = "packaging"
version = "25.0"
description = "Core utilities for Python packages"
optional = false
python-versions = ">=3.8"
files = [
    {file = "packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484"},
    {file = "packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f"},
]

[[package]]
name = "pluggy"
version = "1.6.0"
description = "plugin and hook calling mechanisms for python"
optional = false
python-versions = ">=3.9"
files = [
    {file = "pluggy-1.6.0-py3-none-any.whl", hash = "sha256:e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746"},
    {file = "pluggy-1.6.0.tar.gz", hash = "sha256:7dcc130b76258d33b90f61b658791dede3486c3e6bfb003ee5c9bfb396dd22f3"},
]

[package.extras]
dev = ["pre-commit", "tox"]
testing = ["coverage", "pytest", "pytest-benchmark"]

[[package]]
name = "py4j"
version = "********"
description = "Enables Python programs to dynamically access arbitrary Java objects"
optional = false
python-versions = "*"
files = [
    {file = "py4j-********-py2.py3-none-any.whl", hash = "sha256:85defdfd2b2376eb3abf5ca6474b51ab7e0de341c75a02f46dc9b5976f5a5c1b"},
    {file = "py4j-********.tar.gz", hash = "sha256:0b6e5315bb3ada5cf62ac651d107bb2ebc02def3dee9d9548e3baac644ea8dbb"},
]

[[package]]
name = "pyspark"
version = "3.5.4"
description = "Apache Spark Python API"
optional = false
python-versions = ">=3.8"
files = [
    {file = "pyspark-3.5.4.tar.gz", hash = "sha256:1c2926d63020902163f58222466adf6f8016f6c43c1f319b8e7a71dbaa05fc51"},
]

[package.dependencies]
py4j = "********"

[package.extras]
connect = ["googleapis-common-protos (>=1.56.4)", "grpcio (>=1.56.0)", "grpcio-status (>=1.56.0)", "numpy (>=1.15,<2)", "pandas (>=1.0.5)", "pyarrow (>=4.0.0)"]
ml = ["numpy (>=1.15,<2)"]
mllib = ["numpy (>=1.15,<2)"]
pandas-on-spark = ["numpy (>=1.15,<2)", "pandas (>=1.0.5)", "pyarrow (>=4.0.0)"]
sql = ["numpy (>=1.15,<2)", "pandas (>=1.0.5)", "pyarrow (>=4.0.0)"]

[[package]]
name = "pytest"
version = "8.3.5"
description = "pytest: simple powerful testing with Python"
optional = false
python-versions = ">=3.8"
files = [
    {file = "pytest-8.3.5-py3-none-any.whl", hash = "sha256:c69214aa47deac29fad6c2a4f590b9c4a9fdb16a403176fe154b79c0b4d4d820"},
    {file = "pytest-8.3.5.tar.gz", hash = "sha256:f4efe70cc14e511565ac476b57c279e12a855b11f48f212af1080ef2263d3845"},
]

[package.dependencies]
colorama = {version = "*", markers = "sys_platform == \"win32\""}
iniconfig = "*"
packaging = "*"
pluggy = ">=1.5,<2"

[package.extras]
dev = ["argcomplete", "attrs (>=19.2)", "hypothesis (>=3.56)", "mock", "pygments (>=2.7.2)", "requests", "setuptools", "xmlschema"]

[[package]]
name = "pytest-mock"
version = "3.14.0"
description = "Thin-wrapper around the mock package for easier use with pytest"
optional = false
python-versions = ">=3.8"
files = [
    {file = "pytest-mock-3.14.0.tar.gz", hash = "sha256:2719255a1efeceadbc056d6bf3df3d1c5015530fb40cf347c0f9afac88410bd0"},
    {file = "pytest_mock-3.14.0-py3-none-any.whl", hash = "sha256:0b72c38033392a5f4621342fe11e9219ac11ec9d375f8e2a0c164539e0d70f6f"},
]

[package.dependencies]
pytest = ">=6.2.5"

[package.extras]
dev = ["pre-commit", "pytest-asyncio", "tox"]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
description = "Extensions to the standard Python datetime module"
optional = false
python-versions = "!=3.0.*,!=3.1.*,!=3.2.*,>=2.7"
files = [
    {file = "python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3"},
    {file = "python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427"},
]

[package.dependencies]
six = ">=1.5"

[[package]]
name = "ruff"
version = "0.3.7"
description = "An extremely fast Python linter and code formatter, written in Rust."
optional = false
python-versions = ">=3.7"
files = [
    {file = "ruff-0.3.7-py3-none-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl", hash = "sha256:0e8377cccb2f07abd25e84fc5b2cbe48eeb0fea9f1719cad7caedb061d70e5ce"},
    {file = "ruff-0.3.7-py3-none-macosx_10_12_x86_64.whl", hash = "sha256:15a4d1cc1e64e556fa0d67bfd388fed416b7f3b26d5d1c3e7d192c897e39ba4b"},
    {file = "ruff-0.3.7-py3-none-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d28bdf3d7dc71dd46929fafeec98ba89b7c3550c3f0978e36389b5631b793663"},
    {file = "ruff-0.3.7-py3-none-manylinux_2_17_armv7l.manylinux2014_armv7l.whl", hash = "sha256:379b67d4f49774ba679593b232dcd90d9e10f04d96e3c8ce4a28037ae473f7bb"},
    {file = "ruff-0.3.7-py3-none-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:c060aea8ad5ef21cdfbbe05475ab5104ce7827b639a78dd55383a6e9895b7c51"},
    {file = "ruff-0.3.7-py3-none-manylinux_2_17_ppc64.manylinux2014_ppc64.whl", hash = "sha256:ebf8f615dde968272d70502c083ebf963b6781aacd3079081e03b32adfe4d58a"},
    {file = "ruff-0.3.7-py3-none-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:d48098bd8f5c38897b03604f5428901b65e3c97d40b3952e38637b5404b739a2"},
    {file = "ruff-0.3.7-py3-none-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:da8a4fda219bf9024692b1bc68c9cff4b80507879ada8769dc7e985755d662ea"},
    {file = "ruff-0.3.7-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6c44e0149f1d8b48c4d5c33d88c677a4aa22fd09b1683d6a7ff55b816b5d074f"},
    {file = "ruff-0.3.7-py3-none-musllinux_1_2_aarch64.whl", hash = "sha256:3050ec0af72b709a62ecc2aca941b9cd479a7bf2b36cc4562f0033d688e44fa1"},
    {file = "ruff-0.3.7-py3-none-musllinux_1_2_armv7l.whl", hash = "sha256:a29cc38e4c1ab00da18a3f6777f8b50099d73326981bb7d182e54a9a21bb4ff7"},
    {file = "ruff-0.3.7-py3-none-musllinux_1_2_i686.whl", hash = "sha256:5b15cc59c19edca917f51b1956637db47e200b0fc5e6e1878233d3a938384b0b"},
    {file = "ruff-0.3.7-py3-none-musllinux_1_2_x86_64.whl", hash = "sha256:e491045781b1e38b72c91247cf4634f040f8d0cb3e6d3d64d38dcf43616650b4"},
    {file = "ruff-0.3.7-py3-none-win32.whl", hash = "sha256:bc931de87593d64fad3a22e201e55ad76271f1d5bfc44e1a1887edd0903c7d9f"},
    {file = "ruff-0.3.7-py3-none-win_amd64.whl", hash = "sha256:5ef0e501e1e39f35e03c2acb1d1238c595b8bb36cf7a170e7c1df1b73da00e74"},
    {file = "ruff-0.3.7-py3-none-win_arm64.whl", hash = "sha256:789e144f6dc7019d1f92a812891c645274ed08af6037d11fc65fcbc183b7d59f"},
    {file = "ruff-0.3.7.tar.gz", hash = "sha256:d5c1aebee5162c2226784800ae031f660c350e7a3402c4d1f8ea4e97e232e3ba"},
]

[[package]]
name = "s3transfer"
version = "0.13.0"
description = "An Amazon S3 Transfer Manager"
optional = false
python-versions = ">=3.9"
files = [
    {file = "s3transfer-0.13.0-py3-none-any.whl", hash = "sha256:0148ef34d6dd964d0d8cf4311b2b21c474693e57c2e069ec708ce043d2b527be"},
    {file = "s3transfer-0.13.0.tar.gz", hash = "sha256:f5e6db74eb7776a37208001113ea7aa97695368242b364d73e91c981ac522177"},
]

[package.dependencies]
botocore = ">=1.37.4,<2.0a.0"

[package.extras]
crt = ["botocore[crt] (>=1.37.4,<2.0a.0)"]

[[package]]
name = "six"
version = "1.17.0"
description = "Python 2 and 3 compatibility utilities"
optional = false
python-versions = "!=3.0.*,!=3.1.*,!=3.2.*,>=2.7"
files = [
    {file = "six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274"},
    {file = "six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81"},
]

[[package]]
name = "types-awscrt"
version = "0.23.10"
description = "Type annotations and code completion for awscrt"
optional = false
python-versions = ">=3.8"
files = [
    {file = "types_awscrt-0.23.10-py3-none-any.whl", hash = "sha256:7391bf502f6093221e68da8fb6a2af7ec67a98d376c58d5b76cc3938f449d121"},
    {file = "types_awscrt-0.23.10.tar.gz", hash = "sha256:965659260599b421564204b895467684104a2c0311bbacfd3c2423b8b0d3f3e9"},
]

[[package]]
name = "types-s3transfer"
version = "0.11.2"
description = "Type annotations and code completion for s3transfer"
optional = false
python-versions = ">=3.8"
files = [
    {file = "types_s3transfer-0.11.2-py3-none-any.whl", hash = "sha256:09c31cff8c79a433fcf703b840b66d1f694a6c70c410ef52015dd4fe07ee0ae2"},
    {file = "types_s3transfer-0.11.2.tar.gz", hash = "sha256:3ccb8b90b14434af2fb0d6c08500596d93f3a83fb804a2bb843d9bf4f7c2ca60"},
]

[[package]]
name = "typing-extensions"
version = "4.12.2"
description = "Backported and Experimental Type Hints for Python 3.8+"
optional = false
python-versions = ">=3.8"
files = [
    {file = "typing_extensions-4.12.2-py3-none-any.whl", hash = "sha256:04e5ca0351e0f3f85c6853954072df659d0d13fac324d0072316b67d7794700d"},
    {file = "typing_extensions-4.12.2.tar.gz", hash = "sha256:1a7ead55c7e559dd4dee8856e3a88b41225abfe1ce8df57b7c13915fe121ffb8"},
]

[[package]]
name = "urllib3"
version = "2.3.0"
description = "HTTP library with thread-safe connection pooling, file post, and more."
optional = false
python-versions = ">=3.9"
files = [
    {file = "urllib3-2.3.0-py3-none-any.whl", hash = "sha256:1cee9ad369867bfdbbb48b7dd50374c0967a0bb7710050facf0dd6911440e3df"},
    {file = "urllib3-2.3.0.tar.gz", hash = "sha256:f8c5449b3cf0861679ce7e0503c7b44b5ec981bec0d1d3795a07f1ba96f0204d"},
]

[package.extras]
brotli = ["brotli (>=1.0.9)", "brotlicffi (>=0.8.0)"]
h2 = ["h2 (>=4,<5)"]
socks = ["pysocks (>=1.5.6,!=1.5.7,<2.0)"]
zstd = ["zstandard (>=0.18.0)"]

[metadata]
lock-version = "2.0"
python-versions = "3.11.11"
content-hash = "44badb3ff30054a357abfcbe4a3f97daa1f30c957eedaf7236ee5af1e3da3a8c"

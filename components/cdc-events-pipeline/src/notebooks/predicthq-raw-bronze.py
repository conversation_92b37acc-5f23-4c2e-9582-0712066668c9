import dlt  # type: ignore[import-untyped]
import pyspark.sql.functions as F
from pyspark.sql.types import TimestampType


@dlt.table(
    name="raw_events",
    comment="Bronze table for raw events data from PredictHQ",
    table_properties={"quality": "bronze"},
)
def raw_events_data():
    return (
        spark.readStream.format("cloudFiles")  # noqa: F821
        .option("cloudFiles.format", "PARQUET")
        .load(spark.conf.get("phq_src_bucket"))  # noqa: F821
        .withColumn("RECORD_FILE_NAME", F.col("_metadata.file_path"))
        .withColumn(
            "SM_PHQ_FILE_UPLOAD_DT",
            F.regexp_extract(F.col("RECORD_FILE_NAME"), "^s3://(?:[\\w.]+/)?([\\w\\-:.]+)?/(?:.*)$", 1),
        )
        .withColumn(
            "SM_SAFE_UPDATE_DT",
            F.coalesce(F.col("ROW_UPDATED_DT"), F.col("ROW_INSERTED_DT"), F.col("SM_PHQ_FILE_UPLOAD_DT")),
        )
        .withColumn("SM_SAFE_UPDATE_DT", F.col("SM_SAFE_UPDATE_DT").cast(TimestampType()))
        .withColumn("SM_PHQ_FILE_UPLOAD_DT", F.col("SM_PHQ_FILE_UPLOAD_DT").cast(TimestampType()))
        .withColumn("SM_PROCESSED_DT", F.current_timestamp())
        .drop("RECORD_FILE_NAME")
    )

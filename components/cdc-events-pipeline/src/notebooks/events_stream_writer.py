# Databricks notebook source
import json
import random
import re
import time
from collections import deque
from datetime import datetime, timedelta
from typing import Dict, List

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql import functions as F

# COMMAND ----------

def get_spark() -> SparkSession:
    """For testing purposes, return the Spark session.

    Returns:
        SparkSession: Spark session
    """
    return SparkSession.getActiveSession()

# COMMAND ----------

def get_kinesis_client(region: str = "us-west-2"):
    """Get the Kinesis client using the boto3 library.

    Args:
        region (str): AWS region (default: "us-west-2")

    Returns:
        botocore.client.Kinesis: Kinesis client
    """
    # boto3 is imported inside the method so that the method can be serialized
    import boto3  # type: ignore # noqa: F401
    return boto3.client('kinesis', region_name=region)

# COMMAND ----------

def get_kinesis_shard_count(stream_name: str) -> int:
    """Fetches the number of open shards in the specified Kinesis stream."""
    client = get_kinesis_client()
    response = client.describe_stream_summary(StreamName=stream_name)
    return response['StreamDescriptionSummary']['OpenShardCount']

# COMMAND ----------

def send_batch_to_kinesis(df: DataFrame, epoch_id: int) -> None:
    """
    Sends a micro-batch to Kinesis using put_records with proper batching and retries.
    """
    kinesis = get_kinesis_client()

    MAX_RECORDS_PER_REQUEST = 500
    MAX_BYTES_PER_REQUEST = 5 * 1024 * 1024  # 5 MB

    rows = df.select("event_id", "change_action", "stream_name", "x-sm-trace-token").collect()
    if not rows:
        return

    stream_name = rows[0]["stream_name"]
    records_queue = deque()

    # Prepare all records
    for row in rows:
        payload = {
            "x-sm-trace-token": row["x-sm-trace-token"],
            "payload": {
                "eventType": row["change_action"],
                "eventId": row["event_id"],
                "entity": "PredictHqEvent"
            }
        }
        data_bytes = json.dumps(payload).encode("utf-8")
        records_queue.append({
            "Data": data_bytes,
            "PartitionKey": row["event_id"]
        })

    batch = []
    batch_size = 0

    while records_queue:
        record = records_queue.popleft()
        record_size = len(record["Data"])

        batch.append(record)
        batch_size += record_size

        if len(batch) == MAX_RECORDS_PER_REQUEST or batch_size >= MAX_BYTES_PER_REQUEST:
            _send_with_retries(kinesis, batch, stream_name)
            batch = []
            batch_size = 0

    if batch:
        _send_with_retries(kinesis, batch, stream_name)


# COMMAND ----------


def _send_with_retries(kinesis, records: List[dict], stream_name: str) -> None:
    """
    Sends records to Kinesis with retries on failed records using full jittered exponential backoff.
    """
    attempt = 0

    while True:
        response = kinesis.put_records(Records=records, StreamName=stream_name)
        failed_count = response['FailedRecordCount']
        result_records = response['Records']

        failed_records = []
        for i, result in enumerate(result_records):
            if 'ErrorCode' in result:
                error_code = result['ErrorCode']
                error_message = result.get('ErrorMessage', '')
                failed_record = records[i]

                shard_id_match = re.search(r"shard (\S+) in stream", error_message)
                shard_id = shard_id_match.group(1) if shard_id_match else "unknown"

                print(f"[WARN] Failed record - ErrorCode={error_code}, ShardId={shard_id}")
                failed_records.append(failed_record)
            else:
                shard_id = result.get('ShardId', 'unknown')

        if failed_count == 0 and result_records:
            print(f"[INFO] Batch sent successfully after {attempt} attempts.")
            return

        if not result_records:
            print("[WARN] Empty response from Kinesis, retrying full batch")
            failed_records = records

        attempt += 1

        # Exponential increase from 50ms to 250ms using golden ratio (1.618)
        retry_power = attempt - 1
        backoff_ms = min(50 * (1.618 ** retry_power), 250)

        # Full Jitter: Random value between 1 and backoff_interval
        sleep_time_ms = 1 + random.randint(0, int(backoff_ms))
        sleep_time = sleep_time_ms / 1000.0  # Convert to seconds

        print(f"[INFO] Retrying {len(failed_records)} failed records in {sleep_time:.3f}s (attempt {attempt})")
        time.sleep(sleep_time)

        records = failed_records


# COMMAND ----------

def get_args_validate(dbutils) -> Dict[str, str]:
    """Validate the arguments passed to the notebook.

    Args:
        dbutils (Databricks.dbutils): Databricks dbutils
    Returns:
        dict: Validated arguments passed to the notebook
    Raises:
        Exception: If any of the mandatory arguments are missing
    """
    mandatory_args = ("component", "catalog", "schema", "src_table", "stream_name", "checkpoint_location", "kinesis_writer_lookback_hours")
    args = dbutils.notebook.entry_point.getCurrentBindings()
    if not all(k in args for k in mandatory_args):
        raise Exception("Mandatory task arguments missing")
    return args


# COMMAND ----------

def generate_table_name(catalog: str, schema: str, table: str) -> str:
    """Generate the table name from the arguments passed to the notebook.

    The table name is generated in the format `catalog`.`schema`.`table`.
    The backticks (`) are added to the table name to handle cases where the table name contains special characters.

    Args:
        catalog (str): Catalog name
        schema (str): Schema name
        table (str): Table name
    Returns:
        str: Table name in the format `catalog`.`schema`.`table`
    """
    return f"`{catalog}`.`{schema}`.`{table}`"

# COMMAND ----------

def process_cdf_changes(
    spark: SparkSession,
    table_name: str,
    stream_name: str,
    hours_lookback: int,
    checkpoint_location: str
) -> None:
    """
    Process changes from the table using Change Data Feed (CDF) and send to Kinesis.
    Automatically determines shard count from the stream definition.

    Args:
        spark (SparkSession): Spark session
        table_name (str): Table name with CDF enabled
        stream_name (str): Target Kinesis stream name
        hours_lookback (int): Number of hours to look back for changes
        checkpoint_location (str): Checkpoint location for the stream
    """

    print(f"[{datetime.now()}] Starting to process changes from {table_name}")
    print(f"[{datetime.now()}] Using checkpoint location: {checkpoint_location}")
    lookback_timestamp = datetime.now() - timedelta(hours=hours_lookback)
    formatted_timestamp = lookback_timestamp.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{datetime.now()}] Setting starting timestamp to: {formatted_timestamp}")

    shard_count = get_kinesis_shard_count(stream_name)
    print(f"[{datetime.now()}] Discovered shard count: {shard_count}")

    df = (
        spark.readStream
        .format("delta")
        .option("readChangeFeed", "true")
        .option("startingTimestamp", formatted_timestamp)
        .table(table_name)
        .withColumn("filter_dt", F.coalesce(F.col("event_end_dt"), F.col("sm_safe_update_dt")))
        .filter(F.col("filter_dt") > F.date_add(F.current_date(), -90))
        .filter(~(F.col("_change_type") == "update_preimage"))
        .dropDuplicates(['event_id', 'change_action'])
        .select(
            "event_id",
            "change_action",
            F.current_timestamp().alias("processing_time")
        )
        .withColumn("stream_name", F.lit(stream_name))
        .withColumn("x-sm-trace-token", F.expr("uuid()"))
    )

    query = (
        df.writeStream
        .foreachBatch(send_batch_to_kinesis)
        .queryName(stream_name)
        .option("checkpointLocation", checkpoint_location)
        .trigger(availableNow=True)
        .start()
    )

    return query

# COMMAND ----------

def main():
    args = get_args_validate(dbutils)  # type: ignore # noqa: F821
    lookback_hours = int(args["kinesis_writer_lookback_hours"])
    source_table_name = generate_table_name(args["catalog"], args["schema"], args["src_table"])
    checkpoint_location = args["checkpoint_location"]
    stream_name = args["stream_name"]

    print(f"Processing table: {source_table_name}")
    query = process_cdf_changes(
        get_spark(),
        source_table_name,
        stream_name,
        lookback_hours,
        checkpoint_location
    )

    return query

# COMMAND ----------

if __name__ == "__main__":
    query = main()

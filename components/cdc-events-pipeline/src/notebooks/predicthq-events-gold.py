import dlt  # type: ignore[import-untyped]
import pyspark.sql.functions as F

dlt.create_streaming_table(
    name="events",
    comment="Gold table with SCD-1 on cleaned PredictHQ events data",
    table_properties={"quality": "gold"},
)

dlt.apply_changes(
    target="events",
    source="cleaned_events",
    keys=["event_id"],
    sequence_by=F.col("sm_safe_update_dt"),
    apply_as_deletes=F.expr("change_action = 'DELETE'"),
    stored_as_scd_type=1,
)

from typing import Any

import dlt  # type: ignore[import-untyped]
import pyspark.sql.functions as F
from pyspark.sql.types import BooleanType, IntegerType, TimestampType

rules: dict[str, Any] = {}
rules["valid_event_id"] = "(EVENT_ID IS NOT NULL)"
rules["valid_change_action"] = "(CHANGE_ACTION IN ('UPDATE', 'INSERT', 'DELETE'))"
quarantine_rules = f"NOT({' AND '.join(rules.values())})"


@dlt.table(
    name="raw_clean_t",
    temporary=True,
    comment="Schema enforced PHQ bronze temporary table",
)
@dlt.expect_all(rules)
def phq_raw_clean_t():
    return (
        dlt.read_stream("raw_events")
        .withColumnRenamed("EVENT_ID", "event_id")
        .withColumn("create_dt", F.col("CREATE_DT").cast(TimestampType()))
        .withColumn("update_dt", F.col("UPDATE_DT").cast(TimestampType()))
        .withColumnRenamed("TITLE", "title")
        .withColumnRenamed("CATEGORY", "category")
        .withColumnRenamed("LABELS", "labels")
        .withColumnRenamed("DESCRIPTION", "description")
        .withColumn("event_start_dt", F.col("EVENT_START").cast(TimestampType()))
        .drop("EVENT_START")
        .withColumn("event_end_dt", F.col("EVENT_END").cast(TimestampType()))
        .drop("EVENT_END")
        .withColumn("predicted_end_dt", F.col("PREDICTED_END").cast(TimestampType()))
        .drop("PREDICTED_END")
        .withColumnRenamed("TIMEZONE", "timezone")
        .withColumnRenamed("ENTITIES", "entities")
        .withColumnRenamed("GEO", "geo")
        .withColumnRenamed("SCOPE", "scope")
        .withColumnRenamed("PLACEKEY", "placekey")
        .withColumnRenamed("COUNTRY_CODE", "country_code")
        .withColumnRenamed("PLACE_HIERARCHIES", "place_hierarchies")
        .withColumn("phq_attendance", F.col("PHQ_ATTENDANCE").cast(IntegerType()))
        .withColumn("phq_rank", F.col("PHQ_RANK").cast(IntegerType()))
        .withColumn("local_rank", F.col("LOCAL_RANK").cast(IntegerType()))
        .withColumn("aviation_rank", F.col("AVIATION_RANK").cast(IntegerType()))
        .withColumnRenamed("STATUS", "status")
        .withColumn("brand_safe", F.col("BRAND_SAFE").cast(BooleanType()))
        .withColumnRenamed("PARENT_EVENT_ID", "parent_event_id")
        .withColumn("cancelled_dt", F.col("CANCELLED_DT").cast(TimestampType()))
        .withColumn("postponed_dt", F.col("POSTPONED_DT").cast(TimestampType()))
        .withColumnRenamed("PREDICTED_EVENT_SPEND_ACCOMMODATION", "predicted_event_spend_accommodation")
        .withColumnRenamed("PREDICTED_EVENT_SPEND_HOSPITALITY", "predicted_event_spend_hospitality")
        .withColumnRenamed("PREDICTED_EVENT_SPEND_TRANSPORTATION", "predicted_event_spend_transportation")
        .withColumnRenamed("PHQ_LABELS", "phq_labels")
        .withColumn("row_inserted_dt", F.col("ROW_INSERTED_DT").cast(TimestampType()))
        .withColumn("row_updated_dt", F.col("ROW_UPDATED_DT").cast(TimestampType()))
        .withColumnRenamed("CHANGE_ACTION", "change_action")
        .withColumnRenamed("LON", "lon")
        .withColumnRenamed("LAT", "lat")
        .withColumnRenamed("IMPACT_PATTERNS", "impact_patterns")
        .withColumnRenamed("SM_SAFE_UPDATE_DT", "sm_safe_update_dt")
        .withColumnRenamed("SM_PHQ_FILE_UPLOAD_DT", "sm_phq_file_upload_dt")
        .withColumnRenamed("SM_PROCESSED_DT", "sm_processed_dt")
        .withColumn("is_quarantined", F.expr(quarantine_rules))
        .drop("_rescued_data")
    )


@dlt.table(
    name="cleaned_events",
    comment="Cleaned and validated PredictHQ events data",
    table_properties={"quality": "silver"},
)
def get_phq_valid_events():
    return dlt.readStream("raw_clean_t").filter("is_quarantined=false").drop("is_quarantined")


@dlt.table(
    name="invalid_events",
    comment="Invalid PredictHQ events data materialized as a table",
    table_properties={"quality": "silver"},
)
def get_phq_invalid_events():
    return dlt.read("raw_clean_t").filter("is_quarantined=true").drop("is_quarantined")

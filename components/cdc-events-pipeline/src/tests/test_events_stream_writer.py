from unittest.mock import MagicMock, patch

import pytest
from pyspark.sql import Row

# Import the functions to test
import src.notebooks.events_stream_writer as writer


def test_generate_table_name():
    result = writer.generate_table_name("cat", "sch", "tbl")
    assert result == "`cat`.`sch`.`tbl`"

def test_get_args_validate_success():
    dbutils = MagicMock()
    dbutils.notebook.entry_point.getCurrentBindings.return_value = {
        "component": "c",
        "catalog": "cat",
        "schema": "sch",
        "src_table": "tbl",
        "stream_name": "stream",
        "checkpoint_location": "/tmp/checkpoint",
        "kinesis_writer_lookback_hours": 1
    }
    args = writer.get_args_validate(dbutils)
    assert args["catalog"] == "cat"
    assert args["stream_name"] == "stream"

def test_get_args_validate_missing():
    dbutils = MagicMock()
    dbutils.notebook.entry_point.getCurrentBindings.return_value = {}
    with pytest.raises(Exception) as exc:
        writer.get_args_validate(dbutils)
    assert "Mandatory task arguments missing" in str(exc.value)

@patch("src.notebooks.events_stream_writer.get_kinesis_client")
def test_send_batch_to_kinesis(mock_get_kinesis):
    mock_client = MagicMock()
    mock_get_kinesis.return_value = mock_client

    # Mock DataFrame and select method
    df = MagicMock()
    mock_select_result = MagicMock()
    df.select.return_value = mock_select_result

    # Create mock rows for the collect() method to return
    mock_rows = [
        Row(event_id=f"eid{i}", change_action="insert", stream_name="stream", **{"x-sm-trace-token": f"token{i}"})
        for i in range(10)
    ]
    mock_select_result.collect.return_value = mock_rows

    # Simulate successful response for put_records
    mock_client.put_records.return_value = {"FailedRecordCount": 0, "Records": [{} for _ in range(10)]}

    writer.send_batch_to_kinesis(df, epoch_id=1)

    assert mock_client.put_records.call_count > 0

@patch("src.notebooks.events_stream_writer.get_kinesis_client")
def test_send_with_retries(mock_get_kinesis):
    mock_client = MagicMock()
    mock_get_kinesis.return_value = mock_client

    records = [
        {"Data": b"data1", "PartitionKey": "key1"},
        {"Data": b"data2", "PartitionKey": "key2"}
    ]

    mock_client.put_records.side_effect = [
        {"FailedRecordCount": 1, "Records": [{"ErrorCode": "ProvisionedThroughputExceededException"}, {}]},
        {"FailedRecordCount": 0, "Records": [{}, {}]}
    ]

    writer._send_with_retries(mock_client, records, "stream")

    assert mock_client.put_records.call_count == 2

@patch("src.notebooks.events_stream_writer.get_kinesis_client")
@patch("src.notebooks.events_stream_writer.time.sleep")  # Mock sleep to speed up test
def test_send_with_retries_only_retries_failed_records(mock_sleep, mock_get_kinesis):
    mock_client = MagicMock()
    mock_get_kinesis.return_value = mock_client

    # Create test records
    records = [
        {"Data": b"data1", "PartitionKey": "key1"},
        {"Data": b"data2", "PartitionKey": "key2"},
        {"Data": b"data3", "PartitionKey": "key3"}
    ]

    # First call - 2 of 3 records fail
    first_response = {
        "FailedRecordCount": 2,
        "Records": [
            {"ErrorCode": "ProvisionedThroughputExceededException", "ErrorMessage": "Rate exceeded for shard shard-000001 in stream"},
            {"ShardId": "shard-000002"},  # Successful record
            {"ErrorCode": "InternalFailure", "ErrorMessage": "Internal service failure on shard shard-000003 in stream"}
        ]
    }

    # Second call - all records succeed
    second_response = {
        "FailedRecordCount": 0,
        "Records": [
            {"ShardId": "shard-000001"},
            {"ShardId": "shard-000003"}
        ]
    }

    mock_client.put_records.side_effect = [first_response, second_response]

    # Call the function
    writer._send_with_retries(mock_client, records, "test-stream")

    # Check first call sent all records
    first_call_args = mock_client.put_records.call_args_list[0][1]
    assert len(first_call_args["Records"]) == 3
    assert first_call_args["StreamName"] == "test-stream"

    # Check second call only sent the failed records
    second_call_args = mock_client.put_records.call_args_list[1][1]
    assert len(second_call_args["Records"]) == 2

    # Verify we only retried the failed records by checking partition keys
    failed_partition_keys = {second_call_args["Records"][0]["PartitionKey"],
                             second_call_args["Records"][1]["PartitionKey"]}
    assert failed_partition_keys == {"key1", "key3"}

    # Verify we made exactly 2 calls
    assert mock_client.put_records.call_count == 2

    # Verify we slept once between retries
    assert mock_sleep.call_count == 1

def test_process_cdf_changes_signature():
    # Test that the function accepts the new argument and can be called with mocks
    spark = MagicMock()
    table_name = "table"
    stream_name = "stream"
    hours_lookback = 1
    checkpoint_location = "/tmp/checkpoint"
    # Should not raise
    try:
        writer.process_cdf_changes(spark, table_name, stream_name, hours_lookback, checkpoint_location)
    except Exception as e:
        # Only allow errors that are expected due to internals, not signature
        assert "TypeError" not in str(type(e)), f"Signature error: {e}"

[tool.poetry]
name = "cdc-events-pipeline"
version = "0.1.0"
description = ""
authors = ["SiteMinder"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "3.11.11"

[tool.poetry.group.dev.dependencies]
ruff = "^0.3.2"
pyspark = "^3.5.2"
databricks-dlt = "^0.2.0"
mypy = "^1.9.0"
boto3 = "^1.34.139"
boto3-stubs = "^1.34.139"
botocore = "^1.38.22"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.0"
pytest-mock = "^3.14.0"
botocore = "^1.38.22"

[tool.ruff]
line-length = 300
force-exclude = true
extend-include = ["*.ipynb"]

[tool.ruff.lint]
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"
select = [
  "B015",    # useless-comparison
  "D209",    # new-line-after-last-paragraph
  "E",       # error
  "F",       # Pyflakes
  "I",       # isort
  "W",       # warning
  "UP",      # pyupgrade
]

[tool.ruff.format]
# Use double quotes for strings.
quote-style = "double"
# Indent with spaces.
indent-style = "space"
# Respect magic trailing commas. Useful when structuring function/method arguments.
skip-magic-trailing-comma = false

[tool.mypy]
files = ["src"]
show_error_codes = true
no_implicit_optional = true
warn_unused_ignores = true

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
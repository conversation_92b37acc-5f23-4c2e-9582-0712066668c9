package com.siteminder.insights

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import java.time.LocalDate

data class InsightsPropertyPaceResponse(
    val dates: List<InsightsHotelPaceData>
)

@JsonDeserialize(using = InsightsHotelPaceDataDeserializer::class)
interface InsightsHotelPaceData

data class InsightsHotelPaceDate(
    val date: LocalDate,
    val roomNights: Int
) : InsightsHotelPaceData

data class InsightsMissingHotelPaceDate(
    val date: LocalDate,
    val leadDays: Int,
    val missingReason: String
) : InsightsHotelPaceData

class InsightsHotelPaceDataDeserializer : StdDeserializer<InsightsHotelPaceData>(InsightsHotelPaceData::class.java) {
    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): InsightsHotelPaceData {
        val node: JsonNode = p.codec.readTree(p)
        return if (node.has("missingReason")) {
            InsightsMissingHotelPaceDate(
                date = LocalDate.parse(node.get("date").asText()),
                leadDays = node.get("leadDays").asInt(),
                missingReason = node.get("missingReason").asText()
            )
        } else {
            InsightsHotelPaceDate(
                date = LocalDate.parse(node.get("date").asText()),
                roomNights = node.get("roomNights").asInt(),
            )
        }
    }
}

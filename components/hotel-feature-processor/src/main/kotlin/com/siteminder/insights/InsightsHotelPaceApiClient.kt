package com.siteminder.insights

import com.siteminder.api.ServerException
import com.siteminder.api.TraceTokenContextUtils
import com.siteminder.api.client.error.ErrorResponseParser
import com.siteminder.api.error.ErrorCode
import com.siteminder.metrics.Metrics
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.validation.annotation.Validated
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import jakarta.validation.constraints.NotBlank

class InsightsHotelPaceApiClient(private val restTemplate: RestTemplate, private val errorResponseParser: ErrorResponseParser) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getPropertyDemandData(spid: String, paceRequest: InsightsPaceRequest): InsightsPropertyPaceResponse {
        val uriVariables = mapOf(
            "spid" to spid
        )

        try {
            val httpResponse = restTemplate.exchange("/api/hotels/{spid}/pace", HttpMethod.POST, HttpEntity<InsightsPaceRequest>(paceRequest, headers()), object : ParameterizedTypeReference<InsightsPropertyPaceResponse?>() {}, uriVariables)

            return httpResponse.body ?: throw ServerException(ErrorCode.ERROR.name, "cm-insights/hotel-pace-api response body was null")

        } catch (e: Exception) {
            if (e is HttpClientErrorException && e.statusCode == HttpStatus.NOT_FOUND && isNotFound(e.responseBodyAsString)) return InsightsPropertyPaceResponse(dates = emptyList())

            Metrics.meterException(e, this::class.java)
            logger.error("Error calling cm-insights/hotel-pace-api, get hotel pace", e)
            throw ServerException(ErrorCode.ERROR.name, "Failed request to cm-insights/hotel-pace-api. ${e.message}", null, e)
        }
    }

    private fun isNotFound(content: String): Boolean {
        val errorResponse = errorResponseParser.parseOrNull(content)
        return (errorResponse != null && errorResponse.errors.any { it.code == "NOT_FOUND" } )
    }

    private fun headers() = HttpHeaders().apply {
        contentType = MediaType.APPLICATION_JSON
        set("X-SM-TRACE-TOKEN", TraceTokenContextUtils.getTraceToken())
    }
}

@Validated
@ConfigurationProperties("cm-insights.hotel-pace-api")
data class InsightsHotelPaceApiProperties(
    @field:NotBlank
    val baseUrl: String,
    @field:NotBlank
    val tokenPath: String,
    val concurrency: Int = 250,
    val timeoutInSeconds: Int = 30
)

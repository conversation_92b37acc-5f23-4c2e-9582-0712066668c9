package com.siteminder

import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.api.client.error.ErrorResponseParser
import com.siteminder.http.RestTemplateBuilderCustomiser
import com.siteminder.insights.InsightsHotelPaceApiClient
import com.siteminder.insights.InsightsHotelPaceApiProperties
import com.siteminder.security.JwtRepository
import com.siteminder.security.JwtRequestInterceptors
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ApplicationConfiguration {
    @Bean
    fun apiAutoConfigurationProperties(@Value("\${system.jwt-secret}") secret: String): ApiAutoConfigurationProperties {
        return ApiAutoConfigurationProperties("predicthq", "hotel-feature-processor", secret, "/api/**")
    }

    @Bean
    fun insightsHotelPaceApiClient(
        properties: InsightsHotelPaceApiProperties,
        restTemplateBuilderCustomiser: RestTemplateBuilderCustomiser,
        jwtRepository: JwtRepository,
        errorResponseParser: ErrorResponseParser
    ): InsightsHotelPaceApiClient {

        val restTemplate = restTemplateBuilderCustomiser.custom()
            .maxConnections(properties.concurrency)
            .connectTimeoutInSeconds(properties.timeoutInSeconds)
            .socketTimeoutInSeconds(properties.timeoutInSeconds)
            .build()
            .rootUri(properties.baseUrl)
            .interceptors(JwtRequestInterceptors.simpleJwtInterceptor(jwtRepository, properties.tokenPath))
            .build()

        return InsightsHotelPaceApiClient(restTemplate, errorResponseParser)
    }

}

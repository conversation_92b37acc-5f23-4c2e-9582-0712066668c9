package com.siteminder.hotel

import com.siteminder.InsufficientDemandDataException
import com.siteminder.insights.InsightsHotelPaceData
import com.siteminder.insights.InsightsHotelPaceDate
import com.siteminder.predicthq.DemandDataRequest
import java.time.temporal.ChronoUnit

private const val MIN_DAYS_FOR_ANALYSIS = 180
private const val ALLOWED_CONSECUTIVE_DAYS = 7

fun extractValidDemandData(propertyDemandData: List<InsightsHotelPaceData>): List<InsightsHotelPaceDate> {
    val propertyPaceDemandData = propertyDemandData.filterIsInstance<InsightsHotelPaceDate>().sortedBy { it.date }

    if (propertyPaceDemandData.size < MIN_DAYS_FOR_ANALYSIS) {
        throw InsufficientDemandDataException("Not enough demand data to analyze, size: ${propertyPaceDemandData.size}")
    }

    var startIndex = 0
    var startEndIndexPair: Pair<Int, Int>? = null
    propertyPaceDemandData
        .forEachIndexed { index, hotelPaceData ->
            if (index > 0) {
                val previousDate = propertyPaceDemandData[index - 1].date
                val currentDate = hotelPaceData.date
                val missingConsecutiveDays = ChronoUnit.DAYS.between(previousDate, currentDate).toInt() - 1
                if (missingConsecutiveDays > ALLOWED_CONSECUTIVE_DAYS) {
                    val noOfDays = index - startIndex
                    if (noOfDays >= MIN_DAYS_FOR_ANALYSIS) {
                        startEndIndexPair = Pair(startIndex, index)
                    }
                    startIndex = index
                }
            }
        }

    if (propertyPaceDemandData.size - startIndex >= MIN_DAYS_FOR_ANALYSIS) {
        startEndIndexPair = Pair(startIndex, propertyPaceDemandData.size)
    }

    if (startEndIndexPair != null) {
        return propertyPaceDemandData.subList(startEndIndexPair!!.first, startEndIndexPair!!.second)
    }

    return propertyPaceDemandData
}

fun toPredictHqDemandDataRequest(demandData: List<InsightsHotelPaceDate>): DemandDataRequest {
    return DemandDataRequest(
        dateDemandDataList = demandData.sortedBy { it.date }.map {
            DemandDataRequest.DateDemandData(
                date = it.date,
                demand = it.roomNights
            )
        }
    )
}

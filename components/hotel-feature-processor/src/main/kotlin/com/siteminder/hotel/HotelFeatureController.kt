package com.siteminder.hotel

import com.siteminder.InsufficientDemandDataException
import com.siteminder.SqsPublishException
import com.siteminder.api.ServerException
import com.siteminder.api.TraceTokenContextUtils
import com.siteminder.api.error.ErrorCode
import com.siteminder.aws.SqsPublisher
import com.siteminder.insights.InsightsHotelPaceApiClient
import com.siteminder.insights.InsightsPaceRequest
import com.siteminder.metrics.Metrics
import com.siteminder.predicthq.AnalysisNotFoundException
import com.siteminder.predicthq.CreateAnalysisRequest
import com.siteminder.predicthq.InvalidDemandDataUploadedException
import com.siteminder.predicthq.PredictHqClient
import io.swagger.v3.oas.annotations.Operation
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate
import jakarta.validation.Valid

@Validated
@RestController
class HotelFeatureController(private val insightsApiClient: InsightsHotelPaceApiClient, private val hotelApiClient: HotelApiClient, private val predictHqClient: PredictHqClient, private val sqsPublisher: SqsPublisher, @Value("\${predicthq.analysis-queue-url}") val analysisQueueUrl: String) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Operation(summary = "Requires hotel:write permission")
    @PreAuthorize("hasPermission(null, 'hotel', 'write')")
    @PostMapping("/api/events")
    fun processHotel(@Valid @RequestBody request: HotelRequest) {
        val timer = Metrics.timer("hotel-feature-processing").time()
        val spid = request.spid!!
        var analysisId: String? = null

        try {
            MDC.put("spid", spid)
            val hotel = hotelApiClient.getHotel(spid, TraceTokenContextUtils.getTraceToken())
            if (hotel == null) {
                logger.error("Hotel not found")
                return
            }

            hotelApiClient.markFeatureImportanceAttempt(spid, TraceTokenContextUtils.getTraceToken())

            if (hotel.latitude.toDouble() == 0.0 && hotel.longitude.toDouble() == 0.0) {
                logger.info("Hotel latitude and longitude are not set")
                return
            }

            val propertyDemandData = insightsApiClient.getPropertyDemandData(hotel.spid, InsightsPaceRequest(createDemandDataDateRange(365)))

            val propertyPaceDemandData = extractValidDemandData(propertyDemandData.dates)

            val createAnalysisResponse = predictHqClient.createAnalysis(createAnalysisRequest(hotel))
            analysisId = createAnalysisResponse.analysisId

            predictHqClient.uploadDemandData(analysisId, toPredictHqDemandDataRequest(propertyPaceDemandData))

            if (!sqsPublisher.publish(analysisQueueUrl, AnalysisMessage(hotel.spid, analysisId), TraceTokenContextUtils.getTraceToken()).get()) {
                throw SqsPublishException("Failed to publish event with SPID: $spid")
            }
        } catch (e: Exception) {
            logger.error("Error processing hotel", e)
            Metrics.meterException(e, this::class.java)

            if (analysisId != null) {
                deleteAnalysisSilently(analysisId)
            }

            when (e) {
                is InsufficientDemandDataException, is AnalysisNotFoundException, is InvalidDemandDataUploadedException -> return
                else -> throw ServerException(ErrorCode.ERROR.name, "Failed to process hotel", null, e)
            }

        } finally {
            MDC.remove("spid")
            timer.stop()
        }
    }

    private fun createDemandDataDateRange(noOfDays: Int) =
        buildList {
            val today = LocalDate.now()
            for (i in 1 until noOfDays + 1) {
                add(
                    InsightsPaceRequest.Date(today.minusDays(i.toLong()))
                )
            }
        }

    private fun createAnalysisRequest(hotel: HotelResponse): CreateAnalysisRequest =
        CreateAnalysisRequest(
            name = hotel.spid,
            location = CreateAnalysisRequest.Location(
                geoPoint = CreateAnalysisRequest.Location.GeoPoint(
                    lat = hotel.latitude.toString(),
                    lon = hotel.longitude.toString()
                ),
                radius = hotel.suggestedRadius,
            )
        )

    private fun deleteAnalysisSilently(analysisId: String) {
        try {
            predictHqClient.deleteAnalysis(analysisId)
        } catch (e: Exception) {
            logger.error("Failed to delete analysis $analysisId", e)
        }
    }
}

package com.siteminder

import com.siteminder.api.TraceTokenContextUtils
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders

fun smPost(url: String, jwt: String, body: String, traceToken: String = TraceTokenContextUtils.getTraceToken()): MockHttpServletRequestBuilder {
    return MockMvcRequestBuilders.post(url)
        .header("X-SM-TRACE-TOKEN", traceToken)
        .header("Authorization", "Bearer $jwt")
        .contentType(MediaType.APPLICATION_JSON)
        .content(body)
}

package com.siteminder.insights

import com.siteminder.api.ServerException
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import java.time.LocalDate

@SpringBootTest
@ExtendWith(MockServerExtension::class)
class InsightsApiGetDemandDataTests {

    @Autowired
    private lateinit var client: InsightsHotelPaceApiClient

    private lateinit var mockServerClient: MockServerClient

    val spid = "spid123"

    @Test
    fun `get demand data should return valid response`() {
        val today = LocalDate.now()

        val request = apiRequest(
            path = "/insights/api/hotels/$spid/pace", httpMethod = HttpMethod.POST,
            body = """
            {
              "dates": [
                {
                  "date": "$today",
                  "leadDays": 0
                }
              ]
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = """
                    {
                      "dates": [
                        {
                          "date": "$today",
                          "roomNights": 5
                        }
                      ]
                    }
                    """.trimIndent()
                )
            )

        val response = client.getPropertyDemandData(
            spid,
            InsightsPaceRequest(
                dates = listOf(
                    InsightsPaceRequest.Date(
                        date = today,
                        leadDays = 0
                    )
                )
            )
        )

        response shouldBe
            InsightsPropertyPaceResponse(
                dates = listOf(
                    InsightsHotelPaceDate(
                        date = today,
                        roomNights = 5
                    )
                )
            )
    }

    @Test
    fun `get demand data should return empty list response when no data was found`() {
        val today = LocalDate.now()

        val request = apiRequest(
            path = "/insights/api/hotels/$spid/pace", httpMethod = HttpMethod.POST,
            body = """
            {
              "dates": [
                {
                  "date": "$today",
                  "leadDays": 0
                }
              ]
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
                    {
                      "errors":[
                        {
                          "code":"NOT_FOUND",
                          "message":"Data not found"
                        }
                      ]
                    }
                """.trimIndent()
            ))

        val response = client.getPropertyDemandData(
            spid,
            InsightsPaceRequest(
                dates = listOf(
                    InsightsPaceRequest.Date(
                        date = today,
                        leadDays = 0
                    )
                )
            )
        )

        response shouldBe InsightsPropertyPaceResponse(emptyList())

    }

    @Test
    fun `get demand data should throw ServerException when response payload is null`() {
        val today = LocalDate.now()

        val request = apiRequest(
            path = "/insights/api/hotels/$spid/pace", httpMethod = HttpMethod.POST,
            body = """
            {
              "dates": [
                {
                  "date": "$today",
                  "leadDays": 0
                }
              ]
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(apiResponse(body = null))

        shouldThrow<ServerException> {
            client.getPropertyDemandData(
                spid,
                InsightsPaceRequest(
                    dates = listOf(
                        InsightsPaceRequest.Date(
                            date = today,
                            leadDays = 0
                        )
                    )
                )
            )
        }.also {
            it.message shouldBe "Failed request to cm-insights/hotel-pace-api. cm-insights/hotel-pace-api response body was null"
        }
    }

    @Test
    fun `create hotel should throw ServerException when api respond with non-successful http code`() {
        val today = LocalDate.now()

        val request = apiRequest(
            path = "/insights/api/hotels/$spid/pace", httpMethod = HttpMethod.POST,
            body = """
            {
              "dates": [
                {
                  "date": "$today",
                  "leadDays": 0
                }
              ]
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(apiResponse<Void>(status = HttpStatus.INTERNAL_SERVER_ERROR))

        shouldThrow<ServerException> {
            client.getPropertyDemandData(
                spid,
                InsightsPaceRequest(
                    dates = listOf(
                        InsightsPaceRequest.Date(
                            date = today,
                            leadDays = 0
                        )
                    )
                )
            )
        }.also {
            it.message shouldBe "Failed request to cm-insights/hotel-pace-api. 500 Internal Server Error: [no body]"
        }
    }
}

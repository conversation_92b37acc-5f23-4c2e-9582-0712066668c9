package com.siteminder.hotel

import com.siteminder.InsufficientDemandDataException
import com.siteminder.insights.InsightsHotelPaceDate
import com.siteminder.insights.InsightsMissingHotelPaceDate
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class DemandDataUtilTests {

    @Test
    fun `extractValidDemandData should throw InsufficientDemandDataException when there's not enough data`() {
        val demandData = createDemandDateWithGaps(182, missingPaceDataDays = listOf(1, 2, 3, 4, 5, 6, 27, 38, 49, 110))

        shouldThrow<InsufficientDemandDataException> { extractValidDemandData(demandData) }.also {
            it.message shouldBe "Not enough demand data to analyze, size: 172"
        }
    }

    @Test
    fun `extractValidDemandData should return valid demand data when at least there is 180 days valid entry regardless if there are more than 7 consecutive days missing`() {
        val demandData = createDemandDateWithGaps(365, missingPaceDataDays = listOf(191, 192, 193, 194, 195, 196, 197, 198, 199, 110))

        val validDemandData = extractValidDemandData(demandData)
        validDemandData.size shouldBe 190

        validDemandData.first().date shouldBe LocalDate.now().minusDays(190)
        validDemandData.last().date shouldBe LocalDate.now()
    }

    @Test
    fun `extractValidDemandData should return latest of multiple valid demand data when if there more than 7 consecutive days missing`() {
        val demandData = createDemandDateWithGaps(500, missingPaceDataDays = listOf(201, 202, 203, 204, 205, 206, 207, 208, 199, 330))

        val validDemandData = extractValidDemandData(demandData)
        validDemandData.size shouldBe 200

        validDemandData.first().date shouldBe LocalDate.now().minusDays(200)
        validDemandData.last().date shouldBe LocalDate.now()
    }

    private fun createDemandDateWithGaps(noOfDays: Int = 365, missingPaceDataDays: List<Int> = listOf()) =
        buildList {
            val today = LocalDate.now()
            for (i in 0 until noOfDays) {
                val date = today.minusDays(i.toLong())
                val dayNumber = ChronoUnit.DAYS.between(date, today).toInt()
                if (dayNumber in missingPaceDataDays) {
                    add(
                        InsightsMissingHotelPaceDate(
                            date,
                            dayNumber,
                            "some missing reason"
                        )
                    )
                } else
                    add(
                        InsightsHotelPaceDate(
                            date,
                            i
                        )
                    )
            }
        }
}

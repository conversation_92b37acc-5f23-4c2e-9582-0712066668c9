package com.siteminder.hotel

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.OutputStreamAppender
import com.siteminder.InsufficientDemandDataException
import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.api.TraceTokenContextExtension
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.aws.SqsTestUtils
import com.siteminder.csvApiRequest
import com.siteminder.insights.InsightsHotelPaceDate
import com.siteminder.insights.InsightsMissingHotelPaceDate
import com.siteminder.insights.InsightsPaceRequest
import com.siteminder.insights.InsightsPropertyPaceResponse
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.metrics.MetricRegistryHolder
import com.siteminder.metrics.ResetMetricsExtension
import com.siteminder.mockserver.MockServerExtension
import com.siteminder.predicthq.AnalysisNotFoundException
import com.siteminder.predicthq.AuthException
import com.siteminder.predicthq.BadRequestException
import com.siteminder.predicthq.InvalidDemandDataUploadedException
import com.siteminder.predicthq.TooManyRequestException
import com.siteminder.smPost
import io.kotlintest.matchers.collections.shouldHaveSize
import io.kotlintest.matchers.string.shouldContain
import io.kotlintest.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockserver.client.MockServerClient
import org.mockserver.verify.VerificationTimes
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

@AutoConfigureMockMvc
@SpringBootTest
@ExtendWith(MockServerExtension::class, TraceTokenContextExtension::class, ResetMetricsExtension::class)
class HotelFeatureControllerProcessTests {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var sqsAsyncClient: SqsAsyncClient

    private lateinit var mockServerClient: MockServerClient

    private lateinit var mockedAppender: OutputStreamAppender<ILoggingEvent>

    private lateinit var sqsTestUtils: SqsTestUtils

    private val spid = "SPID123"

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:write")
            .sign()
    }

    @BeforeEach
    fun beforeEach() {
        mockedAppender = mock()
        val batchValidatorLogger = LoggerFactory.getLogger(HotelFeatureController::class.java) as Logger
        batchValidatorLogger.level = Level.INFO
        batchValidatorLogger.addAppender(mockedAppender)

        sqsTestUtils = SqsTestUtils.newInstance(sqsAsyncClient, "analysis-queue")
        sqsTestUtils.emptyQueue()
    }

    @Test
    fun `post hotel should stop processing when hotel is not found`() {
        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
                {
                  "errors": [
                    {
                      "code": "NOT_FOUND",
                      "message": "Hotel not found",
                      "meta": {
                        "spid": "$spid",
                        "entity": "Hotel"
                      }
                    }
                  ]
                }
            """.trimIndent()
            )
        )

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isOk)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages.size shouldBe 0

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(1)).doAppend(argumentCaptor.capture())
        argumentCaptor.allValues.first().message shouldBe "Hotel not found"
    }

    @Test
    fun `post hotel should stop processing when hotel's location is not set'`() {
        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())

        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        val getHotelResponsePayload = """
                {
                  "spid": "$spid",
                  "latitude": 0.0,
                  "longitude": 0,
                  "countryCode": "AU",
                  "suggestedRadius": 3.3,
                  "createdAt": "$today",
                  "updatedAt": "$today"
                }
            """.trimIndent()
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(body = getHotelResponsePayload)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isOk)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages.size shouldBe 0

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(1)).doAppend(argumentCaptor.capture())
        argumentCaptor.allValues.first().message shouldBe "Hotel latitude and longitude are not set"
    }

    @Test
    fun `post hotel should ignore when hotel does not have enough demand data`() {

        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())

        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        val getHotelResponsePayload = """
                {
                  "spid": "$spid",
                  "latitude": 180,
                  "longitude": -143.02,
                  "countryCode": "AU",
                  "suggestedRadius": 3.3,
                  "createdAt": "$today",
                  "updatedAt": "$today"
                }
            """.trimIndent()
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(body = getHotelResponsePayload)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val getPropertyDemandDataRequest = apiRequest(path = "/insights/api/hotels/$spid/pace", body = createDemandDateRequestPayload(), httpMethod = HttpMethod.POST)
        mockServerClient.`when`(getPropertyDemandDataRequest).respond(
            apiResponse(body = createDemandDateWithGapsResponsePayload(noOfDays = 182, missingPaceDataDays = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)))
        )

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isOk)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())
        mockServerClient.verify(getPropertyDemandDataRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages.size shouldBe 0

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(1)).doAppend(argumentCaptor.capture())
        argumentCaptor.allValues.first().formattedMessage shouldBe "Error processing hotel"

        val meters = MetricRegistryHolder.getMetricRegistry().meters
        meters.filter { it.key.contains(InsufficientDemandDataException::class.java.simpleName) }.size shouldBe 1
    }

    @Test
    fun `post hotel should return 500 when unexpected error occurs`() {
        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse<Void>(status = HttpStatus.INTERNAL_SERVER_ERROR)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isInternalServerError)
            .andExpect(
                content().json(
                    """
                {
                  "errors": [
                    {
                      "code": "ERROR",
                      "message": "Failed to process hotel"
                    }
                  ]
                }
            """.trimIndent()
                )
            )

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages.size shouldBe 0
    }

    @Test
    fun `post hotel should return 500 when auth error upon calling PredictHQ api`() {
        val latitude = 180
        val longitude = -143.02
        val suggestedRadius = 3.3
        val noOfDays = 300
        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())
        val missingPaceDataDays = listOf(5, 12, 23, 34, 45, 56, 67, 78, 89, 130)

        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        val getHotelRequestResponsePayload = """
                {
                  "spid": "$spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": "AU",
                  "suggestedRadius": $suggestedRadius,
                  "createdAt": "$today",
                  "updatedAt": "$today"
                }
            """.trimIndent()
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(body = getHotelRequestResponsePayload)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val getPropertyDemandDataRequest = apiRequest(path = "/insights/api/hotels/$spid/pace", body = createDemandDateRequestPayload(), httpMethod = HttpMethod.POST)
        mockServerClient.`when`(getPropertyDemandDataRequest).respond(
            apiResponse(body = createDemandDateWithGapsResponsePayload(noOfDays = noOfDays, missingPaceDataDays = missingPaceDataDays))
        )

        val createAnalysisRequestPayload = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $suggestedRadius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        val createAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses", body = createAnalysisRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        mockServerClient.`when`(createAnalysisRequest).respond(
            apiResponse(status = HttpStatus.FORBIDDEN, body = """{"error": "Auth error"}""")
        )

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isInternalServerError)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())
        mockServerClient.verify(getPropertyDemandDataRequest, VerificationTimes.once())
        mockServerClient.verify(createAnalysisRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages shouldHaveSize 0

        val meters = MetricRegistryHolder.getMetricRegistry().meters
        meters.filter { it.key.contains(AuthException::class.java.simpleName) }.size shouldBe 1
    }

    @Test
    fun `post hotel should return 200 and try to delete analysis when PredictHQ api returns 400 with known validation error`() {
        val latitude = 180
        val longitude = -143.02
        val suggestedRadius = 3.3
        val analysisId = "analysis-id-123"
        val noOfDays = 300
        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())
        val missingPaceDataDays = listOf(5, 12, 23, 34, 45, 56, 67, 78, 89, 130)

        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        val getHotelRequestResponsePayload = """
                {
                  "spid": "$spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": "AU",
                  "suggestedRadius": $suggestedRadius,
                  "createdAt": "$today",
                  "updatedAt": "$today"
                }
            """.trimIndent()
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(body = getHotelRequestResponsePayload)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val getPropertyDemandDataRequest = apiRequest(path = "/insights/api/hotels/$spid/pace", body = createDemandDateRequestPayload(), httpMethod = HttpMethod.POST)
        mockServerClient.`when`(getPropertyDemandDataRequest).respond(
            apiResponse(body = createDemandDateWithGapsResponsePayload(noOfDays = noOfDays, missingPaceDataDays = missingPaceDataDays))
        )

        val createAnalysisRequestPayload = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $suggestedRadius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        val createAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses", body = createAnalysisRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        val createAnalysisResponsePayload = """
            {
              "analysis_id": "$analysisId"
            }
        """.trimIndent()
        mockServerClient.`when`(createAnalysisRequest).respond(
            apiResponse(body = createAnalysisResponsePayload)
        )

        val insightsPropertyPaceResponse = InsightsPropertyPaceResponse(createDemandDateWithGaps(noOfDays, missingPaceDataDays))
        val uploadDemandDataRequestPayload = toPredictHqDemandDataRequest(insightsPropertyPaceResponse.dates.filterIsInstance<InsightsHotelPaceDate>()).toCsv()
        val uploadDemandDataRequest = csvApiRequest(path = "/predicthq-ext/v1/beam/analyses/$analysisId/sink", body = uploadDemandDataRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        mockServerClient.`when`(uploadDemandDataRequest).respond(
            apiResponse(
                status = HttpStatus.BAD_REQUEST,
                body = """
                    {
                      "error": "Invalid data uploaded. The values in 'demand' column are constant. Please make sure that all of them are real demands"
                    }
                """.trimIndent()
            )
        )

        val deleteAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE, traceToken = null)
        mockServerClient.`when`(deleteAnalysisRequest).respond(
            apiResponse<Void>()
        )

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isOk)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())
        mockServerClient.verify(getPropertyDemandDataRequest, VerificationTimes.once())
        mockServerClient.verify(createAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(uploadDemandDataRequest, VerificationTimes.once())
        mockServerClient.verify(deleteAnalysisRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages shouldHaveSize 0

        val meters = MetricRegistryHolder.getMetricRegistry().meters
        meters.filter { it.key.contains(InvalidDemandDataUploadedException::class.java.simpleName) }.size shouldBe 1
    }

    @Test
    fun `post hotel should return 500 and try to delete analysis when PredictHQ api returns 400 with unknown validation error`() {
        val latitude = 180
        val longitude = -143.02
        val suggestedRadius = 3.3
        val analysisId = "analysis-id-123"
        val noOfDays = 300
        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())
        val missingPaceDataDays = listOf(5, 12, 23, 34, 45, 56, 67, 78, 89, 130)

        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        val getHotelRequestResponsePayload = """
                {
                  "spid": "$spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": "AU",
                  "suggestedRadius": $suggestedRadius,
                  "createdAt": "$today",
                  "updatedAt": "$today"
                }
            """.trimIndent()
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(body = getHotelRequestResponsePayload)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val getPropertyDemandDataRequest = apiRequest(path = "/insights/api/hotels/$spid/pace", body = createDemandDateRequestPayload(), httpMethod = HttpMethod.POST)
        mockServerClient.`when`(getPropertyDemandDataRequest).respond(
            apiResponse(body = createDemandDateWithGapsResponsePayload(noOfDays = noOfDays, missingPaceDataDays = missingPaceDataDays))
        )

        val createAnalysisRequestPayload = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $suggestedRadius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        val createAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses", body = createAnalysisRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        val createAnalysisResponsePayload = """
            {
              "analysis_id": "$analysisId"
            }
        """.trimIndent()
        mockServerClient.`when`(createAnalysisRequest).respond(
            apiResponse(body = createAnalysisResponsePayload)
        )

        val insightsPropertyPaceResponse = InsightsPropertyPaceResponse(createDemandDateWithGaps(noOfDays, missingPaceDataDays))
        val uploadDemandDataRequestPayload = toPredictHqDemandDataRequest(insightsPropertyPaceResponse.dates.filterIsInstance<InsightsHotelPaceDate>()).toCsv()
        val uploadDemandDataRequest = csvApiRequest(path = "/predicthq-ext/v1/beam/analyses/$analysisId/sink", body = uploadDemandDataRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        mockServerClient.`when`(uploadDemandDataRequest).respond(
            apiResponse(
                status = HttpStatus.BAD_REQUEST,
                body = """
                    {
                      "error": "Unknown validation error"
                    }
                """.trimIndent()
            )
        )

        val deleteAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE, traceToken = null)
        mockServerClient.`when`(deleteAnalysisRequest).respond(
            apiResponse<Void>()
        )

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isInternalServerError)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())
        mockServerClient.verify(getPropertyDemandDataRequest, VerificationTimes.once())
        mockServerClient.verify(createAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(uploadDemandDataRequest, VerificationTimes.once())
        mockServerClient.verify(deleteAnalysisRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages shouldHaveSize 0

        val meters = MetricRegistryHolder.getMetricRegistry().meters
        meters.filter { it.key.contains(BadRequestException::class.java.simpleName) }.size shouldBe 1
    }

    @Test
    fun `post hotel should return 500 and try to delete analysis when PredictHQ api returns too many request - 429`() {
        val latitude = 180
        val longitude = -143.02
        val analysisId = "analysis-id-123"
        val suggestedRadius = 3.3
        val noOfDays = 300
        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())
        val missingPaceDataDays = listOf(5, 12, 23, 34, 45, 56, 67, 78, 89, 130)

        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        val getHotelRequestResponsePayload = """
                {
                  "spid": "$spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": "AU",
                  "suggestedRadius": $suggestedRadius,
                  "createdAt": "$today",
                  "updatedAt": "$today"
                }
            """.trimIndent()
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(body = getHotelRequestResponsePayload)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val getPropertyDemandDataRequest = apiRequest(path = "/insights/api/hotels/$spid/pace", body = createDemandDateRequestPayload(), httpMethod = HttpMethod.POST)
        mockServerClient.`when`(getPropertyDemandDataRequest).respond(
            apiResponse(body = createDemandDateWithGapsResponsePayload(noOfDays = noOfDays, missingPaceDataDays = missingPaceDataDays))
        )

        val createAnalysisRequestPayload = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $suggestedRadius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        val createAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses", body = createAnalysisRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        val createAnalysisResponsePayload = """
            {
              "analysis_id": "$analysisId"
            }
        """.trimIndent()
        mockServerClient.`when`(createAnalysisRequest).respond(
            apiResponse(body = createAnalysisResponsePayload)
        )

        val insightsPropertyPaceResponse = InsightsPropertyPaceResponse(createDemandDateWithGaps(noOfDays, missingPaceDataDays))
        val uploadDemandDataRequestPayload = toPredictHqDemandDataRequest(insightsPropertyPaceResponse.dates.filterIsInstance<InsightsHotelPaceDate>()).toCsv()
        val uploadDemandDataRequest = csvApiRequest(path = "/predicthq-ext/v1/beam/analyses/$analysisId/sink", body = uploadDemandDataRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        mockServerClient.`when`(uploadDemandDataRequest).respond(
            apiResponse(
                status = HttpStatus.TOO_MANY_REQUESTS,
                body = "Too many requests"
            )
        )

        val deleteAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE, traceToken = null)
        mockServerClient.`when`(deleteAnalysisRequest).respond(
            apiResponse<Void>()
        )

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isInternalServerError)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())
        mockServerClient.verify(getPropertyDemandDataRequest, VerificationTimes.once())
        mockServerClient.verify(createAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(deleteAnalysisRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages shouldHaveSize 0

        val meters = MetricRegistryHolder.getMetricRegistry().meters
        meters.filter { it.key.contains(TooManyRequestException::class.java.simpleName) }.size shouldBe 1
    }

    @Test
    fun `post hotel should return 200 when analysis is not found`() {
        val latitude = 180
        val longitude = -143.02
        val suggestedRadius = 3.3
        val analysisId = "analysis-id-123"
        val noOfDays = 300
        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())
        val missingPaceDataDays = listOf(5, 12, 23, 34, 45, 56, 67, 78, 89, 130)

        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        val getHotelRequestResponsePayload = """
                {
                  "spid": "$spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": "AU",
                  "suggestedRadius": $suggestedRadius,
                  "createdAt": "$today",
                  "updatedAt": "$today"
                }
            """.trimIndent()
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(body = getHotelRequestResponsePayload)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val getPropertyDemandDataRequest = apiRequest(path = "/insights/api/hotels/$spid/pace", body = createDemandDateRequestPayload(), httpMethod = HttpMethod.POST)
        mockServerClient.`when`(getPropertyDemandDataRequest).respond(
            apiResponse(body = createDemandDateWithGapsResponsePayload(noOfDays = noOfDays, missingPaceDataDays = missingPaceDataDays))
        )

        val createAnalysisRequestPayload = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $suggestedRadius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        val createAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses", body = createAnalysisRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        val createAnalysisResponsePayload = """
            {
              "analysis_id": "$analysisId"
            }
        """.trimIndent()
        mockServerClient.`when`(createAnalysisRequest).respond(
            apiResponse(body = createAnalysisResponsePayload)
        )

        val insightsPropertyPaceResponse = InsightsPropertyPaceResponse(createDemandDateWithGaps(noOfDays, missingPaceDataDays))
        val uploadDemandDataRequestPayload = toPredictHqDemandDataRequest(insightsPropertyPaceResponse.dates.filterIsInstance<InsightsHotelPaceDate>()).toCsv()
        val uploadDemandDataRequest = csvApiRequest(path = "/predicthq-ext/v1/beam/analyses/$analysisId/sink", body = uploadDemandDataRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        mockServerClient.`when`(uploadDemandDataRequest).respond(
            apiResponse(
                status = HttpStatus.NOT_FOUND,
                body = """
                    {
                      "error": "Analysis not found"
                    }
                """.trimIndent()
            )
        )

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isOk)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())
        mockServerClient.verify(getPropertyDemandDataRequest, VerificationTimes.once())
        mockServerClient.verify(createAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(uploadDemandDataRequest, VerificationTimes.once())

        sqsTestUtils.rawMessages shouldHaveSize 0

        val meters = MetricRegistryHolder.getMetricRegistry().meters
        meters.filter { it.key.contains(AnalysisNotFoundException::class.java.simpleName) }.size shouldBe 1
    }

    @Test
    fun `post hotel should create analysis and publish to queue`() {
        val latitude = 180
        val longitude = -143.02
        val suggestedRadius = 3.3
        val analysisId = "analysis-id-123"
        val noOfDays = 300
        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())
        val missingPaceDataDays = listOf(5, 12, 23, 34, 45, 56, 67, 78, 89, 130)

        val getHotelRequest = apiRequest(path = "/hotel-api/api/hotels/$spid")
        val getHotelRequestResponsePayload = """
                {
                  "spid": "$spid",
                  "latitude": $latitude,
                  "longitude": $longitude,
                  "countryCode": "AU",
                  "suggestedRadius": $suggestedRadius,
                  "createdAt": "$today",
                  "updatedAt": "$today"
                }
            """.trimIndent()
        mockServerClient.`when`(getHotelRequest).respond(
            apiResponse(body = getHotelRequestResponsePayload)
        )

        mockMarkFeatureImportanceAttempt(spid)

        val getPropertyDemandDataRequest = apiRequest(path = "/insights/api/hotels/$spid/pace", body = createDemandDateRequestPayload(), httpMethod = HttpMethod.POST)
        mockServerClient.`when`(getPropertyDemandDataRequest).respond(
            apiResponse(body = createDemandDateWithGapsResponsePayload(noOfDays = noOfDays, missingPaceDataDays = missingPaceDataDays))
        )

        val createAnalysisRequestPayload = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $suggestedRadius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        val createAnalysisRequest = apiRequest(path = "/predicthq-ext/v1/beam/analyses", body = createAnalysisRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        val createAnalysisResponsePayload = """
            {
              "analysis_id": "$analysisId"
            }
        """.trimIndent()
        mockServerClient.`when`(createAnalysisRequest).respond(
            apiResponse(body = createAnalysisResponsePayload)
        )

        val insightsPropertyPaceResponse = InsightsPropertyPaceResponse(createDemandDateWithGaps(noOfDays, missingPaceDataDays))
        val uploadDemandDataRequestPayload = toPredictHqDemandDataRequest(insightsPropertyPaceResponse.dates.filterIsInstance<InsightsHotelPaceDate>()).toCsv()
        val uploadDemandDataRequest = csvApiRequest(path = "/predicthq-ext/v1/beam/analyses/$analysisId/sink", body = uploadDemandDataRequestPayload, traceToken = null, httpMethod = HttpMethod.POST)
        mockServerClient.`when`(uploadDemandDataRequest).respond(
            apiResponse<Void>()
        )

        val request = """{"spid": "$spid"}"""
        mockMvc.perform(smPost("/api/events", jwt, request))
            .andExpect(status().isOk)

        mockServerClient.verify(getHotelRequest, VerificationTimes.once())
        mockServerClient.verify(getPropertyDemandDataRequest, VerificationTimes.once())
        mockServerClient.verify(createAnalysisRequest, VerificationTimes.once())
        mockServerClient.verify(uploadDemandDataRequest, VerificationTimes.once())

        val sqsMessages = sqsTestUtils.rawMessages
        sqsMessages shouldHaveSize 1
        sqsMessages[0].body() shouldContain """{"spid":"$spid","analysisId":"$analysisId"}"""

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(0)).doAppend(argumentCaptor.capture())
    }

    private fun createDemandDateRequestPayload(noOfDays: Int = 365): String {
        val dateListStr = buildList {
            val today = LocalDate.now()
            for (i in 1 until noOfDays + 1) {
                val date = today.minusDays(i.toLong())
                add(
                    InsightsPaceRequest.Date(
                        date,
                        0
                    )
                )
            }
        }.joinToString(separator = ",\n") {
            """
                {
                  "date": "${it.date}",
                  "leadDays": ${it.leadDays}
                }
            """.trimIndent()
        }

        return """
            {
              "dates": [
                $dateListStr
              ]
            }
        """.trimIndent()
    }

    private fun createDemandDateWithGaps(noOfDays: Int = 365, missingPaceDataDays: List<Int> = listOf()) =
        buildList {
            val today = LocalDate.now()
            for (i in 1 until noOfDays + 1) {
                val date = today.minusDays(i.toLong())
                val dayNumber = ChronoUnit.DAYS.between(date, today).toInt()
                if (dayNumber in missingPaceDataDays) {
                    add(
                        InsightsMissingHotelPaceDate(
                            date,
                            dayNumber,
                            "some missing reason"
                        )
                    )
                } else
                    add(
                        InsightsHotelPaceDate(
                            date,
                            i
                        )
                    )
            }
        }

    private fun createDemandDateWithGapsResponsePayload(noOfDays: Int = 365, missingPaceDataDays: List<Int> = listOf()): String {
        val dateListStr = createDemandDateWithGaps(noOfDays, missingPaceDataDays).joinToString(separator = ",\n") {
            if (it is InsightsMissingHotelPaceDate) {
                """
                {
                  "missingReason": "${it.missingReason}",
                  "date": "${it.date}",
                  "leadDays": ${it.leadDays}
                }
            """.trimIndent()
            } else {
                """
                {
                  "date": "${(it as InsightsHotelPaceDate).date}",
                  "roomNights": ${it.roomNights}
                }
            """.trimIndent()
            }
        }

        return """
            {
              "dates": [
                $dateListStr
              ]
            }
        """.trimIndent()
    }

    private fun mockMarkFeatureImportanceAttempt(spid: String) {
        val request = apiRequest(path = "/hotel-api/api/hotels/$spid/mark-feature-importance-attempt", httpMethod = HttpMethod.PUT)
        mockServerClient.`when`(request).respond(apiResponse<Void>())
    }


}

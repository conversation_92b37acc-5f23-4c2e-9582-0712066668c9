package com.siteminder.hotel

import com.siteminder.api.ApiAutoConfigurationProperties
import com.siteminder.api.TraceTokenContextExtension
import com.siteminder.api.TraceTokenContextUtils
import com.siteminder.jwt.JwtTestUtils
import com.siteminder.metrics.ResetMetricsExtension
import com.siteminder.smPost
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@AutoConfigureMockMvc
@SpringBootTest
@ExtendWith(TraceTokenContextExtension::class, ResetMetricsExtension::class)
class HotelFeatureControllerTests {

    @Autowired
    private lateinit var mockMvc: MockMvc

    private val spid = "SPID123"

    @Autowired
    private lateinit var apiAutoConfigurationProperties: ApiAutoConfigurationProperties

    private lateinit var jwt: String

    @BeforeEach
    fun setup() {
        jwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("hotel:write")
            .sign()
    }

    @Test
    fun `post hotel should return 401 if missing token`() {
        mockMvc.perform(MockMvcRequestBuilders.post("/api/events"))
            .andExpect(MockMvcResultMatchers.status().isUnauthorized)
            .andExpect(MockMvcResultMatchers.content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Missing JWT."}]}"""))
    }

    @Test
    fun `post hotel should return 401 if invalid token`() {
        val request = """{"spid": "$spid"}"""

        mockMvc.perform(smPost("/api/events", "xxx", request, TraceTokenContextUtils.getTraceToken()))
            .andExpect(MockMvcResultMatchers.status().isUnauthorized)
            .andExpect(MockMvcResultMatchers.content().json("""{"errors":[{"code":"UNAUTHORIZED","message":"Invalid JWT Token."}]}"""))
    }

    @Test
    fun `post hotel should return 403 when jwt has insufficient permissions`() {
        val invalidJwt = JwtTestUtils.newInstance(apiAutoConfigurationProperties.jwtSecrets[0].id, apiAutoConfigurationProperties.jwtSecrets[0].secret)
            .newJwt()
            .sub("test-client")
            .aud(apiAutoConfigurationProperties.jwtAudience)
            .perms("something:else")
            .sign()

        val request = """{"spid": "$spid"}"""

        mockMvc.perform(smPost("/api/events", invalidJwt, request, TraceTokenContextUtils.getTraceToken()))
            .andExpect(MockMvcResultMatchers.status().isForbidden)
            .andExpect(MockMvcResultMatchers.content().json("""{"errors":[{"code":"FORBIDDEN","message":"Access denied"}]}"""))
    }

    @Test
    fun `post hotel should return 400 if missing required fields`() {
        val request = "{}"

        mockMvc.perform(smPost("/api/events", jwt, request, TraceTokenContextUtils.getTraceToken()))
            .andExpect(MockMvcResultMatchers.status().isBadRequest)
            .andExpect(
                MockMvcResultMatchers.content().json(
                    """
                {
                  "errors": [
                    {"code":"NOT_BLANK","message":"must not be blank","meta":{"field":"spid"}}
                  ]
                }
            """.trimIndent()
                )
            )
    }

}

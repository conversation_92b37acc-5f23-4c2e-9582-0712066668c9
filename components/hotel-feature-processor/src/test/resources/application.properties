spring.profiles.include=localstack
cm-insights.hotel-pace-api.base-url=http://mockserver:1080/insights
cm-insights.hotel-pace-api.token-path=tokens/cm-insights-hotel-pace-api-token
cm-insights.hotel-pace-api.concurrency=100
cm-insights.hotel-pace-api.timeout-in-seconds=60
predicthq.hotel-api.base-url=http://mockserver:1080/hotel-api
predicthq.hotel-api.token-path=tokens/predicthq-hotel-api-token
predicthq.hotel-api.concurrency=100
predicthq.hotel-api.timeout-in-seconds=60
predicthq.api.base-url=http://mockserver:1080/predicthq-ext
predicthq.api.access-token=predicthq-api-access-token
predicthq.api.concurrency=100
predicthq.api.timeout-in-seconds=60
predicthq.analysis-queue-url=http://localhost:4566/000000000000/analysis-queue

system.jwt-secret=secret
secrets.bucketName=sm.secrets.dev

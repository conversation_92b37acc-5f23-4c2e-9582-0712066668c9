openapi: 3.0.1
info:
  title: predicthq/hotel-feature-processor
  description: See README in https://github.com/siteminder-au/predicthq for details
  version: v1
servers:
- url: http://localhost:8080
  description: Generated server url
security:
- bearerAuth: []
paths:
  /api/events:
    post:
      tags:
      - hotel-feature-controller
      summary: Requires hotel:write permission
      operationId: processHotel
      parameters:
      - name: X-SM-TRACE-TOKEN
        in: header
        description: Trace Token
        required: true
        schema:
          type: string
          example: 123e4567-e89b-12d3-a456-426655440000
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelRequest'
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "404":
          description: Not Found
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    Error:
      required:
      - code
      - message
      type: object
      properties:
        code:
          type: string
          enum:
          - ERROR
          - UNAUTHORIZED
          - FORBIDDEN
          - INVALID
          - NOT_FOUND
          - NOT_NULL
          - NOT_BLANK
          - NOT_EMPTY
          - EMAIL
          - MIN
          - MAX
          - UNIQUE
        message:
          type: string
          description: Brief description of error
        meta:
          type: object
          additionalProperties:
            type: object
            description: Error context
            nullable: true
          description: Error context
          nullable: true
    ErrorResponse:
      required:
      - errors
      type: object
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
    HotelRequest:
      required:
      - spid
      type: object
      properties:
        spid:
          type: string
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

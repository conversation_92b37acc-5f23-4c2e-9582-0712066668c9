apply plugin: 'springboot-kotlin-configuration-plugin' // https://github.com/siteminder-au/sm-gradle-plugins/tree/master/cm-build-plugin

dependencies {
    implementation "com.siteminder:sm-spring-boot-api-error-client-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-api-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-http-client-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-jwt-client-starter:${smLibraryVersion}"
    implementation "com.siteminder:sm-spring-boot-sqs-starter:${smLibraryVersion}"

    implementation project(":libs:hotel-api-client")
    implementation project(":libs:predicthq-client")

    testImplementation "com.siteminder:sm-aws-starter-test:${smLibraryVersion}"
    testImplementation "com.siteminder:sm-spring-boot-api-starter-test:${smLibraryVersion}"
    testImplementation "com.siteminder:sm-spring-boot-starter-test:${smLibraryVersion}"
}

None of the specified publications matched for project ':' - nothing to publish.

> Task :libs:hotel-api-client:dependencies

------------------------------------------------------------
Project ':libs:hotel-api-client'
------------------------------------------------------------

annotationProcessor - Annotation processors and their dependencies for source set 'main'.
No dependencies

api - API dependencies for null/main (n)
No dependencies

apiDependenciesMetadata
No dependencies

apiElements - API elements for main. (n)
No dependencies

apiElements-published (n)
No dependencies

bootArchives - Configuration for Spring Boot archive artifacts. (n)
No dependencies

compileClasspath - Compile classpath for null/main.
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    |    +--- org.jetbrains:annotations:13.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 1.9.20 (c)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 1.9.20 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- org.jetbrains.kotlin:kotlin-reflect:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- com.fasterxml.jackson.module:jackson-module-kotlin -> 2.17.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (c)
|    |    |         \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 (c)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    +--- org.jetbrains.kotlin:kotlin-reflect:1.7.22 -> 1.9.20 (*)
|    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
+--- com.siteminder:sm-spring-boot-api-error-client-starter:11.0.2.RELEASE
|    \--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
+--- com.siteminder:sm-spring-boot-http-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot:3.3.5
|    |    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15
|    |    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    |    \--- org.springframework:spring-context:6.1.14 -> 6.1.15
|    |    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6
|    |    |    |              \--- io.micrometer:micrometer-commons:1.13.6
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.5
|    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.5
|    |    |    |    +--- ch.qos.logback:logback-classic:1.5.11
|    |    |    |    |    +--- ch.qos.logback:logback-core:1.5.11
|    |    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    |    |    \--- org.yaml:snakeyaml:2.2
|    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |         \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.5
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.31
|    |    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.31
|    |    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15 (*)
|    |    \--- org.springframework:spring-webmvc:6.1.14 -> 6.1.15
|    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |         \--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.apache.httpcomponents.client5:httpclient5:5.3.1
|         +--- org.apache.httpcomponents.core5:httpcore5:5.2.4 -> 5.2.5
|         +--- org.apache.httpcomponents.core5:httpcore5-h2:5.2.4 -> 5.2.5
|         |    \--- org.apache.httpcomponents.core5:httpcore5:5.2.5
|         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
\--- com.siteminder:sm-spring-boot-jwt-client-starter:11.0.2.RELEASE
     +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     +--- com.siteminder:sm-spring-boot-s3-starter:11.0.2.RELEASE
     |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    +--- com.siteminder:sm-spring-boot-metrics-starter:11.0.2.RELEASE
     |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.5
     |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
     |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.5
     |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.5
     |    |    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
     |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
     |    |    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
     |    |    |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    |    \--- io.micrometer:micrometer-jakarta9:1.13.6
     |    |    |         +--- io.micrometer:micrometer-core:1.13.6
     |    |    |         |    +--- io.micrometer:micrometer-commons:1.13.6
     |    |    |         |    \--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    |         +--- io.micrometer:micrometer-commons:1.13.6
     |    |    |         \--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    +--- com.siteminder:sm-metrics-core:11.0.2.RELEASE
     |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    |    |    +--- io.dropwizard.metrics:metrics-core:4.2.26
     |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    \--- io.prometheus:simpleclient_common:0.16.0
     |    |    |         \--- io.prometheus:simpleclient:0.16.0
     |    |    |              +--- io.prometheus:simpleclient_tracer_otel:0.16.0
     |    |    |              |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
     |    |    |              \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
     |    |    |                   \--- io.prometheus:simpleclient_tracer_common:0.16.0
     |    |    \--- io.micrometer:micrometer-registry-prometheus-simpleclient:1.13.1 -> 1.13.6
     |    |         +--- io.micrometer:micrometer-core:1.13.6 (*)
     |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
     |    +--- software.amazon.awssdk:s3:2.26.29
     |    |    +--- software.amazon.awssdk:aws-xml-protocol:2.26.29
     |    |    |    +--- software.amazon.awssdk:aws-query-protocol:2.26.29
     |    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29
     |    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29
     |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29
     |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |         \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29
     |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:checksums:2.26.29
     |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |         \--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    \--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:third-party-jackson-core:2.26.29
     |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    +--- software.amazon.awssdk:auth:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws-eventstream:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29 (*)
     |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:arns:2.26.29
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:crt-core:2.26.29
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:auth:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
     |    |    \--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
     |    +--- software.amazon.awssdk:netty-nio-client:2.26.29
     |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.114.Final
     |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.114.Final
     |    |    |    |    \--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-transport:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-resolver:4.1.114.Final
     |    |    |    |         \--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-codec:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    \--- io.netty:netty-handler:4.1.114.Final
     |    |    |         +--- io.netty:netty-common:4.1.114.Final
     |    |    |         +--- io.netty:netty-resolver:4.1.114.Final (*)
     |    |    |         +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |         +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |         +--- io.netty:netty-transport-native-unix-common:4.1.114.Final
     |    |    |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |         |    \--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |         \--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.114.Final
     |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-handler:4.1.114.Final (*)
     |    |    |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
     |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.114.Final
     |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.114.Final
     |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    \--- io.netty:netty-transport-native-unix-common:4.1.114.Final (*)
     |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    \--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5 (*)
     \--- com.github.ben-manes.caffeine:caffeine:3.1.8
          +--- org.checkerframework:checker-qual:3.37.0
          \--- com.google.errorprone:error_prone_annotations:2.21.1

compileOnly - Compile only dependencies for null/main. (n)
No dependencies

compileOnlyDependenciesMetadata
No dependencies

default - Configuration for default artifacts. (n)
No dependencies

developmentOnly - Configuration for development-only dependencies such as Spring Boot's DevTools.
No dependencies

implementation - Implementation only dependencies for null/main. (n)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20 (n)
+--- org.jetbrains.kotlin:kotlin-reflect:1.9.20 (n)
+--- com.fasterxml.jackson.module:jackson-module-kotlin (n)
+--- com.siteminder:sm-spring-boot-api-error-client-starter:11.0.2.RELEASE (n)
+--- com.siteminder:sm-spring-boot-http-client-starter:11.0.2.RELEASE (n)
\--- com.siteminder:sm-spring-boot-jwt-client-starter:11.0.2.RELEASE (n)

implementationDependenciesMetadata
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
+--- org.jetbrains.kotlin:kotlin-reflect:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
+--- com.fasterxml.jackson.module:jackson-module-kotlin -> 2.17.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (c)
|    |    |         \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 (c)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    +--- org.jetbrains.kotlin:kotlin-reflect:1.7.22 -> 1.9.20 (*)
|    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
+--- com.siteminder:sm-spring-boot-api-error-client-starter:11.0.2.RELEASE
|    \--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
+--- com.siteminder:sm-spring-boot-http-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot:3.3.5
|    |    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15
|    |    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    |    \--- org.springframework:spring-context:6.1.14 -> 6.1.15
|    |    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6
|    |    |    |              \--- io.micrometer:micrometer-commons:1.13.6
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.5
|    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.5
|    |    |    |    +--- ch.qos.logback:logback-classic:1.5.11
|    |    |    |    |    +--- ch.qos.logback:logback-core:1.5.11
|    |    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    |    |    \--- org.yaml:snakeyaml:2.2
|    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |         \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.5
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.31
|    |    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.31
|    |    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15 (*)
|    |    \--- org.springframework:spring-webmvc:6.1.14 -> 6.1.15
|    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |         \--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.apache.httpcomponents.client5:httpclient5:5.3.1
|         +--- org.apache.httpcomponents.core5:httpcore5:5.2.4 -> 5.2.5
|         +--- org.apache.httpcomponents.core5:httpcore5-h2:5.2.4 -> 5.2.5
|         |    \--- org.apache.httpcomponents.core5:httpcore5:5.2.5
|         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
\--- com.siteminder:sm-spring-boot-jwt-client-starter:11.0.2.RELEASE
     +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     +--- com.siteminder:sm-spring-boot-s3-starter:11.0.2.RELEASE
     |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    +--- com.siteminder:sm-spring-boot-metrics-starter:11.0.2.RELEASE
     |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.5
     |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
     |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.5
     |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.5
     |    |    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
     |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
     |    |    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
     |    |    |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    |    \--- io.micrometer:micrometer-jakarta9:1.13.6
     |    |    |         +--- io.micrometer:micrometer-core:1.13.6
     |    |    |         |    +--- io.micrometer:micrometer-commons:1.13.6
     |    |    |         |    \--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    |         +--- io.micrometer:micrometer-commons:1.13.6
     |    |    |         \--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    +--- com.siteminder:sm-metrics-core:11.0.2.RELEASE
     |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    |    |    +--- io.dropwizard.metrics:metrics-core:4.2.26
     |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    \--- io.prometheus:simpleclient_common:0.16.0
     |    |    |         \--- io.prometheus:simpleclient:0.16.0
     |    |    |              +--- io.prometheus:simpleclient_tracer_otel:0.16.0
     |    |    |              |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
     |    |    |              \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
     |    |    |                   \--- io.prometheus:simpleclient_tracer_common:0.16.0
     |    |    \--- io.micrometer:micrometer-registry-prometheus-simpleclient:1.13.1 -> 1.13.6
     |    |         +--- io.micrometer:micrometer-core:1.13.6 (*)
     |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
     |    +--- software.amazon.awssdk:s3:2.26.29
     |    |    +--- software.amazon.awssdk:aws-xml-protocol:2.26.29
     |    |    |    +--- software.amazon.awssdk:aws-query-protocol:2.26.29
     |    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29
     |    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29
     |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29
     |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |         \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29
     |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:checksums:2.26.29
     |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |         \--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    \--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:third-party-jackson-core:2.26.29
     |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    +--- software.amazon.awssdk:auth:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws-eventstream:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29 (*)
     |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:arns:2.26.29
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:crt-core:2.26.29
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:auth:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
     |    |    \--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
     |    +--- software.amazon.awssdk:netty-nio-client:2.26.29
     |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.114.Final
     |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.114.Final
     |    |    |    |    \--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-transport:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-resolver:4.1.114.Final
     |    |    |    |         \--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-codec:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    \--- io.netty:netty-handler:4.1.114.Final
     |    |    |         +--- io.netty:netty-common:4.1.114.Final
     |    |    |         +--- io.netty:netty-resolver:4.1.114.Final (*)
     |    |    |         +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |         +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |         +--- io.netty:netty-transport-native-unix-common:4.1.114.Final
     |    |    |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |         |    \--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |         \--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.114.Final
     |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-handler:4.1.114.Final (*)
     |    |    |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
     |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.114.Final
     |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.114.Final
     |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    \--- io.netty:netty-transport-native-unix-common:4.1.114.Final (*)
     |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.114.Final (*)
     |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    \--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5 (*)
     \--- com.github.ben-manes.caffeine:caffeine:3.1.8
          +--- org.checkerframework:checker-qual:3.37.0
          \--- com.google.errorprone:error_prone_annotations:2.21.1

intransitiveDependenciesMetadata
No dependencies

kotlinBuildToolsApiClasspath
\--- org.jetbrains.kotlin:kotlin-build-tools-impl:1.9.20
     +--- org.jetbrains.kotlin:kotlin-build-tools-api:1.9.20
     +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
     |    \--- org.jetbrains:annotations:13.0
     +--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.9.20
     |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
     |    +--- org.jetbrains.kotlin:kotlin-script-runtime:1.9.20
     |    +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10 -> 1.9.20
     |    +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.9.20
     |    \--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
     \--- org.jetbrains.kotlin:kotlin-compiler-runner:1.9.20
          +--- org.jetbrains.kotlin:kotlin-build-common:1.9.20
          +--- org.jetbrains.kotlin:kotlin-daemon-client:1.9.20
          +--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.0 -> 1.8.1
          \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.9.20 (*)

kotlinCompilerClasspath
\--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.9.20
     +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
     |    \--- org.jetbrains:annotations:13.0
     +--- org.jetbrains.kotlin:kotlin-script-runtime:1.9.20
     +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10 -> 1.9.20
     +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.9.20
     \--- org.jetbrains.intellij.deps:trove4j:1.0.20200330

kotlinCompilerPluginClasspath
No dependencies

kotlinCompilerPluginClasspathMain - Kotlin compiler plugins for compilation
+--- org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-scripting-compiler-impl-embeddable:1.9.20
|    |    +--- org.jetbrains.kotlin:kotlin-scripting-common:1.9.20
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    |    |         \--- org.jetbrains:annotations:13.0
|    |    +--- org.jetbrains.kotlin:kotlin-scripting-jvm:1.9.20
|    |    |    +--- org.jetbrains.kotlin:kotlin-script-runtime:1.9.20
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-scripting-common:1.9.20 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
\--- org.jetbrains.kotlin:kotlin-allopen-compiler-plugin-embeddable:1.9.20

kotlinCompilerPluginClasspathTest - Kotlin compiler plugins for compilation
+--- org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-scripting-compiler-impl-embeddable:1.9.20
|    |    +--- org.jetbrains.kotlin:kotlin-scripting-common:1.9.20
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    |    |         \--- org.jetbrains:annotations:13.0
|    |    +--- org.jetbrains.kotlin:kotlin-scripting-jvm:1.9.20
|    |    |    +--- org.jetbrains.kotlin:kotlin-script-runtime:1.9.20
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
|    |    |    \--- org.jetbrains.kotlin:kotlin-scripting-common:1.9.20 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
\--- org.jetbrains.kotlin:kotlin-allopen-compiler-plugin-embeddable:1.9.20

kotlinKlibCommonizerClasspath
\--- org.jetbrains.kotlin:kotlin-klib-commonizer-embeddable:1.9.20
     +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
     |    \--- org.jetbrains:annotations:13.0
     \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.9.20
          +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
          +--- org.jetbrains.kotlin:kotlin-script-runtime:1.9.20
          +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10 -> 1.9.20
          +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.9.20
          \--- org.jetbrains.intellij.deps:trove4j:1.0.20200330

kotlinNativeCompilerPluginClasspath
No dependencies

kotlinScriptDef - Script filename extensions discovery classpath configuration
No dependencies

kotlinScriptDefExtensions
No dependencies

mainSourceElements - List of source directories contained in the Main SourceSet. (n)
No dependencies

productionRuntimeClasspath
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    |    +--- org.jetbrains:annotations:13.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 1.9.20 (c)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 1.9.20 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- org.jetbrains.kotlin:kotlin-reflect:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- com.fasterxml.jackson.module:jackson-module-kotlin -> 2.17.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (c)
|    |    |         \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 (c)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    +--- org.jetbrains.kotlin:kotlin-reflect:1.7.22 -> 1.9.20 (*)
|    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
+--- com.siteminder:sm-spring-boot-api-error-client-starter:11.0.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5
|    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15
|    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    \--- org.springframework:spring-context:6.1.14 -> 6.1.15
|    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6
|    |    |              \--- io.micrometer:micrometer-commons:1.13.6
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.5
|    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.5
|    |    |    +--- ch.qos.logback:logback-classic:1.5.11
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.11
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    |    \--- org.yaml:snakeyaml:2.2
|    +--- com.fasterxml.jackson.core:jackson-annotations -> 2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.17.2 (*)
|    \--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
+--- com.siteminder:sm-spring-boot-http-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |         \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.5
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.31
|    |    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.31
|    |    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15 (*)
|    |    \--- org.springframework:spring-webmvc:6.1.14 -> 6.1.15
|    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |         \--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.apache.httpcomponents.client5:httpclient5:5.3.1
|         +--- org.apache.httpcomponents.core5:httpcore5:5.2.4 -> 5.2.5
|         +--- org.apache.httpcomponents.core5:httpcore5-h2:5.2.4 -> 5.2.5
|         |    \--- org.apache.httpcomponents.core5:httpcore5:5.2.5
|         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
\--- com.siteminder:sm-spring-boot-jwt-client-starter:11.0.2.RELEASE
     +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     +--- com.siteminder:sm-spring-boot-s3-starter:11.0.2.RELEASE
     |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    +--- com.siteminder:sm-spring-boot-metrics-starter:11.0.2.RELEASE
     |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.5
     |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
     |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.5
     |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
     |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (*)
     |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.5
     |    |    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
     |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
     |    |    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
     |    |    |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    |    \--- io.micrometer:micrometer-jakarta9:1.13.6
     |    |    |         +--- io.micrometer:micrometer-core:1.13.6
     |    |    |         |    +--- io.micrometer:micrometer-commons:1.13.6
     |    |    |         |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    |         |    +--- org.hdrhistogram:HdrHistogram:2.2.2
     |    |    |         |    \--- org.latencyutils:LatencyUtils:2.0.3
     |    |    |         +--- io.micrometer:micrometer-commons:1.13.6
     |    |    |         \--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    +--- com.siteminder:sm-metrics-core:11.0.2.RELEASE
     |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    |    |    +--- io.dropwizard.metrics:metrics-core:4.2.26
     |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    \--- io.prometheus:simpleclient_common:0.16.0
     |    |    |         \--- io.prometheus:simpleclient:0.16.0
     |    |    |              +--- io.prometheus:simpleclient_tracer_otel:0.16.0
     |    |    |              |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
     |    |    |              \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
     |    |    |                   \--- io.prometheus:simpleclient_tracer_common:0.16.0
     |    |    \--- io.micrometer:micrometer-registry-prometheus-simpleclient:1.13.1 -> 1.13.6
     |    |         +--- io.micrometer:micrometer-core:1.13.6 (*)
     |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
     |    +--- software.amazon.awssdk:s3:2.26.29
     |    |    +--- software.amazon.awssdk:aws-xml-protocol:2.26.29
     |    |    |    +--- software.amazon.awssdk:aws-query-protocol:2.26.29
     |    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29
     |    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29
     |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29
     |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |         \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29
     |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:checksums:2.26.29
     |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |         \--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    \--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:third-party-jackson-core:2.26.29
     |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    +--- software.amazon.awssdk:auth:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws-eventstream:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29 (*)
     |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:arns:2.26.29
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:crt-core:2.26.29
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:auth:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:apache-client:2.26.29
     |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    +--- org.apache.httpcomponents:httpclient:4.5.13
     |    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
     |    |    |    |    +--- commons-logging:commons-logging:1.2
     |    |    |    |    \--- commons-codec:commons-codec:1.11 -> 1.16.1
     |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16
     |    |    |    \--- commons-codec:commons-codec:1.17.1 -> 1.16.1
     |    |    \--- software.amazon.awssdk:netty-nio-client:2.26.29
     |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |         +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |         +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |         +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |         +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.114.Final
     |    |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-buffer:4.1.114.Final
     |    |         |    |    \--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-transport:4.1.114.Final
     |    |         |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |    |    \--- io.netty:netty-resolver:4.1.114.Final
     |    |         |    |         \--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-codec:4.1.114.Final
     |    |         |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |    |    \--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |    \--- io.netty:netty-handler:4.1.114.Final
     |    |         |         +--- io.netty:netty-common:4.1.114.Final
     |    |         |         +--- io.netty:netty-resolver:4.1.114.Final (*)
     |    |         |         +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |         +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |         +--- io.netty:netty-transport-native-unix-common:4.1.114.Final
     |    |         |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |         |    \--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |         \--- io.netty:netty-codec:4.1.114.Final (*)
     |    |         +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.114.Final
     |    |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |    +--- io.netty:netty-codec:4.1.114.Final (*)
     |    |         |    +--- io.netty:netty-handler:4.1.114.Final (*)
     |    |         |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
     |    |         +--- io.netty:netty-codec:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- io.netty:netty-transport:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- io.netty:netty-common:4.1.111.Final -> 4.1.114.Final
     |    |         +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- io.netty:netty-handler:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.114.Final
     |    |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |    \--- io.netty:netty-transport-native-unix-common:4.1.114.Final (*)
     |    |         +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- org.reactivestreams:reactive-streams:1.0.4
     |    |         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    +--- software.amazon.awssdk:netty-nio-client:2.26.29 (*)
     |    \--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5 (*)
     \--- com.github.ben-manes.caffeine:caffeine:3.1.8
          +--- org.checkerframework:checker-qual:3.37.0
          \--- com.google.errorprone:error_prone_annotations:2.21.1

runtimeClasspath - Runtime classpath of null/main.
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    |    +--- org.jetbrains:annotations:13.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 1.9.20 (c)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 1.9.20 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- org.jetbrains.kotlin:kotlin-reflect:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- com.fasterxml.jackson.module:jackson-module-kotlin -> 2.17.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (c)
|    |    |         \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 (c)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    +--- org.jetbrains.kotlin:kotlin-reflect:1.7.22 -> 1.9.20 (*)
|    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
+--- com.siteminder:sm-spring-boot-api-error-client-starter:11.0.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5
|    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15
|    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    \--- org.springframework:spring-context:6.1.14 -> 6.1.15
|    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6
|    |    |              \--- io.micrometer:micrometer-commons:1.13.6
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.5
|    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.5
|    |    |    +--- ch.qos.logback:logback-classic:1.5.11
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.11
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    |    \--- org.yaml:snakeyaml:2.2
|    +--- com.fasterxml.jackson.core:jackson-annotations -> 2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.17.2 (*)
|    \--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
+--- com.siteminder:sm-spring-boot-http-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |         \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.5
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.31
|    |    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.31
|    |    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15 (*)
|    |    \--- org.springframework:spring-webmvc:6.1.14 -> 6.1.15
|    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |         \--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.apache.httpcomponents.client5:httpclient5:5.3.1
|         +--- org.apache.httpcomponents.core5:httpcore5:5.2.4 -> 5.2.5
|         +--- org.apache.httpcomponents.core5:httpcore5-h2:5.2.4 -> 5.2.5
|         |    \--- org.apache.httpcomponents.core5:httpcore5:5.2.5
|         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
\--- com.siteminder:sm-spring-boot-jwt-client-starter:11.0.2.RELEASE
     +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     +--- com.siteminder:sm-spring-boot-s3-starter:11.0.2.RELEASE
     |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    +--- com.siteminder:sm-spring-boot-metrics-starter:11.0.2.RELEASE
     |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.5
     |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
     |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.5
     |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
     |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (*)
     |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.5
     |    |    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
     |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
     |    |    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
     |    |    |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    |    \--- io.micrometer:micrometer-jakarta9:1.13.6
     |    |    |         +--- io.micrometer:micrometer-core:1.13.6
     |    |    |         |    +--- io.micrometer:micrometer-commons:1.13.6
     |    |    |         |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    |         |    +--- org.hdrhistogram:HdrHistogram:2.2.2
     |    |    |         |    \--- org.latencyutils:LatencyUtils:2.0.3
     |    |    |         +--- io.micrometer:micrometer-commons:1.13.6
     |    |    |         \--- io.micrometer:micrometer-observation:1.13.6 (*)
     |    |    +--- com.siteminder:sm-metrics-core:11.0.2.RELEASE
     |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     |    |    |    +--- io.dropwizard.metrics:metrics-core:4.2.26
     |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    \--- io.prometheus:simpleclient_common:0.16.0
     |    |    |         \--- io.prometheus:simpleclient:0.16.0
     |    |    |              +--- io.prometheus:simpleclient_tracer_otel:0.16.0
     |    |    |              |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
     |    |    |              \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
     |    |    |                   \--- io.prometheus:simpleclient_tracer_common:0.16.0
     |    |    \--- io.micrometer:micrometer-registry-prometheus-simpleclient:1.13.1 -> 1.13.6
     |    |         +--- io.micrometer:micrometer-core:1.13.6 (*)
     |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
     |    +--- software.amazon.awssdk:s3:2.26.29
     |    |    +--- software.amazon.awssdk:aws-xml-protocol:2.26.29
     |    |    |    +--- software.amazon.awssdk:aws-query-protocol:2.26.29
     |    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29
     |    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29
     |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29
     |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |         \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29
     |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:checksums:2.26.29
     |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |         \--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
     |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    \--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:third-party-jackson-core:2.26.29
     |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    |    +--- software.amazon.awssdk:auth:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws-eventstream:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29 (*)
     |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
     |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:arns:2.26.29
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:crt-core:2.26.29
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:auth:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
     |    |    +--- software.amazon.awssdk:apache-client:2.26.29
     |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
     |    |    |    +--- org.apache.httpcomponents:httpclient:4.5.13
     |    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
     |    |    |    |    +--- commons-logging:commons-logging:1.2
     |    |    |    |    \--- commons-codec:commons-codec:1.11 -> 1.16.1
     |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16
     |    |    |    \--- commons-codec:commons-codec:1.17.1 -> 1.16.1
     |    |    \--- software.amazon.awssdk:netty-nio-client:2.26.29
     |    |         +--- software.amazon.awssdk:annotations:2.26.29
     |    |         +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
     |    |         +--- software.amazon.awssdk:utils:2.26.29 (*)
     |    |         +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
     |    |         +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.114.Final
     |    |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-buffer:4.1.114.Final
     |    |         |    |    \--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-transport:4.1.114.Final
     |    |         |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |    |    \--- io.netty:netty-resolver:4.1.114.Final
     |    |         |    |         \--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-codec:4.1.114.Final
     |    |         |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |    |    \--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |    \--- io.netty:netty-handler:4.1.114.Final
     |    |         |         +--- io.netty:netty-common:4.1.114.Final
     |    |         |         +--- io.netty:netty-resolver:4.1.114.Final (*)
     |    |         |         +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |         +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |         +--- io.netty:netty-transport-native-unix-common:4.1.114.Final
     |    |         |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |         |    \--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |         \--- io.netty:netty-codec:4.1.114.Final (*)
     |    |         +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.114.Final
     |    |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |    +--- io.netty:netty-codec:4.1.114.Final (*)
     |    |         |    +--- io.netty:netty-handler:4.1.114.Final (*)
     |    |         |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
     |    |         +--- io.netty:netty-codec:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- io.netty:netty-transport:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- io.netty:netty-common:4.1.111.Final -> 4.1.114.Final
     |    |         +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- io.netty:netty-handler:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.114.Final
     |    |         |    +--- io.netty:netty-common:4.1.114.Final
     |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |         |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |         |    \--- io.netty:netty-transport-native-unix-common:4.1.114.Final (*)
     |    |         +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.114.Final (*)
     |    |         +--- org.reactivestreams:reactive-streams:1.0.4
     |    |         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    +--- software.amazon.awssdk:netty-nio-client:2.26.29 (*)
     |    \--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5 (*)
     \--- com.github.ben-manes.caffeine:caffeine:3.1.8
          +--- org.checkerframework:checker-qual:3.37.0
          \--- com.google.errorprone:error_prone_annotations:2.21.1

runtimeElements - Elements of runtime for main. (n)
No dependencies

runtimeElements-published (n)
No dependencies

runtimeOnly - Runtime only dependencies for null/main. (n)
No dependencies

sourcesElements - sources elements for main. (n)
No dependencies

testAndDevelopmentOnly - Configuration for test and development-only dependencies such as Spring Boot's DevTools.
No dependencies

testAnnotationProcessor - Annotation processors and their dependencies for source set 'test'.
No dependencies

testApi - API dependencies for null/test (n)
No dependencies

testApiDependenciesMetadata
No dependencies

testCompileClasspath - Compile classpath for null/test.
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    |    +--- org.jetbrains:annotations:13.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 1.9.20 (c)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 1.9.20 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- org.jetbrains.kotlin:kotlin-reflect:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- com.fasterxml.jackson.module:jackson-module-kotlin -> 2.17.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 (c)
|    |    |         \--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.17.2 (c)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    +--- org.jetbrains.kotlin:kotlin-reflect:1.7.22 -> 1.9.20 (*)
|    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
+--- com.siteminder:sm-spring-boot-api-error-client-starter:11.0.2.RELEASE
|    \--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
+--- com.siteminder:sm-spring-boot-http-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot:3.3.5
|    |    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15
|    |    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    |    \--- org.springframework:spring-context:6.1.14 -> 6.1.15
|    |    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6
|    |    |    |              \--- io.micrometer:micrometer-commons:1.13.6
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.5
|    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.5
|    |    |    |    +--- ch.qos.logback:logback-classic:1.5.11
|    |    |    |    |    +--- ch.qos.logback:logback-core:1.5.11
|    |    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    |    |    \--- org.yaml:snakeyaml:2.2
|    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |         \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.5
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.31
|    |    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.31
|    |    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15 (*)
|    |    \--- org.springframework:spring-webmvc:6.1.14 -> 6.1.15
|    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |         \--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.apache.httpcomponents.client5:httpclient5:5.3.1
|         +--- org.apache.httpcomponents.core5:httpcore5:5.2.4 -> 5.2.5
|         +--- org.apache.httpcomponents.core5:httpcore5-h2:5.2.4 -> 5.2.5
|         |    \--- org.apache.httpcomponents.core5:httpcore5:5.2.5
|         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
+--- com.siteminder:sm-spring-boot-jwt-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- com.siteminder:sm-spring-boot-s3-starter:11.0.2.RELEASE
|    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    +--- com.siteminder:sm-spring-boot-metrics-starter:11.0.2.RELEASE
|    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.5
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.5
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.5
|    |    |    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
|    |    |    |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    |    \--- io.micrometer:micrometer-jakarta9:1.13.6
|    |    |    |         +--- io.micrometer:micrometer-core:1.13.6
|    |    |    |         |    +--- io.micrometer:micrometer-commons:1.13.6
|    |    |    |         |    \--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    |         +--- io.micrometer:micrometer-commons:1.13.6
|    |    |    |         \--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    +--- com.siteminder:sm-metrics-core:11.0.2.RELEASE
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    |    +--- io.dropwizard.metrics:metrics-core:4.2.26
|    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    \--- io.prometheus:simpleclient_common:0.16.0
|    |    |    |         \--- io.prometheus:simpleclient:0.16.0
|    |    |    |              +--- io.prometheus:simpleclient_tracer_otel:0.16.0
|    |    |    |              |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    |              \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
|    |    |    |                   \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    \--- io.micrometer:micrometer-registry-prometheus-simpleclient:1.13.1 -> 1.13.6
|    |    |         +--- io.micrometer:micrometer-core:1.13.6 (*)
|    |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
|    |    +--- software.amazon.awssdk:s3:2.26.29
|    |    |    +--- software.amazon.awssdk:aws-xml-protocol:2.26.29
|    |    |    |    +--- software.amazon.awssdk:aws-query-protocol:2.26.29
|    |    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29
|    |    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29
|    |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29
|    |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |         \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29
|    |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:checksums:2.26.29
|    |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |         \--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    \--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:third-party-jackson-core:2.26.29
|    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    +--- software.amazon.awssdk:auth:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws-eventstream:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29 (*)
|    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:arns:2.26.29
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:crt-core:2.26.29
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:auth:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
|    |    |    \--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
|    |    +--- software.amazon.awssdk:netty-nio-client:2.26.29
|    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.114.Final
|    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final
|    |    |    |    |    \--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-transport:4.1.114.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |    |    \--- io.netty:netty-resolver:4.1.114.Final
|    |    |    |    |         \--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-codec:4.1.114.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |    |    \--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |    \--- io.netty:netty-handler:4.1.114.Final
|    |    |    |         +--- io.netty:netty-common:4.1.114.Final
|    |    |    |         +--- io.netty:netty-resolver:4.1.114.Final (*)
|    |    |    |         +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |         +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |         +--- io.netty:netty-transport-native-unix-common:4.1.114.Final
|    |    |    |         |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |         |    \--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |         \--- io.netty:netty-codec:4.1.114.Final (*)
|    |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.114.Final
|    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |    +--- io.netty:netty-codec:4.1.114.Final (*)
|    |    |    |    +--- io.netty:netty-handler:4.1.114.Final (*)
|    |    |    |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
|    |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.114.Final
|    |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.114.Final
|    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |    \--- io.netty:netty-transport-native-unix-common:4.1.114.Final (*)
|    |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    \--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5 (*)
|    \--- com.github.ben-manes.caffeine:caffeine:3.1.8
|         +--- org.checkerframework:checker-qual:3.37.0
|         \--- com.google.errorprone:error_prone_annotations:2.21.1
+--- org.springframework.boot:spring-boot-starter-test -> 3.3.5
|    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    +--- org.springframework.boot:spring-boot-test:3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    \--- org.springframework:spring-test:6.1.14 -> 6.1.15
|    |         \--- org.springframework:spring-core:6.1.15 (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-test:3.3.5 (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
|    +--- com.jayway.jsonpath:json-path:2.9.0
|    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2
|    |    \--- jakarta.activation:jakarta.activation-api:2.1.3
|    +--- net.minidev:json-smart:2.5.1
|    |    \--- net.minidev:accessors-smart:2.5.1
|    |         \--- org.ow2.asm:asm:9.6
|    +--- org.assertj:assertj-core:3.25.3
|    |    \--- net.bytebuddy:byte-buddy:1.14.11 -> 1.14.19
|    +--- org.awaitility:awaitility:4.2.2
|    |    \--- org.hamcrest:hamcrest:2.1 -> 2.2
|    +--- org.hamcrest:hamcrest:2.2
|    +--- org.junit.jupiter:junit-jupiter:5.10.5
|    |    +--- org.junit:junit-bom:5.10.5
|    |    |    +--- org.junit.jupiter:junit-jupiter:5.10.5 (c)
|    |    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5 (c)
|    |    |    +--- org.junit.jupiter:junit-jupiter-params:5.10.5 (c)
|    |    |    \--- org.junit.platform:junit-platform-commons:1.10.5 (c)
|    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5
|    |    |    +--- org.junit:junit-bom:5.10.5 (*)
|    |    |    +--- org.opentest4j:opentest4j:1.3.0
|    |    |    +--- org.junit.platform:junit-platform-commons:1.10.5
|    |    |    |    +--- org.junit:junit-bom:5.10.5 (*)
|    |    |    |    \--- org.apiguardian:apiguardian-api:1.1.2
|    |    |    \--- org.apiguardian:apiguardian-api:1.1.2
|    |    \--- org.junit.jupiter:junit-jupiter-params:5.10.5
|    |         +--- org.junit:junit-bom:5.10.5 (*)
|    |         +--- org.junit.jupiter:junit-jupiter-api:5.10.5 (*)
|    |         \--- org.apiguardian:apiguardian-api:1.1.2
|    +--- org.mockito:mockito-core:5.11.0
|    |    +--- net.bytebuddy:byte-buddy:1.14.12 -> 1.14.19
|    |    \--- net.bytebuddy:byte-buddy-agent:1.14.12 -> 1.14.19
|    +--- org.mockito:mockito-junit-jupiter:5.11.0
|    |    \--- org.mockito:mockito-core:5.11.0 (*)
|    +--- org.skyscreamer:jsonassert:1.5.3
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    +--- org.springframework:spring-test:6.1.14 -> 6.1.15 (*)
|    \--- org.xmlunit:xmlunit-core:2.9.1
+--- io.kotlintest:kotlintest-assertions:3.4.2
|    +--- org.jetbrains.kotlin:kotlin-reflect:********* -> 1.9.20 (*)
|    +--- io.arrow-kt:arrow-core-data:0.9.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.11 -> 1.9.20 (*)
|    |    \--- io.arrow-kt:arrow-annotations:0.9.0
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.11 -> 1.9.20 (*)
|    |         \--- io.kindedj:kindedj:1.1.0
|    +--- com.univocity:univocity-parsers:2.8.1
|    \--- com.github.wumpz:diffutils:2.2
|         \--- org.eclipse.jgit:org.eclipse.jgit:4.4.1.201607150455-r
+--- org.mockito.kotlin:mockito-kotlin:3.2.0
|    \--- org.mockito:mockito-core:3.9.0 -> 5.11.0 (*)
\--- com.siteminder:sm-spring-boot-starter-test:11.0.2.RELEASE
     +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     +--- org.mock-server:mockserver-netty:5.15.0
     |    +--- org.mock-server:mockserver-client-java:5.15.0
     |    |    +--- org.mock-server:mockserver-core:5.15.0
     |    |    |    +--- com.lmax:disruptor:3.4.4
     |    |    |    +--- javax.servlet:javax.servlet-api:4.0.1
     |    |    |    +--- io.netty:netty-buffer:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-http:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-http2:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-socks:4.1.86.Final -> 4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-handler:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-handler-proxy:4.1.86.Final -> 4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-codec-socks:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-tcnative-boringssl-static:2.0.56.Final -> 2.0.66.Final
     |    |    |    |    \--- io.netty:netty-tcnative-classes:2.0.66.Final
     |    |    |    +--- com.jcraft:jzlib:1.1.3
     |    |    |    +--- com.fasterxml.uuid:java-uuid-generator:4.1.0
     |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    +--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    +--- org.bouncycastle:bcpkix-jdk18on:1.72
     |    |    |    |    +--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    |    \--- org.bouncycastle:bcutil-jdk18on:1.72
     |    |    |    |         \--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    +--- com.nimbusds:nimbus-jose-jwt:9.28
     |    |    |    |    \--- com.github.stephenc.jcip:jcip-annotations:1.0-1
     |    |    |    +--- org.apache.velocity:velocity-engine-scripting:2.3
     |    |    |    |    \--- org.apache.velocity:velocity-engine-core:2.3
     |    |    |    |         +--- org.apache.commons:commons-lang3:3.11 -> 3.14.0
     |    |    |    |         \--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
     |    |    |    +--- org.apache.velocity:velocity-engine-core:2.3 (*)
     |    |    |    +--- org.apache.velocity.tools:velocity-tools-generic:3.1
     |    |    |    |    +--- org.apache.velocity:velocity-engine-core:2.3 (*)
     |    |    |    |    +--- commons-beanutils:commons-beanutils:1.9.4
     |    |    |    |    |    +--- commons-logging:commons-logging:1.2
     |    |    |    |    |    \--- commons-collections:commons-collections:3.2.2
     |    |    |    |    +--- org.apache.commons:commons-digester3:3.2
     |    |    |    |    |    +--- commons-beanutils:commons-beanutils:1.8.3 -> 1.9.4 (*)
     |    |    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
     |    |    |    |    +--- org.apache.commons:commons-lang3:3.10 -> 3.14.0
     |    |    |    |    +--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
     |    |    |    |    \--- com.github.cliftonlabs:json-simple:3.0.2
     |    |    |    +--- com.samskivert:jmustache:1.15 -> 1.16
     |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- net.javacrumbs.json-unit:json-unit-core:2.36.0
     |    |    |    |    \--- org.hamcrest:hamcrest-core:2.2
     |    |    |    |         \--- org.hamcrest:hamcrest:2.2
     |    |    |    +--- com.networknt:json-schema-validator:1.0.76
     |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    \--- com.ethlo.time:itu:1.7.0
     |    |    |    +--- com.jayway.jsonpath:json-path:2.7.0 -> 2.9.0
     |    |    |    +--- io.swagger.parser.v3:swagger-parser:2.1.10
     |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-v2-converter:2.1.10
     |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9
     |    |    |    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.22 -> 2.0.16
     |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
     |    |    |    |    |    |    |    +--- org.yaml:snakeyaml:2.2
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
     |    |    |    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
     |    |    |    |    |    |    +--- io.swagger:swagger-models:1.6.9
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.22 -> 2.0.16
     |    |    |    |    |    |    |    \--- io.swagger:swagger-annotations:1.6.9
     |    |    |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |    |    \--- com.google.guava:guava:31.0.1-android -> 31.1-jre
     |    |    |    |    |    |         +--- com.google.guava:failureaccess:1.0.1
     |    |    |    |    |    |         +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    |    |    |    |         +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |         +--- org.checkerframework:checker-qual:3.12.0 -> 3.37.0
     |    |    |    |    |    |         +--- com.google.errorprone:error_prone_annotations:2.11.0 -> 2.21.1
     |    |    |    |    |    |         \--- com.google.j2objc:j2objc-annotations:1.3
     |    |    |    |    |    +--- io.swagger:swagger-parser:1.0.64
     |    |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9 (*)
     |    |    |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |    |    \--- commons-io:commons-io:2.11.0
     |    |    |    |    |    +--- io.swagger:swagger-compat-spec-parser:1.0.64
     |    |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9 (*)
     |    |    |    |    |    |    +--- io.swagger:swagger-parser:1.0.64 (*)
     |    |    |    |    |    |    +--- com.github.java-json-tools:json-schema-validator:2.2.14
     |    |    |    |    |    |    |    +--- com.google.guava:guava:28.2-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils-equivalence:1.0
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:json-schema-core:1.2.14
     |    |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    |    +--- com.google.guava:guava:28.2-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils:2.0
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils-equivalence:1.0
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:uri-template:0.10
     |    |    |    |    |    |    |    |    |    +--- com.google.guava:guava:28.1-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:msg-simple:1.2
     |    |    |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:btf:1.3
     |    |    |    |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |    +--- org.mozilla:rhino:1.7.7.2
     |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |    |    +--- com.sun.mail:mailapi:1.6.2
     |    |    |    |    |    |    |    +--- joda-time:joda-time:2.10.5
     |    |    |    |    |    |    |    +--- com.googlecode.libphonenumber:libphonenumber:8.11.1
     |    |    |    |    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |    |    \--- net.sf.jopt-simple:jopt-simple:5.0.4
     |    |    |    |    |    |    +--- com.github.java-json-tools:json-patch:1.13
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:msg-simple:1.2 (*)
     |    |    |    |    |    |    |    \--- com.github.java-json-tools:jackson-coreutils:2.0
     |    |    |    |    |    |    \--- org.apache.httpcomponents:httpclient:4.5.13
     |    |    |    |    |    |         +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
     |    |    |    |    |    |         +--- commons-logging:commons-logging:1.2
     |    |    |    |    |    |         \--- commons-codec:commons-codec:1.11 -> 1.16.1
     |    |    |    |    |    +--- io.swagger.core.v3:swagger-models:2.2.8
     |    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-core:2.1.10
     |    |    |    |    |    |    \--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |    \--- io.swagger.parser.v3:swagger-parser-v3:2.1.10
     |    |    |    |    |         +--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |         +--- io.swagger.core.v3:swagger-core:2.2.8
     |    |    |    |    |         |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 -> 4.0.2 (*)
     |    |    |    |    |         |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    |    |         |    +--- org.slf4j:slf4j-api:1.7.35 -> 2.0.16
     |    |    |    |    |         |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- io.swagger.core.v3:swagger-annotations:2.2.8 -> 2.2.22
     |    |    |    |    |         |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |         |    +--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |         |    \--- jakarta.validation:jakarta.validation-api:2.0.2 -> 3.0.2
     |    |    |    |    |         +--- io.swagger.parser.v3:swagger-parser-core:2.1.10 (*)
     |    |    |    |    |         +--- commons-io:commons-io:2.11.0
     |    |    |    |    |         +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         \--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2 (*)
     |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-v3:2.1.10 (*)
     |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    \--- commons-io:commons-io:2.11.0
     |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:3.0.1 -> 4.0.2 (*)
     |    |    |    +--- org.xmlunit:xmlunit-core:2.9.1
     |    |    |    +--- org.xmlunit:xmlunit-placeholders:2.9.1
     |    |    |    |    \--- org.xmlunit:xmlunit-core:2.9.1
     |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    +--- commons-io:commons-io:2.11.0
     |    |    |    +--- org.apache.commons:commons-text:1.10.0
     |    |    |    +--- commons-codec:commons-codec:1.15 -> 1.16.1
     |    |    |    +--- com.google.guava:guava:31.1-jre (*)
     |    |    |    +--- io.github.classgraph:classgraph:4.8.154
     |    |    |    +--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     |    |    |    +--- io.prometheus:simpleclient:0.16.0 (*)
     |    |    |    \--- io.prometheus:simpleclient_httpserver:0.16.0
     |    |    |         +--- io.prometheus:simpleclient:0.16.0 (*)
     |    |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
     |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    +--- com.google.guava:guava:31.1-jre (*)
     |    |    \--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     |    +--- org.mock-server:mockserver-core:5.15.0 (*)
     |    +--- io.netty:netty-buffer:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec-http:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec-http2:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-common:4.1.86.Final -> 4.1.114.Final
     |    +--- io.netty:netty-handler:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-transport:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-tcnative-boringssl-static:2.0.56.Final -> 2.0.66.Final (*)
     |    +--- commons-io:commons-io:2.11.0
     |    +--- com.google.guava:guava:31.1-jre (*)
     |    \--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)

testCompileOnly - Compile only dependencies for null/test. (n)
No dependencies

testCompileOnlyDependenciesMetadata
No dependencies

testImplementation - Implementation only dependencies for null/test. (n)
+--- org.springframework.boot:spring-boot-starter-test (n)
+--- io.kotlintest:kotlintest-assertions:3.4.2 (n)
+--- org.mockito.kotlin:mockito-kotlin:3.2.0 (n)
\--- com.siteminder:sm-spring-boot-starter-test:11.0.2.RELEASE (n)

testImplementationDependenciesMetadata
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
+--- org.jetbrains.kotlin:kotlin-reflect:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
+--- com.fasterxml.jackson.module:jackson-module-kotlin -> 2.17.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 (c)
|    |    |         \--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.17.2 (c)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    +--- org.jetbrains.kotlin:kotlin-reflect:1.7.22 -> 1.9.20 (*)
|    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
+--- com.siteminder:sm-spring-boot-api-error-client-starter:11.0.2.RELEASE
|    \--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
+--- com.siteminder:sm-spring-boot-http-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot:3.3.5
|    |    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15
|    |    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    |    \--- org.springframework:spring-context:6.1.14 -> 6.1.15
|    |    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6
|    |    |    |              \--- io.micrometer:micrometer-commons:1.13.6
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.5
|    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.5
|    |    |    |    +--- ch.qos.logback:logback-classic:1.5.11
|    |    |    |    |    +--- ch.qos.logback:logback-core:1.5.11
|    |    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    |    |    \--- org.yaml:snakeyaml:2.2
|    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |         \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.5
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.31
|    |    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.31
|    |    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15 (*)
|    |    \--- org.springframework:spring-webmvc:6.1.14 -> 6.1.15
|    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |         \--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.apache.httpcomponents.client5:httpclient5:5.3.1
|         +--- org.apache.httpcomponents.core5:httpcore5:5.2.4 -> 5.2.5
|         +--- org.apache.httpcomponents.core5:httpcore5-h2:5.2.4 -> 5.2.5
|         |    \--- org.apache.httpcomponents.core5:httpcore5:5.2.5
|         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
+--- com.siteminder:sm-spring-boot-jwt-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- com.siteminder:sm-spring-boot-s3-starter:11.0.2.RELEASE
|    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    +--- com.siteminder:sm-spring-boot-metrics-starter:11.0.2.RELEASE
|    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.5
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.5
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.5
|    |    |    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
|    |    |    |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    |    \--- io.micrometer:micrometer-jakarta9:1.13.6
|    |    |    |         +--- io.micrometer:micrometer-core:1.13.6
|    |    |    |         |    +--- io.micrometer:micrometer-commons:1.13.6
|    |    |    |         |    \--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    |         +--- io.micrometer:micrometer-commons:1.13.6
|    |    |    |         \--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    +--- com.siteminder:sm-metrics-core:11.0.2.RELEASE
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    |    +--- io.dropwizard.metrics:metrics-core:4.2.26
|    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    \--- io.prometheus:simpleclient_common:0.16.0
|    |    |    |         \--- io.prometheus:simpleclient:0.16.0
|    |    |    |              +--- io.prometheus:simpleclient_tracer_otel:0.16.0
|    |    |    |              |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    |              \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
|    |    |    |                   \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    \--- io.micrometer:micrometer-registry-prometheus-simpleclient:1.13.1 -> 1.13.6
|    |    |         +--- io.micrometer:micrometer-core:1.13.6 (*)
|    |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
|    |    +--- software.amazon.awssdk:s3:2.26.29
|    |    |    +--- software.amazon.awssdk:aws-xml-protocol:2.26.29
|    |    |    |    +--- software.amazon.awssdk:aws-query-protocol:2.26.29
|    |    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29
|    |    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29
|    |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29
|    |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |         \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29
|    |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:checksums:2.26.29
|    |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |         \--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    \--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:third-party-jackson-core:2.26.29
|    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    +--- software.amazon.awssdk:auth:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws-eventstream:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29 (*)
|    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:arns:2.26.29
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:crt-core:2.26.29
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:auth:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
|    |    |    \--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
|    |    +--- software.amazon.awssdk:netty-nio-client:2.26.29
|    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.114.Final
|    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final
|    |    |    |    |    \--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-transport:4.1.114.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |    |    \--- io.netty:netty-resolver:4.1.114.Final
|    |    |    |    |         \--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-codec:4.1.114.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |    |    \--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |    \--- io.netty:netty-handler:4.1.114.Final
|    |    |    |         +--- io.netty:netty-common:4.1.114.Final
|    |    |    |         +--- io.netty:netty-resolver:4.1.114.Final (*)
|    |    |    |         +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |         +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |         +--- io.netty:netty-transport-native-unix-common:4.1.114.Final
|    |    |    |         |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |         |    \--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |         \--- io.netty:netty-codec:4.1.114.Final (*)
|    |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.114.Final
|    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |    +--- io.netty:netty-codec:4.1.114.Final (*)
|    |    |    |    +--- io.netty:netty-handler:4.1.114.Final (*)
|    |    |    |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
|    |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.114.Final
|    |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.114.Final
|    |    |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |    |    \--- io.netty:netty-transport-native-unix-common:4.1.114.Final (*)
|    |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.114.Final (*)
|    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    \--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5 (*)
|    \--- com.github.ben-manes.caffeine:caffeine:3.1.8
|         +--- org.checkerframework:checker-qual:3.37.0
|         \--- com.google.errorprone:error_prone_annotations:2.21.1
+--- org.springframework.boot:spring-boot-starter-test -> 3.3.5
|    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    +--- org.springframework.boot:spring-boot-test:3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    \--- org.springframework:spring-test:6.1.14 -> 6.1.15
|    |         \--- org.springframework:spring-core:6.1.15 (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-test:3.3.5 (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
|    +--- com.jayway.jsonpath:json-path:2.9.0
|    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2
|    |    \--- jakarta.activation:jakarta.activation-api:2.1.3
|    +--- net.minidev:json-smart:2.5.1
|    |    \--- net.minidev:accessors-smart:2.5.1
|    |         \--- org.ow2.asm:asm:9.6
|    +--- org.assertj:assertj-core:3.25.3
|    |    \--- net.bytebuddy:byte-buddy:1.14.11 -> 1.14.19
|    +--- org.awaitility:awaitility:4.2.2
|    |    \--- org.hamcrest:hamcrest:2.1 -> 2.2
|    +--- org.hamcrest:hamcrest:2.2
|    +--- org.junit.jupiter:junit-jupiter:5.10.5
|    |    +--- org.junit:junit-bom:5.10.5
|    |    |    +--- org.junit.jupiter:junit-jupiter:5.10.5 (c)
|    |    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5 (c)
|    |    |    +--- org.junit.jupiter:junit-jupiter-params:5.10.5 (c)
|    |    |    \--- org.junit.platform:junit-platform-commons:1.10.5 (c)
|    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5
|    |    |    +--- org.junit:junit-bom:5.10.5 (*)
|    |    |    +--- org.opentest4j:opentest4j:1.3.0
|    |    |    +--- org.junit.platform:junit-platform-commons:1.10.5
|    |    |    |    +--- org.junit:junit-bom:5.10.5 (*)
|    |    |    |    \--- org.apiguardian:apiguardian-api:1.1.2
|    |    |    \--- org.apiguardian:apiguardian-api:1.1.2
|    |    \--- org.junit.jupiter:junit-jupiter-params:5.10.5
|    |         +--- org.junit:junit-bom:5.10.5 (*)
|    |         +--- org.junit.jupiter:junit-jupiter-api:5.10.5 (*)
|    |         \--- org.apiguardian:apiguardian-api:1.1.2
|    +--- org.mockito:mockito-core:5.11.0
|    |    +--- net.bytebuddy:byte-buddy:1.14.12 -> 1.14.19
|    |    \--- net.bytebuddy:byte-buddy-agent:1.14.12 -> 1.14.19
|    +--- org.mockito:mockito-junit-jupiter:5.11.0
|    |    \--- org.mockito:mockito-core:5.11.0 (*)
|    +--- org.skyscreamer:jsonassert:1.5.3
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    +--- org.springframework:spring-test:6.1.14 -> 6.1.15 (*)
|    \--- org.xmlunit:xmlunit-core:2.9.1
+--- io.kotlintest:kotlintest-assertions:3.4.2
|    +--- org.jetbrains.kotlin:kotlin-reflect:********* -> 1.9.20 (*)
|    +--- io.arrow-kt:arrow-core-data:0.9.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.11 -> 1.9.20 (*)
|    |    \--- io.arrow-kt:arrow-annotations:0.9.0
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.11 -> 1.9.20
|    |         \--- io.kindedj:kindedj:1.1.0
|    +--- com.univocity:univocity-parsers:2.8.1
|    \--- com.github.wumpz:diffutils:2.2
|         \--- org.eclipse.jgit:org.eclipse.jgit:4.4.1.201607150455-r
+--- org.mockito.kotlin:mockito-kotlin:3.2.0
|    \--- org.mockito:mockito-core:3.9.0 -> 5.11.0 (*)
\--- com.siteminder:sm-spring-boot-starter-test:11.0.2.RELEASE
     +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     +--- org.mock-server:mockserver-netty:5.15.0
     |    +--- org.mock-server:mockserver-client-java:5.15.0
     |    |    +--- org.mock-server:mockserver-core:5.15.0
     |    |    |    +--- com.lmax:disruptor:3.4.4
     |    |    |    +--- javax.servlet:javax.servlet-api:4.0.1
     |    |    |    +--- io.netty:netty-buffer:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-http:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-http2:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-socks:4.1.86.Final -> 4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-handler:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-handler-proxy:4.1.86.Final -> 4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-codec-socks:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-tcnative-boringssl-static:2.0.56.Final -> 2.0.66.Final
     |    |    |    |    \--- io.netty:netty-tcnative-classes:2.0.66.Final
     |    |    |    +--- com.jcraft:jzlib:1.1.3
     |    |    |    +--- com.fasterxml.uuid:java-uuid-generator:4.1.0
     |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    +--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    +--- org.bouncycastle:bcpkix-jdk18on:1.72
     |    |    |    |    +--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    |    \--- org.bouncycastle:bcutil-jdk18on:1.72
     |    |    |    |         \--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    +--- com.nimbusds:nimbus-jose-jwt:9.28
     |    |    |    |    \--- com.github.stephenc.jcip:jcip-annotations:1.0-1
     |    |    |    +--- org.apache.velocity:velocity-engine-scripting:2.3
     |    |    |    |    \--- org.apache.velocity:velocity-engine-core:2.3
     |    |    |    |         +--- org.apache.commons:commons-lang3:3.11 -> 3.14.0
     |    |    |    |         \--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
     |    |    |    +--- org.apache.velocity:velocity-engine-core:2.3 (*)
     |    |    |    +--- org.apache.velocity.tools:velocity-tools-generic:3.1
     |    |    |    |    +--- org.apache.velocity:velocity-engine-core:2.3 (*)
     |    |    |    |    +--- commons-beanutils:commons-beanutils:1.9.4
     |    |    |    |    |    +--- commons-logging:commons-logging:1.2
     |    |    |    |    |    \--- commons-collections:commons-collections:3.2.2
     |    |    |    |    +--- org.apache.commons:commons-digester3:3.2
     |    |    |    |    |    +--- commons-beanutils:commons-beanutils:1.8.3 -> 1.9.4 (*)
     |    |    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
     |    |    |    |    +--- org.apache.commons:commons-lang3:3.10 -> 3.14.0
     |    |    |    |    +--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
     |    |    |    |    \--- com.github.cliftonlabs:json-simple:3.0.2
     |    |    |    +--- com.samskivert:jmustache:1.15 -> 1.16
     |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- net.javacrumbs.json-unit:json-unit-core:2.36.0
     |    |    |    |    \--- org.hamcrest:hamcrest-core:2.2
     |    |    |    |         \--- org.hamcrest:hamcrest:2.2
     |    |    |    +--- com.networknt:json-schema-validator:1.0.76
     |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    \--- com.ethlo.time:itu:1.7.0
     |    |    |    +--- com.jayway.jsonpath:json-path:2.7.0 -> 2.9.0
     |    |    |    +--- io.swagger.parser.v3:swagger-parser:2.1.10
     |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-v2-converter:2.1.10
     |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9
     |    |    |    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.22 -> 2.0.16
     |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
     |    |    |    |    |    |    |    +--- org.yaml:snakeyaml:2.2
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
     |    |    |    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
     |    |    |    |    |    |    +--- io.swagger:swagger-models:1.6.9
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.22 -> 2.0.16
     |    |    |    |    |    |    |    \--- io.swagger:swagger-annotations:1.6.9
     |    |    |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |    |    \--- com.google.guava:guava:31.0.1-android -> 31.1-jre
     |    |    |    |    |    |         +--- com.google.guava:failureaccess:1.0.1
     |    |    |    |    |    |         +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    |    |    |    |         +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |         +--- org.checkerframework:checker-qual:3.12.0 -> 3.37.0
     |    |    |    |    |    |         +--- com.google.errorprone:error_prone_annotations:2.11.0 -> 2.21.1
     |    |    |    |    |    |         \--- com.google.j2objc:j2objc-annotations:1.3
     |    |    |    |    |    +--- io.swagger:swagger-parser:1.0.64
     |    |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9 (*)
     |    |    |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |    |    \--- commons-io:commons-io:2.11.0
     |    |    |    |    |    +--- io.swagger:swagger-compat-spec-parser:1.0.64
     |    |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9 (*)
     |    |    |    |    |    |    +--- io.swagger:swagger-parser:1.0.64 (*)
     |    |    |    |    |    |    +--- com.github.java-json-tools:json-schema-validator:2.2.14
     |    |    |    |    |    |    |    +--- com.google.guava:guava:28.2-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils-equivalence:1.0
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:json-schema-core:1.2.14
     |    |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    |    +--- com.google.guava:guava:28.2-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils:2.0
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils-equivalence:1.0
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:uri-template:0.10
     |    |    |    |    |    |    |    |    |    +--- com.google.guava:guava:28.1-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:msg-simple:1.2
     |    |    |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:btf:1.3
     |    |    |    |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |    +--- org.mozilla:rhino:1.7.7.2
     |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |    |    +--- com.sun.mail:mailapi:1.6.2
     |    |    |    |    |    |    |    +--- joda-time:joda-time:2.10.5
     |    |    |    |    |    |    |    +--- com.googlecode.libphonenumber:libphonenumber:8.11.1
     |    |    |    |    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |    |    \--- net.sf.jopt-simple:jopt-simple:5.0.4
     |    |    |    |    |    |    +--- com.github.java-json-tools:json-patch:1.13
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:msg-simple:1.2 (*)
     |    |    |    |    |    |    |    \--- com.github.java-json-tools:jackson-coreutils:2.0
     |    |    |    |    |    |    \--- org.apache.httpcomponents:httpclient:4.5.13
     |    |    |    |    |    |         +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
     |    |    |    |    |    |         +--- commons-logging:commons-logging:1.2
     |    |    |    |    |    |         \--- commons-codec:commons-codec:1.11 -> 1.16.1
     |    |    |    |    |    +--- io.swagger.core.v3:swagger-models:2.2.8
     |    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-core:2.1.10
     |    |    |    |    |    |    \--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |    \--- io.swagger.parser.v3:swagger-parser-v3:2.1.10
     |    |    |    |    |         +--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |         +--- io.swagger.core.v3:swagger-core:2.2.8
     |    |    |    |    |         |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 -> 4.0.2 (*)
     |    |    |    |    |         |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    |    |         |    +--- org.slf4j:slf4j-api:1.7.35 -> 2.0.16
     |    |    |    |    |         |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- io.swagger.core.v3:swagger-annotations:2.2.8 -> 2.2.22
     |    |    |    |    |         |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |         |    +--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |         |    \--- jakarta.validation:jakarta.validation-api:2.0.2 -> 3.0.2
     |    |    |    |    |         +--- io.swagger.parser.v3:swagger-parser-core:2.1.10 (*)
     |    |    |    |    |         +--- commons-io:commons-io:2.11.0
     |    |    |    |    |         +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         \--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2 (*)
     |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-v3:2.1.10 (*)
     |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    \--- commons-io:commons-io:2.11.0
     |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:3.0.1 -> 4.0.2 (*)
     |    |    |    +--- org.xmlunit:xmlunit-core:2.9.1
     |    |    |    +--- org.xmlunit:xmlunit-placeholders:2.9.1
     |    |    |    |    \--- org.xmlunit:xmlunit-core:2.9.1
     |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    +--- commons-io:commons-io:2.11.0
     |    |    |    +--- org.apache.commons:commons-text:1.10.0
     |    |    |    +--- commons-codec:commons-codec:1.15 -> 1.16.1
     |    |    |    +--- com.google.guava:guava:31.1-jre (*)
     |    |    |    +--- io.github.classgraph:classgraph:4.8.154
     |    |    |    +--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     |    |    |    +--- io.prometheus:simpleclient:0.16.0 (*)
     |    |    |    \--- io.prometheus:simpleclient_httpserver:0.16.0
     |    |    |         +--- io.prometheus:simpleclient:0.16.0 (*)
     |    |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
     |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    +--- com.google.guava:guava:31.1-jre (*)
     |    |    \--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     |    +--- org.mock-server:mockserver-core:5.15.0 (*)
     |    +--- io.netty:netty-buffer:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec-http:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec-http2:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-common:4.1.86.Final -> 4.1.114.Final
     |    +--- io.netty:netty-handler:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-transport:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-tcnative-boringssl-static:2.0.56.Final -> 2.0.66.Final (*)
     |    +--- commons-io:commons-io:2.11.0
     |    +--- com.google.guava:guava:31.1-jre (*)
     |    \--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20

testIntransitiveDependenciesMetadata
No dependencies

testKotlinScriptDef - Script filename extensions discovery classpath configuration
No dependencies

testKotlinScriptDefExtensions
No dependencies

testRuntimeClasspath - Runtime classpath of null/test.
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20
|    |    +--- org.jetbrains:annotations:13.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0 -> 1.9.20 (c)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0 -> 1.9.20 (c)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- org.jetbrains.kotlin:kotlin-reflect:1.9.20
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)
+--- com.fasterxml.jackson.module:jackson-module-kotlin -> 2.17.2
|    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2
|    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-kotlin:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (c)
|    |    |         +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 (c)
|    |    |         \--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.17.2 (c)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    +--- org.jetbrains.kotlin:kotlin-reflect:1.7.22 -> 1.9.20 (*)
|    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
+--- com.siteminder:sm-spring-boot-api-error-client-starter:11.0.2.RELEASE
|    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5
|    |    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15
|    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    \--- org.springframework:spring-context:6.1.14 -> 6.1.15
|    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6
|    |    |              \--- io.micrometer:micrometer-commons:1.13.6
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.5
|    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.5
|    |    |    +--- ch.qos.logback:logback-classic:1.5.11
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.11
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    |    \--- org.yaml:snakeyaml:2.2
|    +--- com.fasterxml.jackson.core:jackson-annotations -> 2.17.2 (*)
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.17.2 (*)
|    \--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
+--- com.siteminder:sm-spring-boot-http-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.5
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.6 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2
|    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
|    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |         \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.5
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.31
|    |    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.31
|    |    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.31
|    |    +--- org.springframework:spring-web:6.1.14 -> 6.1.15 (*)
|    |    \--- org.springframework:spring-webmvc:6.1.14 -> 6.1.15
|    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |         \--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.apache.httpcomponents.client5:httpclient5:5.3.1
|         +--- org.apache.httpcomponents.core5:httpcore5:5.2.4 -> 5.2.5
|         +--- org.apache.httpcomponents.core5:httpcore5-h2:5.2.4 -> 5.2.5
|         |    \--- org.apache.httpcomponents.core5:httpcore5:5.2.5
|         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
+--- com.siteminder:sm-spring-boot-jwt-client-starter:11.0.2.RELEASE
|    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    +--- com.siteminder:sm-spring-boot-s3-starter:11.0.2.RELEASE
|    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    +--- com.siteminder:sm-spring-boot-metrics-starter:11.0.2.RELEASE
|    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.5
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.5
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.5
|    |    |    |    |    |    \--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    |    |    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
|    |    |    |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    |    \--- io.micrometer:micrometer-jakarta9:1.13.6
|    |    |    |         +--- io.micrometer:micrometer-core:1.13.6
|    |    |    |         |    +--- io.micrometer:micrometer-commons:1.13.6
|    |    |    |         |    +--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    |         |    +--- org.hdrhistogram:HdrHistogram:2.2.2
|    |    |    |         |    \--- org.latencyutils:LatencyUtils:2.0.3
|    |    |    |         +--- io.micrometer:micrometer-commons:1.13.6
|    |    |    |         \--- io.micrometer:micrometer-observation:1.13.6 (*)
|    |    |    +--- com.siteminder:sm-metrics-core:11.0.2.RELEASE
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    |    +--- io.dropwizard.metrics:metrics-core:4.2.26
|    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    \--- io.prometheus:simpleclient_common:0.16.0
|    |    |    |         \--- io.prometheus:simpleclient:0.16.0
|    |    |    |              +--- io.prometheus:simpleclient_tracer_otel:0.16.0
|    |    |    |              |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    |              \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
|    |    |    |                   \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    \--- io.micrometer:micrometer-registry-prometheus-simpleclient:1.13.1 -> 1.13.6
|    |    |         +--- io.micrometer:micrometer-core:1.13.6 (*)
|    |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
|    |    +--- software.amazon.awssdk:s3:2.26.29
|    |    |    +--- software.amazon.awssdk:aws-xml-protocol:2.26.29
|    |    |    |    +--- software.amazon.awssdk:aws-query-protocol:2.26.29
|    |    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29
|    |    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29
|    |    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29
|    |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |         \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29
|    |    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:checksums:2.26.29
|    |    |    |    |    |    |    |         +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |         \--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    \--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:third-party-jackson-core:2.26.29
|    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    |    |    |    +--- software.amazon.awssdk:auth:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-aws-eventstream:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    |    \--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    |    |    |    +--- software.amazon.awssdk:retries:2.26.29 (*)
|    |    |    |    |    |    \--- software.amazon.eventstream:eventstream:1.0.1
|    |    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:protocol-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:arns:2.26.29
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:profiles:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:crt-core:2.26.29
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    \--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:identity-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-auth-aws:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:checksums:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:checksums-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:retries-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:sdk-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:auth:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:regions:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:aws-core:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:json-utils:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:endpoints-spi:2.26.29 (*)
|    |    |    +--- software.amazon.awssdk:apache-client:2.26.29
|    |    |    |    +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |    |    +--- software.amazon.awssdk:annotations:2.26.29
|    |    |    |    +--- org.apache.httpcomponents:httpclient:4.5.13
|    |    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
|    |    |    |    |    +--- commons-logging:commons-logging:1.2
|    |    |    |    |    \--- commons-codec:commons-codec:1.11 -> 1.16.1
|    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16
|    |    |    |    \--- commons-codec:commons-codec:1.17.1 -> 1.16.1
|    |    |    \--- software.amazon.awssdk:netty-nio-client:2.26.29
|    |    |         +--- software.amazon.awssdk:annotations:2.26.29
|    |    |         +--- software.amazon.awssdk:http-client-spi:2.26.29 (*)
|    |    |         +--- software.amazon.awssdk:utils:2.26.29 (*)
|    |    |         +--- software.amazon.awssdk:metrics-spi:2.26.29 (*)
|    |    |         +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.114.Final
|    |    |         |    +--- io.netty:netty-common:4.1.114.Final
|    |    |         |    +--- io.netty:netty-buffer:4.1.114.Final
|    |    |         |    |    \--- io.netty:netty-common:4.1.114.Final
|    |    |         |    +--- io.netty:netty-transport:4.1.114.Final
|    |    |         |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |         |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |         |    |    \--- io.netty:netty-resolver:4.1.114.Final
|    |    |         |    |         \--- io.netty:netty-common:4.1.114.Final
|    |    |         |    +--- io.netty:netty-codec:4.1.114.Final
|    |    |         |    |    +--- io.netty:netty-common:4.1.114.Final
|    |    |         |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |         |    |    \--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |         |    \--- io.netty:netty-handler:4.1.114.Final
|    |    |         |         +--- io.netty:netty-common:4.1.114.Final
|    |    |         |         +--- io.netty:netty-resolver:4.1.114.Final (*)
|    |    |         |         +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |         |         +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |         |         +--- io.netty:netty-transport-native-unix-common:4.1.114.Final
|    |    |         |         |    +--- io.netty:netty-common:4.1.114.Final
|    |    |         |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |         |         |    \--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |         |         \--- io.netty:netty-codec:4.1.114.Final (*)
|    |    |         +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.114.Final
|    |    |         |    +--- io.netty:netty-common:4.1.114.Final
|    |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |         |    +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |         |    +--- io.netty:netty-codec:4.1.114.Final (*)
|    |    |         |    +--- io.netty:netty-handler:4.1.114.Final (*)
|    |    |         |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
|    |    |         +--- io.netty:netty-codec:4.1.111.Final -> 4.1.114.Final (*)
|    |    |         +--- io.netty:netty-transport:4.1.111.Final -> 4.1.114.Final (*)
|    |    |         +--- io.netty:netty-common:4.1.111.Final -> 4.1.114.Final
|    |    |         +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.114.Final (*)
|    |    |         +--- io.netty:netty-handler:4.1.111.Final -> 4.1.114.Final (*)
|    |    |         +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.114.Final
|    |    |         |    +--- io.netty:netty-common:4.1.114.Final
|    |    |         |    +--- io.netty:netty-buffer:4.1.114.Final (*)
|    |    |         |    +--- io.netty:netty-transport:4.1.114.Final (*)
|    |    |         |    \--- io.netty:netty-transport-native-unix-common:4.1.114.Final (*)
|    |    |         +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.114.Final (*)
|    |    |         +--- org.reactivestreams:reactive-streams:1.0.4
|    |    |         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    +--- software.amazon.awssdk:netty-nio-client:2.26.29 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.5 (*)
|    \--- com.github.ben-manes.caffeine:caffeine:3.1.8
|         +--- org.checkerframework:checker-qual:3.37.0
|         \--- com.google.errorprone:error_prone_annotations:2.21.1
+--- org.springframework.boot:spring-boot-starter-test -> 3.3.5
|    +--- org.springframework.boot:spring-boot-starter:3.3.5 (*)
|    +--- org.springframework.boot:spring-boot-test:3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    \--- org.springframework:spring-test:6.1.14 -> 6.1.15
|    |         \--- org.springframework:spring-core:6.1.15 (*)
|    +--- org.springframework.boot:spring-boot-test-autoconfigure:3.3.5
|    |    +--- org.springframework.boot:spring-boot:3.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-test:3.3.5 (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.5 (*)
|    +--- com.jayway.jsonpath:json-path:2.9.0
|    |    +--- net.minidev:json-smart:2.5.0 -> 2.5.1
|    |    |    \--- net.minidev:accessors-smart:2.5.1
|    |    |         \--- org.ow2.asm:asm:9.6
|    |    \--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2
|    |    \--- jakarta.activation:jakarta.activation-api:2.1.3
|    +--- net.minidev:json-smart:2.5.1 (*)
|    +--- org.assertj:assertj-core:3.25.3
|    |    \--- net.bytebuddy:byte-buddy:1.14.11 -> 1.14.19
|    +--- org.awaitility:awaitility:4.2.2
|    |    \--- org.hamcrest:hamcrest:2.1 -> 2.2
|    +--- org.hamcrest:hamcrest:2.2
|    +--- org.junit.jupiter:junit-jupiter:5.10.5
|    |    +--- org.junit:junit-bom:5.10.5
|    |    |    +--- org.junit.jupiter:junit-jupiter:5.10.5 (c)
|    |    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5 (c)
|    |    |    +--- org.junit.jupiter:junit-jupiter-engine:5.10.5 (c)
|    |    |    +--- org.junit.jupiter:junit-jupiter-params:5.10.5 (c)
|    |    |    +--- org.junit.platform:junit-platform-commons:1.10.5 (c)
|    |    |    \--- org.junit.platform:junit-platform-engine:1.10.5 (c)
|    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5
|    |    |    +--- org.junit:junit-bom:5.10.5 (*)
|    |    |    +--- org.opentest4j:opentest4j:1.3.0
|    |    |    \--- org.junit.platform:junit-platform-commons:1.10.5
|    |    |         \--- org.junit:junit-bom:5.10.5 (*)
|    |    +--- org.junit.jupiter:junit-jupiter-params:5.10.5
|    |    |    +--- org.junit:junit-bom:5.10.5 (*)
|    |    |    \--- org.junit.jupiter:junit-jupiter-api:5.10.5 (*)
|    |    \--- org.junit.jupiter:junit-jupiter-engine:5.10.5
|    |         +--- org.junit:junit-bom:5.10.5 (*)
|    |         +--- org.junit.platform:junit-platform-engine:1.10.5
|    |         |    +--- org.junit:junit-bom:5.10.5 (*)
|    |         |    +--- org.opentest4j:opentest4j:1.3.0
|    |         |    \--- org.junit.platform:junit-platform-commons:1.10.5 (*)
|    |         \--- org.junit.jupiter:junit-jupiter-api:5.10.5 (*)
|    +--- org.mockito:mockito-core:5.11.0
|    |    +--- net.bytebuddy:byte-buddy:1.14.12 -> 1.14.19
|    |    +--- net.bytebuddy:byte-buddy-agent:1.14.12 -> 1.14.19
|    |    \--- org.objenesis:objenesis:3.3
|    +--- org.mockito:mockito-junit-jupiter:5.11.0
|    |    +--- org.mockito:mockito-core:5.11.0 (*)
|    |    \--- org.junit.jupiter:junit-jupiter-api:5.10.2 -> 5.10.5 (*)
|    +--- org.skyscreamer:jsonassert:1.5.3
|    |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
|    +--- org.springframework:spring-core:6.1.14 -> 6.1.15 (*)
|    +--- org.springframework:spring-test:6.1.14 -> 6.1.15 (*)
|    \--- org.xmlunit:xmlunit-core:2.9.1
+--- io.kotlintest:kotlintest-assertions:3.4.2
|    +--- org.jetbrains.kotlin:kotlin-reflect:********* -> 1.9.20 (*)
|    +--- io.arrow-kt:arrow-core-data:0.9.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.11 -> 1.9.20 (*)
|    |    \--- io.arrow-kt:arrow-annotations:0.9.0
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.11 -> 1.9.20 (*)
|    |         \--- io.kindedj:kindedj:1.1.0
|    +--- com.univocity:univocity-parsers:2.8.1
|    \--- com.github.wumpz:diffutils:2.2
|         \--- org.eclipse.jgit:org.eclipse.jgit:4.4.1.201607150455-r
+--- org.mockito.kotlin:mockito-kotlin:3.2.0
|    \--- org.mockito:mockito-core:3.9.0 -> 5.11.0 (*)
\--- com.siteminder:sm-spring-boot-starter-test:11.0.2.RELEASE
     +--- com.siteminder:sm-spring-boot-metrics-starter:11.0.2.RELEASE (*)
     +--- org.springframework.boot:spring-boot-starter-test:3.3.2 -> 3.3.5 (*)
     +--- org.apache.commons:commons-text:1.12.0
     |    \--- org.apache.commons:commons-lang3:3.14.0
     +--- jakarta.persistence:jakarta.persistence-api:3.1.0
     +--- org.springframework:spring-jdbc -> 6.1.15
     |    +--- org.springframework:spring-beans:6.1.15 (*)
     |    +--- org.springframework:spring-core:6.1.15 (*)
     |    \--- org.springframework:spring-tx:6.1.15
     |         +--- org.springframework:spring-beans:6.1.15 (*)
     |         \--- org.springframework:spring-core:6.1.15 (*)
     +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
     +--- org.mock-server:mockserver-netty:5.15.0
     |    +--- org.mock-server:mockserver-client-java:5.15.0
     |    |    +--- org.mock-server:mockserver-core:5.15.0
     |    |    |    +--- com.lmax:disruptor:3.4.4
     |    |    |    +--- javax.servlet:javax.servlet-api:4.0.1
     |    |    |    +--- io.netty:netty-buffer:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-http:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-http2:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-codec-socks:4.1.86.Final -> 4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-handler:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-handler-proxy:4.1.86.Final -> 4.1.114.Final
     |    |    |    |    +--- io.netty:netty-common:4.1.114.Final
     |    |    |    |    +--- io.netty:netty-buffer:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-transport:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-codec:4.1.114.Final (*)
     |    |    |    |    +--- io.netty:netty-codec-socks:4.1.114.Final (*)
     |    |    |    |    \--- io.netty:netty-codec-http:4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-transport:4.1.86.Final -> 4.1.114.Final (*)
     |    |    |    +--- io.netty:netty-tcnative-boringssl-static:2.0.56.Final -> 2.0.66.Final
     |    |    |    |    \--- io.netty:netty-tcnative-classes:2.0.66.Final
     |    |    |    +--- com.jcraft:jzlib:1.1.3
     |    |    |    +--- com.fasterxml.uuid:java-uuid-generator:4.1.0
     |    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    +--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    +--- org.bouncycastle:bcpkix-jdk18on:1.72
     |    |    |    |    +--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    |    \--- org.bouncycastle:bcutil-jdk18on:1.72
     |    |    |    |         \--- org.bouncycastle:bcprov-jdk18on:1.72
     |    |    |    +--- com.nimbusds:nimbus-jose-jwt:9.28
     |    |    |    |    \--- com.github.stephenc.jcip:jcip-annotations:1.0-1
     |    |    |    +--- org.apache.velocity:velocity-engine-scripting:2.3
     |    |    |    |    \--- org.apache.velocity:velocity-engine-core:2.3
     |    |    |    |         +--- org.apache.commons:commons-lang3:3.11 -> 3.14.0
     |    |    |    |         \--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
     |    |    |    +--- org.apache.velocity:velocity-engine-core:2.3 (*)
     |    |    |    +--- org.apache.velocity.tools:velocity-tools-generic:3.1
     |    |    |    |    +--- org.apache.velocity:velocity-engine-core:2.3 (*)
     |    |    |    |    +--- commons-beanutils:commons-beanutils:1.9.4
     |    |    |    |    |    +--- commons-logging:commons-logging:1.2
     |    |    |    |    |    \--- commons-collections:commons-collections:3.2.2
     |    |    |    |    +--- org.apache.commons:commons-digester3:3.2
     |    |    |    |    |    +--- commons-beanutils:commons-beanutils:1.8.3 -> 1.9.4 (*)
     |    |    |    |    |    \--- commons-logging:commons-logging:1.1.1 -> 1.2
     |    |    |    |    +--- org.apache.commons:commons-lang3:3.10 -> 3.14.0
     |    |    |    |    +--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
     |    |    |    |    \--- com.github.cliftonlabs:json-simple:3.0.2
     |    |    |    +--- com.samskivert:jmustache:1.15 -> 1.16
     |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.1 -> 2.17.2 (*)
     |    |    |    +--- net.javacrumbs.json-unit:json-unit-core:2.36.0
     |    |    |    |    \--- org.hamcrest:hamcrest-core:2.2
     |    |    |    |         \--- org.hamcrest:hamcrest:2.2
     |    |    |    +--- com.networknt:json-schema-validator:1.0.76
     |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
     |    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    |    \--- com.ethlo.time:itu:1.7.0
     |    |    |    +--- com.jayway.jsonpath:json-path:2.7.0 -> 2.9.0 (*)
     |    |    |    +--- io.swagger.parser.v3:swagger-parser:2.1.10
     |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-v2-converter:2.1.10
     |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9
     |    |    |    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.22 -> 2.0.16
     |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 (*)
     |    |    |    |    |    |    |    +--- org.yaml:snakeyaml:2.2
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 (*)
     |    |    |    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.2 (*)
     |    |    |    |    |    |    +--- io.swagger:swagger-models:1.6.9
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    +--- org.slf4j:slf4j-api:1.7.22 -> 2.0.16
     |    |    |    |    |    |    |    \--- io.swagger:swagger-annotations:1.6.9
     |    |    |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |    |    \--- com.google.guava:guava:31.0.1-android -> 31.1-jre
     |    |    |    |    |    |         +--- com.google.guava:failureaccess:1.0.1
     |    |    |    |    |    |         +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    |    |    |    |         +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |         +--- org.checkerframework:checker-qual:3.12.0 -> 3.37.0
     |    |    |    |    |    |         +--- com.google.errorprone:error_prone_annotations:2.11.0 -> 2.21.1
     |    |    |    |    |    |         \--- com.google.j2objc:j2objc-annotations:1.3
     |    |    |    |    |    +--- io.swagger:swagger-parser:1.0.64
     |    |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9 (*)
     |    |    |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |    |    \--- commons-io:commons-io:2.11.0
     |    |    |    |    |    +--- io.swagger:swagger-compat-spec-parser:1.0.64
     |    |    |    |    |    |    +--- io.swagger:swagger-core:1.6.9 (*)
     |    |    |    |    |    |    +--- io.swagger:swagger-parser:1.0.64 (*)
     |    |    |    |    |    |    +--- com.github.java-json-tools:json-schema-validator:2.2.14
     |    |    |    |    |    |    |    +--- com.google.guava:guava:28.2-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils-equivalence:1.0
     |    |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    |    +--- com.google.guava:guava:28.2-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    |    \--- com.github.java-json-tools:jackson-coreutils:2.0
     |    |    |    |    |    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.11.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    |         +--- com.github.java-json-tools:msg-simple:1.2
     |    |    |    |    |    |    |    |         |    +--- com.github.java-json-tools:btf:1.3
     |    |    |    |    |    |    |    |         |    |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |         |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |         \--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:json-schema-core:1.2.14
     |    |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    |    +--- com.google.guava:guava:28.2-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils:2.0 (*)
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:jackson-coreutils-equivalence:1.0 (*)
     |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:uri-template:0.10
     |    |    |    |    |    |    |    |    |    +--- com.google.guava:guava:28.1-android -> 31.1-jre (*)
     |    |    |    |    |    |    |    |    |    +--- com.github.java-json-tools:msg-simple:1.2 (*)
     |    |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:2.0.1 -> 3.0.2
     |    |    |    |    |    |    |    |    +--- org.mozilla:rhino:1.7.7.2
     |    |    |    |    |    |    |    |    \--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |    |    +--- com.sun.mail:mailapi:1.6.2
     |    |    |    |    |    |    |    +--- joda-time:joda-time:2.10.5
     |    |    |    |    |    |    |    +--- com.googlecode.libphonenumber:libphonenumber:8.11.1
     |    |    |    |    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |    |    |    |    \--- net.sf.jopt-simple:jopt-simple:5.0.4
     |    |    |    |    |    |    +--- com.github.java-json-tools:json-patch:1.13
     |    |    |    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.11.0 -> 2.17.2 (*)
     |    |    |    |    |    |    |    +--- com.github.java-json-tools:msg-simple:1.2 (*)
     |    |    |    |    |    |    |    \--- com.github.java-json-tools:jackson-coreutils:2.0 (*)
     |    |    |    |    |    |    \--- org.apache.httpcomponents:httpclient:4.5.13 (*)
     |    |    |    |    |    +--- io.swagger.core.v3:swagger-models:2.2.8
     |    |    |    |    |    |    \--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-core:2.1.10
     |    |    |    |    |    |    \--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |    \--- io.swagger.parser.v3:swagger-parser-v3:2.1.10
     |    |    |    |    |         +--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |         +--- io.swagger.core.v3:swagger-core:2.2.8
     |    |    |    |    |         |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 -> 4.0.2 (*)
     |    |    |    |    |         |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    |    |         |    +--- org.slf4j:slf4j-api:1.7.35 -> 2.0.16
     |    |    |    |    |         |    +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         |    +--- io.swagger.core.v3:swagger-annotations:2.2.8 -> 2.2.22
     |    |    |    |    |         |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |         |    +--- io.swagger.core.v3:swagger-models:2.2.8 (*)
     |    |    |    |    |         |    \--- jakarta.validation:jakarta.validation-api:2.0.2 -> 3.0.2
     |    |    |    |    |         +--- io.swagger.parser.v3:swagger-parser-core:2.1.10 (*)
     |    |    |    |    |         +--- commons-io:commons-io:2.11.0
     |    |    |    |    |         +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.14.0 -> 2.17.2 (*)
     |    |    |    |    |         \--- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.0 -> 2.17.2 (*)
     |    |    |    |    +--- io.swagger.parser.v3:swagger-parser-v3:2.1.10 (*)
     |    |    |    |    +--- org.yaml:snakeyaml:1.33 -> 2.2
     |    |    |    |    \--- commons-io:commons-io:2.11.0
     |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:3.0.1 -> 4.0.2 (*)
     |    |    |    +--- com.sun.xml.bind:jaxb-impl:4.0.1 -> 4.0.5
     |    |    |    |    \--- com.sun.xml.bind:jaxb-core:4.0.5
     |    |    |    |         +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
     |    |    |    |         \--- org.eclipse.angus:angus-activation:2.0.2
     |    |    |    |              \--- jakarta.activation:jakarta.activation-api:2.1.3
     |    |    |    +--- org.xmlunit:xmlunit-core:2.9.1
     |    |    |    +--- org.xmlunit:xmlunit-placeholders:2.9.1
     |    |    |    |    \--- org.xmlunit:xmlunit-core:2.9.1
     |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    |    +--- commons-io:commons-io:2.11.0
     |    |    |    +--- org.apache.commons:commons-text:1.10.0 -> 1.12.0 (*)
     |    |    |    +--- commons-codec:commons-codec:1.15 -> 1.16.1
     |    |    |    +--- com.google.guava:guava:31.1-jre (*)
     |    |    |    +--- io.github.classgraph:classgraph:4.8.154
     |    |    |    +--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     |    |    |    +--- io.prometheus:simpleclient:0.16.0 (*)
     |    |    |    \--- io.prometheus:simpleclient_httpserver:0.16.0
     |    |    |         +--- io.prometheus:simpleclient:0.16.0 (*)
     |    |    |         \--- io.prometheus:simpleclient_common:0.16.0 (*)
     |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
     |    |    +--- com.google.guava:guava:31.1-jre (*)
     |    |    \--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     |    +--- org.mock-server:mockserver-core:5.15.0 (*)
     |    +--- io.netty:netty-buffer:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec-http:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-codec-http2:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-common:4.1.86.Final -> 4.1.114.Final
     |    +--- io.netty:netty-handler:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-transport:4.1.86.Final -> 4.1.114.Final (*)
     |    +--- io.netty:netty-tcnative-boringssl-static:2.0.56.Final -> 2.0.66.Final (*)
     |    +--- commons-io:commons-io:2.11.0
     |    +--- com.google.guava:guava:31.1-jre (*)
     |    \--- org.slf4j:slf4j-api:2.0.6 -> 2.0.16
     \--- org.jetbrains.kotlin:kotlin-stdlib:1.9.20 (*)

testRuntimeOnly - Runtime only dependencies for null/test. (n)
No dependencies

(c) - A dependency constraint, not a dependency. The dependency affected by the constraint occurs elsewhere in the tree.
(*) - Indicates repeated occurrences of a transitive dependency subtree. Gradle expands transitive dependency subtrees only once per project; repeat occurrences only display the root of the subtree, followed by this annotation.

(n) - A dependency or dependency configuration that cannot be resolved.

A web-based, searchable dependency report is available by adding the --scan option.

1 actionable task: 1 executed

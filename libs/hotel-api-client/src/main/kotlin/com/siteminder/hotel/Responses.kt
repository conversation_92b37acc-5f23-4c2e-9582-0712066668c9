package com.siteminder.hotel

import com.fasterxml.jackson.annotation.JsonValue
import java.math.BigDecimal
import java.time.ZonedDateTime

data class HotelResponse(
    val spid: String,
    val latitude: BigDecimal,
    val longitude: BigDecimal,
    val countryCode: String,
    val suggestedRadius: BigDecimal,
    val featureImportance: List<FeatureImportanceResponse>? = null,
    val featureImportanceUpdatedAt: ZonedDateTime? = null,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime
) {
    data class FeatureImportanceResponse(
        val category: Category,
        val pvalue: Double,
        val important: Boolean
    ) {
        enum class Category(@get:JsonValue val value: String) {
            observances("observances"),
            public_holidays("public-holidays"),
            concerts("concerts"),
            sports("sports"),
            community("community"),
            expos("expos"),
            performing_arts("performing-arts"),
            conferences("conferences"),
            school_holidays("school-holidays"),
            festivals("festivals"),
            severe_weather("severe-weather"),
            academic("academic")
        }
    }
}

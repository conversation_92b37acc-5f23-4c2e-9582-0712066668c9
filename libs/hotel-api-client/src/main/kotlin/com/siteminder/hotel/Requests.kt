package com.siteminder.hotel

import com.fasterxml.jackson.annotation.JsonValue
import java.math.BigDecimal

data class CreateHotelRequest(
    val spid: String,
    val latitude: BigDecimal,
    val longitude: BigDecimal,
    val countryCode: String
)

data class UpdateHotelRequest(
    val latitude: BigDecimal,
    val longitude: BigDecimal,
    val countryCode: String
)

data class FeatureImportanceRequest(
    val category: Category,
    val pvalue: BigDecimal,
    val important: Boolean
) {
    enum class Category(@get:JsonValue val value: String) {
        observances("observances"),
        public_holidays("public-holidays"),
        concerts("concerts"),
        sports("sports"),
        community("community"),
        expos("expos"),
        performing_arts("performing-arts"),
        conferences("conferences"),
        school_holidays("school-holidays"),
        festivals("festivals"),
        severe_weather("severe-weather"),
        academic("academic")
    }
}

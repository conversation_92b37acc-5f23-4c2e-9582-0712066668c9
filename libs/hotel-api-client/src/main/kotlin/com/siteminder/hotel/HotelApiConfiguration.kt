package com.siteminder.hotel

import com.siteminder.api.client.error.ErrorResponseParser
import com.siteminder.http.RestTemplateBuilderCustomiser
import com.siteminder.security.JwtRepository
import com.siteminder.security.JwtRequestInterceptors
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(HotelApiProperties::class)
class HotelApiConfiguration {

    @Bean
    fun hotelApiClient(
        properties: HotelApiProperties,
        restTemplateBuilderCustomiser: RestTemplateBuilderCustomiser,
        jwtRepository: JwtRepository,
        errorResponseParser: ErrorResponseParser
    ): HotelApiClient {

        val restTemplate = restTemplateBuilderCustomiser.custom()
            .connectTimeoutInSeconds(properties.timeoutInSeconds)
            .socketTimeoutInSeconds(properties.timeoutInSeconds)
            .maxConnections(properties.concurrency)
            .build()
            .rootUri(properties.baseUrl)
            .interceptors(JwtRequestInterceptors.simpleJwtInterceptor(jwtRepository, properties.tokenPath))
            .build()

        return HotelApiClient(restTemplate, errorResponseParser)
    }
}

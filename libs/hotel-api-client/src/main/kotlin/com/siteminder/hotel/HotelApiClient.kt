package com.siteminder.hotel

import com.siteminder.api.client.error.ErrorResponseParser
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.exchange

class HotelApiClient(private val restTemplate: RestTemplate, private val errorResponseParser: ErrorResponseParser) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getHotel(spid: String, traceToken: String): HotelResponse? {
        try {
            val httpResponse = restTemplate.exchange<HotelResponse>("$API_HOTELS_ENDPOINT/{spid}", HttpMethod.GET, HttpEntity<String>(headers(traceToken)), mapOf("spid" to spid))
            return httpResponse.body ?: throw HotelApiClientException("predicthq/hotel-api response body was null")
        } catch (e: Exception) {
            if (e is HttpClientErrorException && e.statusCode == HttpStatus.NOT_FOUND && isNotFound(e.responseBodyAsString)) return null

            logger.error("Error calling predicthq/hotel-api, get hotel", e)
            throw HotelApiClientException("Failed request to predicthq/hotel-api. ${e.message}", e)
        }
    }

    fun createHotel(request: CreateHotelRequest, traceToken: String): HotelResponse {
        try {
            val httpResponse = restTemplate.exchange<HotelResponse>("$API_HOTELS_ENDPOINT", HttpMethod.POST, HttpEntity<CreateHotelRequest>(request, headers(traceToken)))
            return httpResponse.body ?: throw HotelApiClientException("predicthq/hotel-api response body was null")
        } catch (e: Exception) {
            logger.error("Error calling predicthq/hotel-api, create hotel", e)
            throw HotelApiClientException("Failed request to predicthq/hotel-api. ${e.message}", e)
        }
    }

    fun updateHotel(spid: String, request: UpdateHotelRequest, traceToken: String): HotelResponse? {
        try {
            val httpResponse = restTemplate.exchange<HotelResponse>("$API_HOTELS_ENDPOINT/{spid}", HttpMethod.PUT, HttpEntity<UpdateHotelRequest>(request, headers(traceToken)), mapOf("spid" to spid))
            return httpResponse.body ?: throw HotelApiClientException("predicthq/hotel-api response body was null")
        } catch (e: Exception) {
            if (e is HttpClientErrorException && e.statusCode == HttpStatus.NOT_FOUND && isNotFound(e.responseBodyAsString)) return null

            logger.error("Error calling predicthq/hotel-api, update hotel", e)
            throw HotelApiClientException("Failed request to predicthq/hotel-api. ${e.message}", e)
        }
    }

    fun updateFeatureImportance(spid: String, featureImportanceList: List<FeatureImportanceRequest>, traceToken: String): HotelResponse? {
        try {
            val httpResponse = restTemplate.exchange<HotelResponse>("$API_HOTELS_ENDPOINT/{spid}/feature-importance", HttpMethod.PUT, HttpEntity<List<FeatureImportanceRequest>>(featureImportanceList, headers(traceToken)), mapOf("spid" to spid))
            return httpResponse.body ?: throw HotelApiClientException("predicthq/hotel-api response body was null")
        } catch (e: Exception) {
            if (e is HttpClientErrorException && e.statusCode == HttpStatus.NOT_FOUND && isNotFound(e.responseBodyAsString)) return null

            logger.error("Error calling predicthq/hotel-api, update feature importance", e)
            throw HotelApiClientException("Failed request to predicthq/hotel-api. ${e.message}", e)
        }
    }

    fun markFeatureImportanceAttempt(spid: String, traceToken: String) {
        try {
            restTemplate.exchange<HotelResponse>("$API_HOTELS_ENDPOINT/{spid}/mark-feature-importance-attempt", HttpMethod.PUT, HttpEntity<Void>(headers(traceToken)), mapOf("spid" to spid))
        } catch (e: Exception) {
            if (e is HttpClientErrorException && e.statusCode == HttpStatus.NOT_FOUND && isNotFound(e.responseBodyAsString)) return

            logger.error("Error calling predicthq/hotel-api, marking feature importance last attempt at", e)
            throw HotelApiClientException("Failed request to predicthq/hotel-api. ${e.message}", e)
        }
    }

    private fun isNotFound(content: String): Boolean {
        val errorResponse = errorResponseParser.parseOrNull(content)
        return (errorResponse != null && errorResponse.errors.any { it.code == "NOT_FOUND" })
    }

    private fun headers(traceToken: String) = HttpHeaders().apply {
        contentType = MediaType.APPLICATION_JSON
        set("X-SM-TRACE-TOKEN", traceToken)
    }

    companion object {
        private const val API_HOTELS_ENDPOINT = "/api/hotels"
    }
}

@ConfigurationProperties("predicthq.hotel-api")
data class HotelApiProperties(
    val baseUrl: String,
    val tokenPath: String,
    val concurrency: Int = 500,
    val timeoutInSeconds: Int = 60
)

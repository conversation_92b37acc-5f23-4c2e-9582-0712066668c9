package com.siteminder

import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse
import org.mockserver.model.JsonBody.json
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus

fun apiRequest(path: String, body: String? = null, httpMethod: HttpMethod = HttpMethod.GET, traceToken: String? = null, additionalHeaders: Map<String, String> = mapOf(), queryParams: Map<String, String> = mapOf()): HttpRequest =
    HttpRequest.request()
        .withMethod(httpMethod.name())
        .withPath(path)
        .apply {
            additionalHeaders.forEach {
                withHeader(it.key, it.value)
            }

            queryParams.forEach {
                withQueryStringParameter(it.key, it.value)
            }

            if (body != null) {
                withContentType(org.mockserver.model.MediaType.APPLICATION_JSON)
                withBody(json(body))
            }

            if (traceToken != null) {
                withHeader("X-SM-TRACE-TOKEN", traceToken)
            }
        }

fun apiResponse(status: HttpStatus = HttpStatus.OK, body: String? = null): HttpResponse = HttpResponse.response()
    .withStatusCode(status.value())
    .withContentType(org.mockserver.model.MediaType.APPLICATION_JSON)
    .apply {
        if (body != null) {
            withBody(body)
        }
    }

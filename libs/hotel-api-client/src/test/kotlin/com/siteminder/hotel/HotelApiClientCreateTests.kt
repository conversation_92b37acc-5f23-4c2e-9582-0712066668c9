package com.siteminder.hotel

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.api.client.error.ErrorResponseParser
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*


@ExtendWith(MockServerExtension::class)
class HotelApiClientCreateTests {

    private lateinit var client: HotelApiClient

    private lateinit var mockServerClient: MockServerClient

    val spid = "spid123"
    val latitude = 234.098.toBigDecimal()
    val longitude = (-73.456).toBigDecimal()
    val countryCode = "AU"
    val traceToken = UUID.randomUUID().toString()

    @BeforeEach
    fun setUp() {

        val restTemplate = RestTemplateBuilder()
            .rootUri("http://${mockServerClient.remoteAddress().hostName}:${mockServerClient.remoteAddress().port}")
            .build()

        val errorResponseParser = ErrorResponseParser(jacksonObjectMapper())
        client = HotelApiClient(restTemplate, errorResponseParser)
    }

    @Test
    fun `create hotel should return hotel response`() {
        val today = ZonedDateTime.now(ZoneId.of("UTC").normalized())

        val request = apiRequest(
            path = "/api/hotels", httpMethod = HttpMethod.POST,
            body = """
            {
              "spid": "$spid",
              "latitude": $latitude,
              "longitude": $longitude,
              "countryCode": "$countryCode"
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = """
                    {
                      "spid": "$spid",
                      "latitude": $latitude,
                      "longitude": $longitude,
                      "countryCode": "$countryCode",
                      "suggestedRadius": 3.3,
                      "featureImportance": [
                        {
                          "category": "observances",
                          "pvalue": 0.1,
                          "important": true
                        },
                        {
                          "category": "sports",
                          "pvalue": 0.65,
                          "important": false
                        }
                      ],
                      "featureImportanceUpdatedAt": "$today",
                      "createdAt": "$today",
                      "updatedAt": "$today"
                    }
                    """.trimIndent()
                )
            )

        val response = client.createHotel(
            CreateHotelRequest(
                spid = spid,
                latitude = latitude,
                longitude = longitude,
                countryCode = countryCode
            ),
            traceToken
        )

        response shouldBe HotelResponse(
            spid = spid,
            latitude = latitude,
            longitude = longitude,
            countryCode = countryCode,
            suggestedRadius = 3.3.toBigDecimal(),
            featureImportance = listOf(
                HotelResponse.FeatureImportanceResponse(
                    category = HotelResponse.FeatureImportanceResponse.Category.observances,
                    pvalue = 0.1,
                    important = true
                ),
                HotelResponse.FeatureImportanceResponse(
                    category = HotelResponse.FeatureImportanceResponse.Category.sports,
                    pvalue = 0.65,
                    important = false
                )
            ),
            featureImportanceUpdatedAt = today,
            createdAt = today,
            updatedAt = today
        )
    }

    @Test
    fun `create hotel should throw ServerException when response payload is null`() {
        val request = apiRequest(
            path = "/api/hotels", httpMethod = HttpMethod.POST,
            body = """
            {
              "spid": "$spid",
              "latitude": $latitude,
              "longitude": $longitude,
              "countryCode": "$countryCode"
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = null
                )
            )

        shouldThrow<HotelApiClientException> {
            client.createHotel(
                CreateHotelRequest(
                    spid = spid,
                    latitude = latitude,
                    longitude = longitude,
                    countryCode = countryCode
                ),
                traceToken
            )
        }.also {
            it.message shouldBe "Failed request to predicthq/hotel-api. predicthq/hotel-api response body was null"
        }
    }

    @Test
    fun `create hotel should throw ServerException when api respond with non-successful http code`() {
        val request = apiRequest(
            path = "/api/hotels", httpMethod = HttpMethod.POST,
            body = """
            {
              "spid": "$spid",
              "latitude": $latitude,
              "longitude": $longitude,
              "countryCode": "$countryCode"
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.BAD_REQUEST)
            )

        shouldThrow<HotelApiClientException> {
            client.createHotel(
                CreateHotelRequest(
                    spid = spid,
                    latitude = latitude,
                    longitude = longitude,
                    countryCode = countryCode
                ),
                traceToken
            )
        }.also {
            it.message shouldBe "Failed request to predicthq/hotel-api. 400 Bad Request: [no body]"
        }
    }
}

package com.siteminder.hotel

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.api.client.error.ErrorResponseParser
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldNotThrowAny
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import java.util.*

@ExtendWith(MockServerExtension::class)
class HotelApiClientMarkFeatureImportanceAttemptTests {

    private lateinit var client: HotelApiClient

    private lateinit var mockServerClient: MockServerClient

    val spid = "spid123"
    val traceToken = UUID.randomUUID().toString()

    @BeforeEach
    fun setUp() {

        val restTemplate = RestTemplateBuilder()
            .rootUri("http://${mockServerClient.remoteAddress().hostName}:${mockServerClient.remoteAddress().port}")
            .build()

        val errorResponseParser = ErrorResponseParser(jacksonObjectMapper())
        client = HotelApiClient(restTemplate, errorResponseParser)
    }

    @Test
    fun `should return hotel response`() {
        val request = apiRequest(path = "/api/hotels/$spid/mark-feature-importance-attempt", httpMethod = HttpMethod.PUT)

        mockServerClient.`when`(request).respond(apiResponse())

        shouldNotThrowAny { client.markFeatureImportanceAttempt(spid, traceToken) }

    }

    @Test
    fun `should not throw exception when api returns 404`() {

        val request = apiRequest(path = "/api/hotels/$spid/mark-feature-importance-attempt", httpMethod = HttpMethod.PUT)

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.NOT_FOUND,
                    body = """
                {
                  "errors": [
                    {
                      "code": "NOT_FOUND",
                      "message": "Hotel not found",
                      "meta": {
                        "spid": "$spid",
                        "entity": "Hotel"
                      }
                    }
                  ]
                }
            """.trimIndent()
                )
            )

        shouldNotThrowAny { client.markFeatureImportanceAttempt(spid, traceToken) }
    }

    @Test
    fun `should throw ServerException when api respond with non-successful http code`() {

        val request = apiRequest(path = "/api/hotels/$spid/mark-feature-importance-attempt", httpMethod = HttpMethod.PUT)

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.BAD_REQUEST)
            )

        shouldThrow<HotelApiClientException> {
            client.markFeatureImportanceAttempt(spid, traceToken)
        }.also {
            it.message shouldBe "Failed request to predicthq/hotel-api. 400 Bad Request: [no body]"
        }
    }
}

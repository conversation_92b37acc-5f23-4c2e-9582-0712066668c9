package com.siteminder.predicthq

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.exchange
import java.math.BigDecimal

class PredictHqClient(private val restTemplate: RestTemplate, private val objectMapper: ObjectMapper) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getSuggestedRadius(lat: BigDecimal, lon: BigDecimal): BigDecimal {
        val uriVariables = mapOf(
            "location" to "$lat,$lon",
            "radiusUnit" to "km",
            "industry" to "accommodation"
        )

        try {
            val response = restTemplate.exchange<SuggestedRadiusResponse>("/suggested-radius?location.origin={location}&radius_unit={radiusUnit}&industry={industry}", HttpMethod.GET, HttpEntity<String>(headers()), uriVariables)
            return response.body?.radius ?: throw PredictHqClientException("predicthq/api response body was null")
        } catch (e: Exception) {
            logger.error("Error calling predicthq/api, get suggested radius", e)
            throw e
        }
    }

    fun getFeatureImportance(analysisId: String): FeatureImportanceResponse {
        val uriVariables = mapOf(
            "analysisId" to analysisId,
        )

        try {
            val response = restTemplate.exchange<FeatureImportanceResponse>("/v1/beam/analyses/{analysisId}/feature-importance", HttpMethod.GET, HttpEntity<String>(headers()), uriVariables)
            return response.body ?: throw PredictHqClientException("predicthq/api response body was null")
        } catch (e: Exception) {
            logger.error("Error calling predicthq/api, get feature importance", e)

            when (e) {
                is BadRequestException -> {
                    val errorResponse = objectMapper.readValueSilently<ErrorResponse>(e.response)
                    if (errorResponse?.error?.contains("Feature Importance results are not ready", true) == true) {
                        logger.info("Feature importance results are not yet ready for analysisId: $analysisId")
                        throw FeatureImportanceNotReadyException("Feature Importance results are not ready for analysisId: $analysisId")
                    }
                }
                is NotFoundException -> {
                    if (isAnalysisNotFound(e.response)) {
                        throw AnalysisNotFoundException("Analysis not found for analysisId: $analysisId")
                    }
                }
                else -> throw e
            }

            throw e
        }
    }

    fun uploadDemandData(analysisId: String, demandDataRequest: DemandDataRequest) {
        val uriVariables = mapOf(
            "analysisId" to analysisId
        )

        val headers = HttpHeaders().apply {
            set("Content-Type", "text/csv")
        }

        try {
            restTemplate.exchange<Void>("/v1/beam/analyses/{analysisId}/sink", HttpMethod.POST, HttpEntity(demandDataRequest.toCsv(), headers), uriVariables)
        } catch (e: Exception) {
            logger.error("Error calling predicthq/api, upload demand data", e)

            when (e) {
                is NotFoundException -> {
                    if (isAnalysisNotFound(e.response)) {
                        throw AnalysisNotFoundException("Analysis not found for analysisId: $analysisId")
                    }
                }
                is BadRequestException -> {
                    if (isKnownUploadDemandDataValidationError(e.response)) {
                        throw InvalidDemandDataUploadedException("Invalid demand data uploaded for analysisId: $analysisId", e)
                    }
                    throw e
                }
                else -> throw e
            }
        }
    }

    fun createAnalysis(createAnalysisRequest: CreateAnalysisRequest): CreateAnalysisResponse {
        try {
            val response = restTemplate.exchange("/v1/beam/analyses", HttpMethod.POST, HttpEntity(createAnalysisRequest, headers()), CreateAnalysisResponse::class.java)
            return response.body ?: throw PredictHqClientException("predicthq/api response body was null")
        } catch (e: Exception) {
            logger.error("Error calling predicthq/api, create analysis", e)
            throw e
        }
    }

    fun getAnalysis(analysisId: String): GetAnalysisResponse {
        val uriVariables = mapOf(
            "analysisId" to analysisId
        )
        try {
            val response = restTemplate.exchange<GetAnalysisResponse>("/v1/beam/analyses/{analysisId}", HttpMethod.GET, HttpEntity<String>(headers()), uriVariables)
            return response.body ?: throw PredictHqClientException("predicthq/api response body was null")
        } catch (e: Exception) {
            logger.error("Error calling predicthq/api, get analysis", e)

            if (e is NotFoundException && isAnalysisNotFound(e.response)) {
                throw AnalysisNotFoundException("Analysis not found for analysisId: $analysisId")
            }

            throw e
        }
    }

    fun deleteAnalysis(analysisId: String) {
        val uriVariables = mapOf(
            "analysisId" to analysisId
        )
        try {
            restTemplate.exchange<Void>("/v1/beam/analyses/{analysisId}", HttpMethod.DELETE, HttpEntity<String>(headers()), uriVariables)
        } catch (e: Exception) {
            logger.error("Error calling predicthq/api, delete analysis", e)

            if (e is NotFoundException && isAnalysisNotFound(e.response)) {
                throw AnalysisNotFoundException("Analysis not found for analysisId: $analysisId")
            }

            throw e
        }
    }

    private fun isAnalysisNotFound(content: String): Boolean {
        val errorResponse = objectMapper.readValueSilently<ErrorResponse>(content)
        return errorResponse?.error?.contains("Analysis not found", true) == true
    }

    private fun isKnownUploadDemandDataValidationError(content: String?): Boolean {
        val errorResponse = objectMapper.readValueSilently<ErrorResponse>(content)
        KNOWN_UPLOAD_DEMAND_DATA_VALIDATION_ERROR_MESSAGES.forEach {
            if (errorResponse?.error?.contains(it, true) == true) {
                return true
            }
        }

        logger.error("Unknown upload demand data validation error: $content")

        return false
    }

    private fun headers() = HttpHeaders().apply {
        contentType = MediaType.APPLICATION_JSON
    }

    private inline fun <reified T> ObjectMapper.readValueSilently(content: String?): T? {
        if (content == null)
            return null

        return try {
            this.readValue<T>(content)
        } catch (e: Exception) {
            logger.error("Error parsing json", e)
            null
        }
    }
}

@ConfigurationProperties("predicthq.api")
data class PredictHqClientProperties(
    val baseUrl: String,
    val accessToken: String,
    val concurrency: Int = 500,
    val timeoutInSeconds: Int = 60
)

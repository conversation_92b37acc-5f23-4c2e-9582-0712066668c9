package com.siteminder.predicthq

data class FeatureImportanceNotReadyException(override val message: String) : RuntimeException(message)

data class PredictHqClientException(override val message: String, val response: String? = null, override val cause: Exception? = null) : RuntimeException(message, cause)

data class AnalysisNotFoundException(override val message: String, override val cause: Exception? = null) : RuntimeException(message, cause)

data class NotFoundException(override val message: String, val response: String, override val cause: Exception? = null) : RuntimeException(message, cause)

data class AuthException(override val message: String, override val cause: Exception? = null) : RuntimeException(message, cause)

data class TooManyRequestException(override val message: String, override val cause: Exception? = null) : RuntimeException(message, cause)

data class BadRequestException(override val message: String, val response: String? = null, override val cause: Exception? = null) : RuntimeException(message, cause)

data class InvalidDemandDataUploadedException(override val message: String, override val cause: Exception? = null) : RuntimeException(message, cause)

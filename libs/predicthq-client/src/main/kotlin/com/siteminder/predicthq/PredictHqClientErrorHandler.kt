package com.siteminder.predicthq

import org.springframework.http.HttpStatus
import org.springframework.http.client.ClientHttpResponse
import org.springframework.util.StreamUtils
import org.springframework.web.client.ResponseErrorHandler
import java.nio.charset.StandardCharsets

class PredictHqClientErrorHandler : ResponseErrorHandler {

    override fun hasError(response: ClientHttpResponse): <PERSON><PERSON><PERSON> {
        return response.statusCode.isError
    }

    override fun handleError(response: ClientHttpResponse) {
        val errorResponse = StreamUtils.copyToString(response.body, StandardCharsets.UTF_8)

        when (response.statusCode) {
            HttpStatus.UNAUTHORIZED, HttpStatus.FORBIDDEN -> throw AuthException("Auth error")
            HttpStatus.TOO_MANY_REQUESTS -> throw TooManyRequestException("Too many requests")
            HttpStatus.NOT_FOUND -> throw NotFoundException("Not found", errorResponse)
            HttpStatus.BAD_REQUEST -> throw BadRequestException("Bad request", errorResponse)
            else -> throw PredictHqClientException("Failed request", errorResponse)
        }
    }
}

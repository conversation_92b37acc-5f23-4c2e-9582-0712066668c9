package com.siteminder.predicthq

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal
import java.time.LocalDate

data class CreateAnalysisRequest(
    val name: String,
    val location: Location,
    val rank: Rank = Rank(),
    @field:JsonProperty("demand_type")
    val demandType: DemandType = DemandType()
) {
    data class Location(
        @field:JsonProperty("geopoint")
        val geoPoint: GeoPoint,
        val radius: BigDecimal,
        val unit: String = "km"
    ) {
        data class GeoPoint(
            val lat: String,
            val lon: String
        )
    }

    data class Rank(
        val type: String = "phq"
    )

    data class DemandType(
        val industry: String = "accommodation"
    )
}

data class DemandDataRequest(
    val dateDemandDataList : List<DateDemandData>

) {
    data class DateDemandData(
        val date: LocalDate,
        val demand: Int
    )

    fun toCsv(): String {
        return "date,demand\n" + dateDemandDataList.joinToString("\n") { "${it.date},${it.demand}" }
    }

}

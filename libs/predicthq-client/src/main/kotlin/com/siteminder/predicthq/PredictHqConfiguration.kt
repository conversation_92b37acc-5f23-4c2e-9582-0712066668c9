package com.siteminder.predicthq

import com.fasterxml.jackson.databind.ObjectMapper
import com.siteminder.http.RestTemplateBuilderCustomiser
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders

@Configuration
@EnableConfigurationProperties(PredictHqClientProperties::class)
class PredictHqConfiguration {

    @Bean
    fun predictHqClient(
        properties: PredictHqClientProperties,
        restTemplateBuilderCustomiser: RestTemplateBuilderCustomiser,
        objectMapper: ObjectMapper
    ): PredictHqClient {

        val restTemplate = restTemplateBuilderCustomiser.custom()
            .connectTimeoutInSeconds(properties.timeoutInSeconds)
            .socketTimeoutInSeconds(properties.timeoutInSeconds)
            .maxConnections(properties.concurrency)
            .build()
            .rootUri(properties.baseUrl)
            .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer ${properties.accessToken}")
            .errorHandler(PredictHqClientErrorHandler())
            .build()

        return PredictHqClient(restTemplate, objectMapper)
    }
}

package com.siteminder.predicthq

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import java.math.BigDecimal

data class SuggestedRadiusResponse(
    val radius: BigDecimal,
    @field:JsonProperty("radius_unit")
    val radiusUnit: String,
    val location: Location
) {
    data class Location(
        val lat: String,
        val lon: String
    )
}

data class FeatureImportanceResponse(
    @field:JsonProperty("feature_importance")
    val featureImportance: List<FeatureImportance>
) {
    data class FeatureImportance(
        @field:JsonProperty("feature_group")
        val featureGroup: Category,
        val features: List<String>,
        @field:JsonProperty("p_value")
        val pvalue: Double,
        val important: Boolean
    )

    enum class Category(@get:JsonValue val value: String) {
        observances("observances"),
        public_holidays("public-holidays"),
        concerts("concerts"),
        sports("sports"),
        community("community"),
        expos("expos"),
        performing_arts("performing-arts"),
        conferences("conferences"),
        school_holidays("school-holidays"),
        festivals("festivals"),
        severe_weather("severe-weather"),
        academic("academic")
    }
}

data class CreateAnalysisResponse(
    @field:JsonProperty("analysis_id")
    val analysisId: String
)

data class GetAnalysisResponse(
    val name: String,
    @field:JsonProperty("readiness_status")
    val readinessStatus: ReadinessStatus? = null,
    @field:JsonProperty("readiness_checks")
    val readinessChecks: ReadinessChecks? = null,
    @field:JsonProperty("processing_completed")
    val processingCompleted: ProcessingCompleted
) {
    data class ProcessingCompleted(
        @field:JsonProperty("feature_importance")
        val feature_importance: Boolean
    )

    data class ReadinessChecks(
        @field:JsonProperty("error_code")
        val errorCode: String?
    )

    enum class ReadinessStatus {
        pending,
        failed,
        ready
    }
}

data class ErrorResponse(
    val error: String
)

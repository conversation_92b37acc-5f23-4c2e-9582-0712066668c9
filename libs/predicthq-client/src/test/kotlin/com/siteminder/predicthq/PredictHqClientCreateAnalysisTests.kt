package com.siteminder.predicthq

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory

@ExtendWith(MockServerExtension::class)
class PredictHqClientCreateAnalysisTests {

    private lateinit var client: PredictHqClient

    private lateinit var mockServerClient: MockServerClient

    val latitude = (-33.8670522).toBigDecimal()
    val longitude = 151.1957362.toBigDecimal()
    val radius = 3.3.toBigDecimal()
    val analysisId = "zpcR2iE3h0Q"
    val spid = "123445"

    @BeforeEach
    fun setUp() {

        val restTemplate = RestTemplateBuilder()
            .rootUri("http://${mockServerClient.remoteAddress().hostName}:${mockServerClient.remoteAddress().port}")
            .errorHandler(PredictHqClientErrorHandler())
            .build()

        client = PredictHqClient(restTemplate, jacksonObjectMapper())
    }

    @Test
    fun `create analysis should return valid response`() {

        val request = apiRequest(
            path = "/v1/beam/analyses", httpMethod = HttpMethod.POST,
            body = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $radius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = """
                        {
                          "analysis_id": "$analysisId"
                        }
                    """.trimIndent()
                )
            )

        val response = client.createAnalysis(createAnalysisRequest())

        response shouldBe CreateAnalysisResponse(analysisId)
    }

    @Test
    fun `create analysis should throw PredictHqClientException when response payload is null`() {

        val request = apiRequest(
            path = "/v1/beam/analyses", httpMethod = HttpMethod.POST,
            body = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $radius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = null,

                    )
            )

        shouldThrow<PredictHqClientException> {
            client.createAnalysis(createAnalysisRequest())
        }.also {
            it.message shouldBe "predicthq/api response body was null"
        }
    }

    @Test
    fun `create analysis should throw PredictHqClientException when api respond with non-successful http code`() {

        val request = apiRequest(
            path = "/v1/beam/analyses", httpMethod = HttpMethod.POST,
            body = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $radius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.BAD_GATEWAY)
            )

        shouldThrow<PredictHqClientException> {
            client.createAnalysis(createAnalysisRequest())
        }.also {
            it.message shouldBe "Failed request"
        }
    }

    @Test
    fun `create analysis should throw TooManyRequestException when api respond with 429`() {

        val request = apiRequest(
            path = "/v1/beam/analyses", httpMethod = HttpMethod.POST,
            body = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $radius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.TOO_MANY_REQUESTS)
            )

        shouldThrow<TooManyRequestException> {
            client.createAnalysis(createAnalysisRequest())
        }.also {
            it.message shouldBe "Too many requests"
        }
    }

    @Test
    fun `create analysis should throw AuthException when api respond with auth related http status code`() {

        val request = apiRequest(
            path = "/v1/beam/analyses", httpMethod = HttpMethod.POST,
            body = """
            {
              "name": "$spid",
              "location": {
                "geopoint": {
                  "lat": "$latitude",
                  "lon": "$longitude"
                },
                "radius": $radius,
                "unit": "km"
              },
              "rank": {
                "type": "phq"
              },
              "demand_type": {
                "industry": "accommodation"
              }
            }
        """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.FORBIDDEN)
            )

        shouldThrow<AuthException> {
            client.createAnalysis(createAnalysisRequest())
        }.also {
            it.message shouldBe "Auth error"
        }
    }

    private fun createAnalysisRequest() = CreateAnalysisRequest(
        name = spid,
        location = CreateAnalysisRequest.Location(
            geoPoint = CreateAnalysisRequest.Location.GeoPoint(
                lat = latitude.toString(),
                lon = longitude.toString()
            ),
            radius = radius,
        )
    )
}

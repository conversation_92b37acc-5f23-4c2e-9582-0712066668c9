package com.siteminder.predicthq

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpStatus
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory

@ExtendWith(MockServerExtension::class)
class PredictHqClientGetFeatureImportanceTests {

    private lateinit var client: PredictHqClient

    private lateinit var mockServerClient: MockServerClient

    val analysisId = "xxx"

    @BeforeEach
    fun setUp() {

        val restTemplate = RestTemplateBuilder()
            .rootUri("http://${mockServerClient.remoteAddress().hostName}:${mockServerClient.remoteAddress().port}")
            .errorHandler(PredictHqClientErrorHandler())
            .build()

        client = PredictHqClient(restTemplate, jacksonObjectMapper())
    }

    @Test
    fun `get feature importance should return valid response`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = """
                    {
                      "feature_importance": [
                        {
                          "feature_group": "public-holidays",
                          "features": [
                            "phq_rank_public_holidays"
                          ],
                          "p_value": 0.01,
                          "important": true
                        },
                        {
                          "feature_group": "festivals",
                          "features": [
                            "phq_attendance_festivals"
                          ],
                          "p_value": 0.2764,
                          "important": false
                        },
                        {
                          "feature_group": "academic",
                          "features": [
                            "phq_rank_academic_holiday"
                          ],
                          "p_value": 0.9999,
                          "important": false
                        }
                      ]
                    }
                    """.trimIndent()
                )
            )

        val featureImportance = client.getFeatureImportance(analysisId)

        featureImportance shouldBe FeatureImportanceResponse(
            listOf(
                FeatureImportanceResponse.FeatureImportance(
                    featureGroup = FeatureImportanceResponse.Category.public_holidays,
                    features = listOf("phq_rank_public_holidays"),
                    pvalue = 0.01,
                    important = true
                ),
                FeatureImportanceResponse.FeatureImportance(
                    featureGroup = FeatureImportanceResponse.Category.festivals,
                    features = listOf("phq_attendance_festivals"),
                    pvalue = 0.2764,
                    important = false
                ),
                FeatureImportanceResponse.FeatureImportance(
                    featureGroup = FeatureImportanceResponse.Category.academic,
                    features = listOf("phq_rank_academic_holiday"),
                    pvalue = 0.9999,
                    important = false
                )
            )
        )
    }

    @Test
    fun `get feature importance should throw AnalysisNotFoundException when analysis not found`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.NOT_FOUND,
                    body = """
                    {
                      "error": "Analysis not found"
                    }
                """.trimIndent()
                )
            )

        shouldThrow<AnalysisNotFoundException> { client.getFeatureImportance(analysisId) }
    }

    @Test
    fun `get feature importance should throw FeatureImportanceNotReadyException when analysis is not ready`() {
        val request = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.BAD_REQUEST,
                    body = """
                        {
                          "error": "Feature Importance results are not ready"
                        }
                    """.trimIndent()
                )
            )

        shouldThrow<FeatureImportanceNotReadyException> {
            client.getFeatureImportance(analysisId)
        }.also {
            it.message shouldBe "Feature Importance results are not ready for analysisId: $analysisId"
        }
    }

    @Test
    fun `get feature importance should throw PredictHqClientException when response payload is null`() {
        val request = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = null,
                    )
            )

        shouldThrow<PredictHqClientException> {
            client.getFeatureImportance(analysisId)
        }.also {
            it.message shouldBe "predicthq/api response body was null"
        }
    }

    @Test
    fun `get feature importance should throw TooManyRequestException when api respond with 429`() {
        val request = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.TOO_MANY_REQUESTS)
            )

        shouldThrow<TooManyRequestException> {
            client.getFeatureImportance(analysisId)
        }.also {
            it.message shouldBe "Too many requests"
        }
    }

    @Test
    fun `get feature importance should throw AuthException when api respond with auth related http status code`() {
        val request = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.FORBIDDEN)
            )

        shouldThrow<AuthException> {
            client.getFeatureImportance(analysisId)
        }.also {
            it.message shouldBe "Auth error"
        }
    }

    @Test
    fun `get feature importance should throw PredictHqClientException when api respond with non-successful http code`() {
        val request = apiRequest(path = "/v1/beam/analyses/$analysisId/feature-importance")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.INTERNAL_SERVER_ERROR)
            )

        shouldThrow<PredictHqClientException> {
            client.getFeatureImportance(analysisId)
        }.also {
            it.message shouldBe "Failed request"
        }
    }
}

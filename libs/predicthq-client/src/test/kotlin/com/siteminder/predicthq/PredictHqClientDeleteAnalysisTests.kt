package com.siteminder.predicthq

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldNotThrowAny
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory

@ExtendWith(MockServerExtension::class)
class PredictHqClientDeleteAnalysisTests {

    private lateinit var client: PredictHqClient

    private lateinit var mockServerClient: MockServerClient

    val analysisId = "zpcR2iE3h0Q"

    @BeforeEach
    fun setUp() {

        val restTemplate = RestTemplateBuilder()
            .rootUri("http://${mockServerClient.remoteAddress().hostName}:${mockServerClient.remoteAddress().port}")
            .errorHandler(PredictHqClientErrorHandler())
            .build()

        client = PredictHqClient(restTemplate, jacksonObjectMapper())
    }

    @Test
    fun `delete analysis should return successful response`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE)

        mockServerClient.`when`(request)
            .respond(apiResponse())

        shouldNotThrowAny { client.deleteAnalysis(analysisId) }
    }

    @Test
    fun `delete analysis should throw AnalysisNotFoundException when analysis not found`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE)

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.NOT_FOUND,
                    body = """
                    {
                      "error": "Analysis not found"
                    }
                """.trimIndent()
                )
            )

        shouldThrow<AnalysisNotFoundException> { client.deleteAnalysis(analysisId) }.also {
            it.message shouldBe "Analysis not found for analysisId: $analysisId"
        }
    }

    @Test
    fun `delete analysis should throw TooManyRequestException when api respond with 429`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE)

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.TOO_MANY_REQUESTS
                )
            )

        shouldThrow<TooManyRequestException> { client.deleteAnalysis(analysisId) }.also {
            it.message shouldBe "Too many requests"
        }
    }


    @Test
    fun `delete analysis should throw AuthException when api respond with auth related http status code`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE)

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.UNAUTHORIZED
                )
            )

        shouldThrow<AuthException> { client.deleteAnalysis(analysisId) }.also {
            it.message shouldBe "Auth error"
        }
    }

    @Test
    fun `delete analysis should throw PredictHqClientException when api respond with non-successful http code`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId", httpMethod = HttpMethod.DELETE)

        mockServerClient.`when`(request)
            .respond(apiResponse(status = HttpStatus.INTERNAL_SERVER_ERROR))

        shouldThrow<PredictHqClientException> {
            client.deleteAnalysis(analysisId)
        }.also {
            it.message shouldBe "Failed request"
        }
    }
}

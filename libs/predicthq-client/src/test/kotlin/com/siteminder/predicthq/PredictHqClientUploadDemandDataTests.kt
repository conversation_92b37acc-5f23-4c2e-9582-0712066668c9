package com.siteminder.predicthq

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.OutputStreamAppender
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.apiResponse
import com.siteminder.csvApiRequest
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldNotThrowAny
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.ArgumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockserver.client.MockServerClient
import org.slf4j.LoggerFactory
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import java.time.LocalDate

@ExtendWith(MockServerExtension::class)
class PredictHqClientUploadDemandDataTests {

    private lateinit var client: PredictHqClient

    private lateinit var mockServerClient: MockServerClient
    private lateinit var mockedAppender: OutputStreamAppender<ILoggingEvent>

    val analysisId = "zpcR2iE3h0Q"

    @BeforeEach
    fun setUp() {

        val restTemplate = RestTemplateBuilder()
            .rootUri("http://${mockServerClient.remoteAddress().hostName}:${mockServerClient.remoteAddress().port}")
            .errorHandler(PredictHqClientErrorHandler())
            .build()

        client = PredictHqClient(restTemplate, jacksonObjectMapper())

        mockedAppender = mock()
        val logger = LoggerFactory.getLogger(PredictHqClient::class.java) as Logger
        logger.level = Level.INFO
        logger.addAppender(mockedAppender)
    }

    @Test
    fun `upload demand data should return successful response`() {

        val today = LocalDate.now()

        val request = csvApiRequest(
            path = "/v1/beam/analyses/$analysisId/sink", httpMethod = HttpMethod.POST,
            body = """
                date,demand
                ${today},100
                """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(apiResponse())

        shouldNotThrowAny {
            client.uploadDemandData(
                analysisId,
                DemandDataRequest(
                    listOf(
                        DemandDataRequest.DateDemandData(today, 100)
                    )
                )
            )
        }
    }

    @Test
    fun `upload demand data should throw AnalysisNotFoundException when analysis not found`() {

        val today = LocalDate.now()

        val request = csvApiRequest(
            path = "/v1/beam/analyses/$analysisId/sink", httpMethod = HttpMethod.POST,
            body = """
                date,demand
                ${today},100
                """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.NOT_FOUND,
                    body = """
                    {
                      "error": "Analysis not found"
                    }
                """.trimIndent()
                )
            )

        shouldThrow<AnalysisNotFoundException> {
            client.uploadDemandData(
                analysisId,
                DemandDataRequest(
                    listOf(
                        DemandDataRequest.DateDemandData(today, 100)
                    )
                )
            )
        }
    }

    @Test
    fun `upload demand data should throw TooManyRequestException when api respond with 429`() {

        val today = LocalDate.now()

        val request = csvApiRequest(
            path = "/v1/beam/analyses/$analysisId/sink", httpMethod = HttpMethod.POST,
            body = """
                date,demand
                ${today},100
                """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(apiResponse(status = HttpStatus.TOO_MANY_REQUESTS))

        shouldThrow<TooManyRequestException> {
            client.uploadDemandData(
                analysisId,
                DemandDataRequest(
                    listOf(
                        DemandDataRequest.DateDemandData(today, 100)
                    )
                )
            )
        }.also {
            it.message shouldBe "Too many requests"
        }
    }

    @Test
    fun `upload demand data should throw AuthException when api respond with auth related http status code`() {

        val today = LocalDate.now()

        val request = csvApiRequest(
            path = "/v1/beam/analyses/$analysisId/sink", httpMethod = HttpMethod.POST,
            body = """
                date,demand
                ${today},100
                """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(apiResponse(status = HttpStatus.FORBIDDEN))

        shouldThrow<AuthException> {
            client.uploadDemandData(
                analysisId,
                DemandDataRequest(
                    listOf(
                        DemandDataRequest.DateDemandData(today, 100)
                    )
                )
            )
        }.also {
            it.message shouldBe "Auth error"
        }
    }

    @ParameterizedTest
    @ValueSource(
        strings = [
            "Invalid data uploaded. There are 25 consecutive demand days missing in the demand column. Please ensure the total missing consecutive demand days is 7 or less",
            "Invalid data uploaded. The values in 'demand' column are constant. Please make sure that all of them are real demands",
            "Invalid data uploaded. The dataset contains more than 20% missing demand values"
        ]
    )
    fun `upload demand data should throw InvalidDemandDataUploadedException when api responds 400 with known validation error message`(error: String) {

        val today = LocalDate.now()

        val request = csvApiRequest(
            path = "/v1/beam/analyses/$analysisId/sink", httpMethod = HttpMethod.POST,
            body = """
                date,demand
                ${today},100
                """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.BAD_REQUEST, body = """
                {
                  "error": "$error"
                }
            """.trimIndent()
                )
            )

        shouldThrow<InvalidDemandDataUploadedException> {
            client.uploadDemandData(
                analysisId,
                DemandDataRequest(
                    listOf(
                        DemandDataRequest.DateDemandData(today, 100)
                    )
                )
            )
        }.also {
            it.message shouldBe "Invalid demand data uploaded for analysisId: $analysisId"
        }
    }

    @Test
    fun `upload demand data should throw BadRequestException when api responds 400 with unknown validation error`() {

        val today = LocalDate.now()

        val request = csvApiRequest(
            path = "/v1/beam/analyses/$analysisId/sink", httpMethod = HttpMethod.POST,
            body = """
                date,demand
                ${today},100
                """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.BAD_REQUEST, body = """
                {
                  "error": "Unknown validation error"
                }
            """.trimIndent()
                )
            )

        shouldThrow<BadRequestException> {
            client.uploadDemandData(
                analysisId,
                DemandDataRequest(
                    listOf(
                        DemandDataRequest.DateDemandData(today, 100)
                    )
                )
            )
        }.also {
            it.message shouldBe "Bad request"
        }

        val argumentCaptor = ArgumentCaptor.forClass(ILoggingEvent::class.java)
        verify(mockedAppender, times(2)).doAppend(argumentCaptor.capture())
        argumentCaptor.allValues[1].message shouldBe """
            Unknown upload demand data validation error: {
              "error": "Unknown validation error"
            }
        """.trimIndent()
    }

    @Test
    fun `upload demand data should throw PredictHqClientException when api respond with non-successful http code`() {

        val today = LocalDate.now()

        val request = csvApiRequest(
            path = "/v1/beam/analyses/$analysisId/sink", httpMethod = HttpMethod.POST,
            body = """
                date,demand
                ${today},100
                """.trimIndent()
        )

        mockServerClient.`when`(request)
            .respond(apiResponse(status = HttpStatus.GATEWAY_TIMEOUT))

        shouldThrow<PredictHqClientException> {
            client.uploadDemandData(
                analysisId,
                DemandDataRequest(
                    listOf(
                        DemandDataRequest.DateDemandData(today, 100)
                    )
                )
            )
        }.also {
            it.message shouldBe "Failed request"
        }
    }
}

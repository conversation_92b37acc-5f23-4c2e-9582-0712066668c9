package com.siteminder.predicthq

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpStatus
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory

@ExtendWith(MockServerExtension::class)
class PredictHqClientGetAnalysisTests {

    private lateinit var client: PredictHqClient

    private lateinit var mockServerClient: MockServerClient

    val analysisId = "zpcR2iE3h0Q"
    val spid = "123445"

    @BeforeEach
    fun setUp() {

        val restTemplate = RestTemplateBuilder()
            .rootUri("http://${mockServerClient.remoteAddress().hostName}:${mockServerClient.remoteAddress().port}")
            .errorHandler(PredictHqClientErrorHandler())
            .build()

        client = PredictHqClient(restTemplate, jacksonObjectMapper())
    }

    @Test
    fun `get analysis should return valid response`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = """
                        {
                          "name": "$spid",
                          "location": {
                            "geopoint": {
                              "lat": "-35.849761",
                              "lon": "174.7628903"
                            },
                            "radius": 1.2,
                            "unit": "km"
                          },
                          "rank": {
                            "type": "phq",
                            "levels": {
                              "phq": {
                                "min": 51
                              }
                            }
                          },
                          "status": "draft",
                          "readiness_status": "ready",
                          "readiness_checks": {
                            "error_code": "NO_ERROR"
                          },
                          "create_dt": "2024-04-05T04:15:29.118163+00:00",
                          "update_dt": "2024-04-05T04:15:29.118163+00:00",
                          "user_id": null,
                          "access_type": "limited",
                          "processing_completed": {
                            "correlation": false,
                            "feature_importance": true
                          },
                          "tz": "UTC"
                        }
                    """.trimIndent()
                )
            )

        val response = client.getAnalysis(analysisId)

        response shouldBe GetAnalysisResponse(
            name = spid,
            readinessStatus = GetAnalysisResponse.ReadinessStatus.ready,
            readinessChecks = GetAnalysisResponse.ReadinessChecks(
                errorCode = "NO_ERROR"
            ),
            processingCompleted = GetAnalysisResponse.ProcessingCompleted(
                feature_importance = true
            )
        )
    }

    @Test
    fun `get analysis should throw AnalysisNotFoundException when analysis not found`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    status = HttpStatus.NOT_FOUND,
                    body = """
                    {
                      "error": "Analysis not found"
                    }
                """.trimIndent()
                )
            )

        shouldThrow<AnalysisNotFoundException> { client.getAnalysis(analysisId) }
    }

    @Test
    fun `get analysis should throw PredictHqClientException when response payload is null`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = null,
                    )
            )

        shouldThrow<PredictHqClientException> {
            client.getAnalysis(analysisId)
        }.also {
            it.message shouldBe "predicthq/api response body was null"
        }
    }

    @Test
    fun `get analysis should throw TooManyRequestException when api respond with 429`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.TOO_MANY_REQUESTS)
            )

        shouldThrow<TooManyRequestException> {
            client.getAnalysis(analysisId)
        }.also {
            it.message shouldBe "Too many requests"
        }
    }

    @Test
    fun `get analysis should throw AuthException when api respond with auth related http status code`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.UNAUTHORIZED)
            )

        shouldThrow<AuthException> {
            client.getAnalysis(analysisId)
        }.also {
            it.message shouldBe "Auth error"
        }
    }

    @Test
    fun `get analysis should throw PredictHqClientException when api respond with non-successful http code`() {

        val request = apiRequest(path = "/v1/beam/analyses/$analysisId")

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.GATEWAY_TIMEOUT)
            )

        shouldThrow<PredictHqClientException> {
            client.getAnalysis(analysisId)
        }.also {
            it.message shouldBe "Failed request"
        }
    }
}

package com.siteminder.predicthq

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.siteminder.apiRequest
import com.siteminder.apiResponse
import com.siteminder.mockserver.MockServerExtension
import io.kotlintest.shouldBe
import io.kotlintest.shouldThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockserver.client.MockServerClient
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpStatus
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory

@ExtendWith(MockServerExtension::class)
class PredictHqClientGetSuggestedRadiusTests {

    private lateinit var client: PredictHqClient

    private lateinit var mockServerClient: MockServerClient

    val latitude = 234.098.toBigDecimal()
    val longitude = (-73.456).toBigDecimal()
    val location = "$latitude,$longitude"

    @BeforeEach
    fun setUp() {

        val restTemplate = RestTemplateBuilder()
            .rootUri("http://${mockServerClient.remoteAddress().hostName}:${mockServerClient.remoteAddress().port}")
            .errorHandler(PredictHqClientErrorHandler())
            .build()

        client = PredictHqClient(restTemplate, jacksonObjectMapper())
    }

    @Test
    fun `get suggested radius should return radius`() {

        val request = apiRequest(path = "/suggested-radius", queryParams = mapOf("location.origin" to location, "radius_unit" to "km", "industry" to "accommodation"))

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = """
                    {
                      "radius": 3.3,
                      "radius_unit": "km",
                      "location": {
                        "lat": "$latitude",
                        "lon": "$longitude"
                      }
                    }
                    """.trimIndent()
                )
            )

        val radius = client.getSuggestedRadius(latitude, longitude)

        radius shouldBe 3.3.toBigDecimal()
    }

    @Test
    fun `get suggested radius should throw PredictHqClientException when response payload is null`() {

        val request = apiRequest(path = "/suggested-radius", queryParams = mapOf("location.origin" to location, "radius_unit" to "km", "industry" to "accommodation"))

        mockServerClient.`when`(request)
            .respond(
                apiResponse(
                    body = null
                )
            )

        shouldThrow<PredictHqClientException> {
            client.getSuggestedRadius(latitude, longitude)
        }.also {
            it.message shouldBe "predicthq/api response body was null"
        }
    }

    @Test
    fun `get suggested radius should throw TooManyRequestException when api respond with 429`() {

        val request = apiRequest(path = "/suggested-radius", queryParams = mapOf("location.origin" to location, "radius_unit" to "km", "industry" to "accommodation"))

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.TOO_MANY_REQUESTS)
            )

        shouldThrow<TooManyRequestException> {
            client.getSuggestedRadius(latitude, longitude)
        }.also {
            it.message shouldBe "Too many requests"
        }
    }

    @Test
    fun `get suggested radius should throw AuthException when api respond with auth related http status code`() {

        val request = apiRequest(path = "/suggested-radius", queryParams = mapOf("location.origin" to location, "radius_unit" to "km", "industry" to "accommodation"))

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.FORBIDDEN)
            )

        shouldThrow<AuthException> {
            client.getSuggestedRadius(latitude, longitude)
        }.also {
            it.message shouldBe "Auth error"
        }
    }

    @Test
    fun `get suggested radius should throw PredictHqClientException when api respond with non-successful http code`() {

        val request = apiRequest(path = "/suggested-radius", queryParams = mapOf("location.origin" to location, "radius_unit" to "km", "industry" to "accommodation"))

        mockServerClient.`when`(request)
            .respond(
                apiResponse(status = HttpStatus.INTERNAL_SERVER_ERROR)
            )

        shouldThrow<PredictHqClientException> {
            client.getSuggestedRadius(latitude, longitude)
        }.also {
            it.message shouldBe "Failed request"
        }
    }
}

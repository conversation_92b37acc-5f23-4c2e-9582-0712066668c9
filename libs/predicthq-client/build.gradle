apply plugin: 'springboot-kotlin-configuration-plugin' // https://github.com/siteminder-au/sm-gradle-plugins/tree/master/cm-build-plugin

dependencies {
    api "org.springframework.boot:spring-boot-starter-web:${springBootVersion}"

    implementation "com.siteminder:sm-spring-boot-http-client-starter:${smLibraryVersion}"

    testImplementation "com.siteminder:sm-spring-boot-starter-test:${smLibraryVersion}"
}

bootJar {
    enabled = false
}

jar {
    enabled = true
}

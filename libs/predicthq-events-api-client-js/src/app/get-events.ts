/* eslint-disable */
import { AxiosInstance, AxiosResponse } from 'axios'
import { Exactly } from './utils'

export type OperationWithAxiosResponse = <T extends Exactly<Request, T>>(request: T) => Promise<AxiosResponse<Response>>
export const operationWithAxiosResponse = <T extends Exactly<Request, T>>(axios: AxiosInstance, request: T): Promise<AxiosResponse<Response>> => {
  return axios.request({
    url: `/api/events`,
    // @ts-ignore for sm-axios
    urlTemplate: '/api/events',
    method: 'GET',
    headers: request.headers,
    params: request.query,
  })
}

/**
 * Requires events:read permission
 */
export type Operation = <T extends Exactly<Request, T>>(request: T) => Promise<Response>
export const operation = async <T extends Exactly<Request, T>>(axios: AxiosInstance, request: T): Promise<Response> => {
  return (await operationWithAxiosResponse(axios, request)).data
}

export interface Request {
  headers: {
    'X-SM-TRACE-TOKEN': string
  }
  query?: {
    spid?: string
    latitude?: number
    longitude?: number
    radius?: number
    /**
     * Start of range for Event dates (inclusive)
     */
    start?: string
    /**
     * End of range for Event dates (inclusive)
     */
    end?: string
    /**
     * Start of range for createdDate (inclusive)
     */
    createdStart?: string
    /**
     * End of range for createdDate (inclusive)
     */
    createdEnd?: string
    countryCode?: string[]
    category?: (
      | 'observances'
      | 'public-holidays'
      | 'concerts'
      | 'sports'
      | 'community'
      | 'expos'
      | 'performing-arts'
      | 'conferences'
      | 'school-holidays'
      | 'festivals'
    )[]
    excludeCategory?: (
      | 'observances'
      | 'public-holidays'
      | 'concerts'
      | 'sports'
      | 'community'
      | 'expos'
      | 'performing-arts'
      | 'conferences'
      | 'school-holidays'
      | 'festivals'
    )[]
    status?: ('active' | 'postponed' | 'cancelled' | 'predicted' | 'archived')[]
    excludeStatus?: ('active' | 'postponed' | 'cancelled' | 'predicted' | 'archived')[]
    page?: number
    pageSize?: number
  }
}

export interface Response {
  items: {
    eventId: string
    createdDate: string
    updatedDate: string
    title: string
    category:
      | 'observances'
      | 'public-holidays'
      | 'concerts'
      | 'sports'
      | 'community'
      | 'expos'
      | 'performing-arts'
      | 'conferences'
      | 'school-holidays'
      | 'festivals'
    eventStart: string
    eventEnd: string
    predictedEnd?: string | null
    timezone?: string | null
    entities: {
      entityId: string
      category?:
        | 'observances'
        | 'public-holidays'
        | 'concerts'
        | 'sports'
        | 'community'
        | 'expos'
        | 'performing-arts'
        | 'conferences'
        | 'school-holidays'
        | 'festivals'
      description?: string | null
      labels?: string[] | null
      formattedAddress?: string | null
      name: string
      timezone?: string | null
      recurring?: {
        ical: string
        [k: string]: any
      } | null
      type: 'event-group' | 'venue'
      [k: string]: any
    }[]
    scope?: 'locality' | 'localadmin' | 'county' | 'region' | 'country'
    countryCode?: string | null
    phqAttendance?: number | null
    phqRank: number
    localRank?: number | null
    aviationRank?: number | null
    status: 'active' | 'postponed' | 'cancelled' | 'predicted' | 'archived'
    brandSafe: boolean
    cancelledDate?: string | null
    postponedDate?: string | null
    /**
     * Predicted event spend for Accommodation in USD
     */
    predictedEventSpendAccommodation?: number | null
    /**
     * Predicted event spend for Hospitality in USD
     */
    predictedEventSpendHospitality?: number | null
    /**
     * Predicted event spend for Transportation in USD
     */
    predictedEventSpendTransportation?: number | null
    lon: number
    lat: number
    impactPatterns: {
      impactType: 'phq_rank' | 'phq_attendance'
      impacts: {
        dateLocal: string
        position: 'leading' | 'event_day' | 'lagging'
        value: number
        [k: string]: any
      }[]
      vertical: 'retail' | 'accommodation' | 'hospitality'
      [k: string]: any
    }[]
    [k: string]: any
  }[]
  page: number
  pages: number
  total: number
  [k: string]: any
}


/* eslint-disable */
import { AxiosInstance } from 'axios'

import * as GetEvents from './get-events'
import * as GetEvent from './get-event'


type GetEventsRequest = GetEvents.Request
type GetEventsResponse = GetEvents.Response
type GetEventRequest = GetEvent.Request
type GetEventResponse = GetEvent.Response

const operations = {
  getEvents: GetEvents.operation,
  getEvent: GetEvent.operation,
}

const operationsWithAxiosResponse = {
  getEvents: GetEvents.operationWithAxiosResponse,
  getEvent: GetEvent.operationWithAxiosResponse,
}

export interface ApiClient {
  getEvents: GetEvents.Operation,
  getEvent: GetEvent.Operation,
}

export interface ApiClientWithAxiosResponse {
  getEvents: GetEvents.OperationWithAxiosResponse,
  getEvent: GetEvent.OperationWithAxiosResponse,
}

export const createClient = (axios: AxiosInstance): ApiClient => {

  const apiClient = {}
  for (let [name, operation] of Object.entries(operations)) {
    // @ts-ignore
    apiClient[name] = (...args: any[]) => operation(axios, ...args)
  }

  return apiClient as ApiClient

}

export const createClientWithAxiosResponse = (axios: AxiosInstance): ApiClientWithAxiosResponse => {

  const apiClient = {}
  for (let [name, operation] of Object.entries(operationsWithAxiosResponse)) {
    // @ts-ignore
    apiClient[name] = (...args: any[]) => operation(axios, ...args)
  }

  return apiClient as ApiClientWithAxiosResponse

}

export {
  GetEventsRequest, GetEventsResponse,
  GetEventRequest, GetEventResponse,
}

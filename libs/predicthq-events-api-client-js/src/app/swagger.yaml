openapi: 3.0.1
info:
  title: predicthq/events-api
  description: See README in https://github.com/siteminder-au/predicthq for details
  version: v1
servers:
  - url: http://localhost:8080
    description: Generated server url
security:
  - bearerAuth: []
paths:
  /api/events:
    get:
      tags:
        - events-controller
      summary: Requires events:read permission
      operationId: getEvents
      parameters:
        - name: spid
          in: query
          required: false
          schema:
            type: string
        - name: latitude
          in: query
          required: false
          schema:
            maximum: 90
            minimum: -90
            type: number
            format: double
        - name: longitude
          in: query
          required: false
          schema:
            maximum: 180
            minimum: -180
            type: number
            format: double
        - name: radius
          in: query
          required: false
          schema:
            minimum: 1
            type: number
            format: double
        - name: start
          in: query
          description: Start of range for Event dates (inclusive)
          required: false
          schema:
            type: string
            description: Start of range for Event dates (inclusive)
            format: date-time
        - name: end
          in: query
          description: End of range for Event dates (inclusive)
          required: false
          schema:
            type: string
            description: End of range for Event dates (inclusive)
            format: date-time
        - name: createdStart
          in: query
          description: Start of range for createdDate (inclusive)
          required: false
          schema:
            type: string
            description: Start of range for createdDate (inclusive)
            format: date-time
        - name: createdEnd
          in: query
          description: End of range for createdDate (inclusive)
          required: false
          schema:
            type: string
            description: End of range for createdDate (inclusive)
            format: date-time
        - name: countryCode
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
        - name: category
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
              enum:
                - observances
                - public-holidays
                - concerts
                - sports
                - community
                - expos
                - performing-arts
                - conferences
                - school-holidays
                - festivals
        - name: excludeCategory
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
              enum:
                - observances
                - public-holidays
                - concerts
                - sports
                - community
                - expos
                - performing-arts
                - conferences
                - school-holidays
                - festivals
        - name: status
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
              enum:
                - active
                - postponed
                - cancelled
                - predicted
                - archived
        - name: excludeStatus
          in: query
          required: false
          schema:
            type: array
            items:
              type: string
              enum:
                - active
                - postponed
                - cancelled
                - predicted
                - archived
        - name: page
          in: query
          required: false
          schema:
            minimum: 1
            type: integer
            format: int32
        - name: pageSize
          in: query
          required: false
          schema:
            maximum: 500
            minimum: 1
            type: integer
            format: int32
        - name: X-SM-TRACE-TOKEN
          in: header
          description: Trace Token
          required: true
          schema:
            type: string
            example: 123e4567-e89b-12d3-a456-426655440000
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PagedResponseEventResponse"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            "*/*":
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /api/events/{eventId}:
    get:
      tags:
        - events-controller
      summary: Requires events:read permission
      operationId: getEvent
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
        - name: X-SM-TRACE-TOKEN
          in: header
          description: Trace Token
          required: true
          schema:
            type: string
            example: 123e4567-e89b-12d3-a456-426655440000
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EventResponse"
        "400":
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Not Found
          content:
            "*/*":
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
components:
  schemas:
    Entity:
      required:
        - entityId
        - name
        - type
      type: object
      properties:
        entityId:
          type: string
        category:
          type: string
          nullable: true
          enum:
            - observances
            - public-holidays
            - concerts
            - sports
            - community
            - expos
            - performing-arts
            - conferences
            - school-holidays
            - festivals
        description:
          type: string
          nullable: true
        labels:
          type: array
          nullable: true
          items:
            type: string
            nullable: true
        formattedAddress:
          type: string
          nullable: true
        name:
          type: string
        timezone:
          type: string
          nullable: true
        recurring:
          $ref: "#/components/schemas/Recurring"
        type:
          type: string
          enum:
            - event-group
            - venue
    Error:
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: string
          enum:
            - ERROR
            - UNAUTHORIZED
            - FORBIDDEN
            - INVALID
            - NOT_FOUND
            - NOT_NULL
            - NOT_BLANK
            - NOT_EMPTY
            - EMAIL
            - MIN
            - MAX
            - UNIQUE
        message:
          type: string
          description: Brief description of error
        meta:
          type: object
          additionalProperties:
            type: object
            description: Error context
            nullable: true
          description: Error context
          nullable: true
    ErrorResponse:
      required:
        - errors
      type: object
      properties:
        errors:
          type: array
          items:
            $ref: "#/components/schemas/Error"
    EventResponse:
      required:
        - brandSafe
        - category
        - createdDate
        - entities
        - eventEnd
        - eventId
        - eventStart
        - impactPatterns
        - lat
        - lon
        - phqRank
        - status
        - title
        - updatedDate
      type: object
      properties:
        eventId:
          type: string
        createdDate:
          type: string
          format: date-time
        updatedDate:
          type: string
          format: date-time
        title:
          type: string
        category:
          type: string
          enum:
            - observances
            - public-holidays
            - concerts
            - sports
            - community
            - expos
            - performing-arts
            - conferences
            - school-holidays
            - festivals
        eventStart:
          type: string
          format: date-time
        eventEnd:
          type: string
          format: date-time
        predictedEnd:
          type: string
          format: date-time
          nullable: true
        timezone:
          type: string
          nullable: true
        entities:
          type: array
          items:
            $ref: "#/components/schemas/Entity"
        scope:
          type: string
          nullable: true
          enum:
            - locality
            - localadmin
            - county
            - region
            - country
        countryCode:
          pattern: ISO 3166-1 alpha-2
          type: string
          nullable: true
        phqAttendance:
          type: integer
          format: int64
          nullable: true
        phqRank:
          type: integer
          format: int64
        localRank:
          type: integer
          format: int64
          nullable: true
        aviationRank:
          type: integer
          format: int64
          nullable: true
        status:
          type: string
          enum:
            - active
            - postponed
            - cancelled
            - predicted
            - archived
        brandSafe:
          type: boolean
        cancelledDate:
          type: string
          format: date-time
          nullable: true
        postponedDate:
          type: string
          format: date-time
          nullable: true
        predictedEventSpendAccommodation:
          type: integer
          description: Predicted event spend for Accommodation in USD
          format: int64
          nullable: true
        predictedEventSpendHospitality:
          type: integer
          description: Predicted event spend for Hospitality in USD
          format: int64
          nullable: true
        predictedEventSpendTransportation:
          type: integer
          description: Predicted event spend for Transportation in USD
          format: int64
          nullable: true
        lon:
          type: number
        lat:
          type: number
        impactPatterns:
          type: array
          items:
            $ref: "#/components/schemas/ImpactPattern"
    Impact:
      required:
        - dateLocal
        - position
        - value
      type: object
      properties:
        dateLocal:
          type: string
          format: date
        position:
          type: string
          enum:
            - leading
            - event_day
            - lagging
        value:
          type: integer
          format: int32
    ImpactPattern:
      required:
        - impactType
        - impacts
        - vertical
      type: object
      properties:
        impactType:
          type: string
          enum:
            - phq_rank
            - phq_attendance
        impacts:
          type: array
          items:
            $ref: "#/components/schemas/Impact"
        vertical:
          type: string
          enum:
            - retail
            - accommodation
            - hospitality
    PagedResponseEventResponse:
      required:
        - items
        - page
        - pages
        - total
      type: object
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/EventResponse"
        page:
          type: integer
          format: int32
        pages:
          type: integer
          format: int32
        total:
          type: integer
          format: int64
    Recurring:
      required:
        - ical
      type: object
      properties:
        ical:
          type: string
      nullable: true
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

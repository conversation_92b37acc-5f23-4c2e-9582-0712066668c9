# @siteminder/predicthq-events-api-client

**This is generated code. Do not edit**

## Supported versions

- NodeJS 18.16.0 & above.
- npm 9.5.1 & above

## Example Usage

```javascript
import { createClient } from '@siteminder/predicthq-events-api-client'
import { createSmAxios } from '@siteminder/sm-axios'
import S3JwtCache from '@siteminder/s3-jwt-cache'
import { config, metrics, rootLogger, s3 } from '../shared' // example path

const jwtCache = new S3JwtCache(config('SECRETS_BUCKET'), config('API_TOKEN_KEY'), { s3, logger, metrics }) // example config

const axiosInstance = createSmAxios({
  axiosRequestConfig: { baseURL: config('API_URL') }, // example config
  interceptors: {
    bearerTokenOptions: { getBearerToken: () => jwtCache.getToken() },
    retryOptions: { retries: 3, retryDelayMs: 500, maxRetryTimeMs: 3000 },
    errorOnProxy404Options: { isProxy404: axiosError => typeof axiosError?.response?.data?.type === 'undefined' },
    smAxiosErrorOptions: { logger },
    smAxiosMetricsOptions: { metrics },
  },
})

export const client = createClient(axiosInstance)
```

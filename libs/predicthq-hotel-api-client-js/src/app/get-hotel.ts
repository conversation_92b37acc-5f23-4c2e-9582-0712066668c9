/* eslint-disable */
import { AxiosInstance, AxiosResponse } from 'axios'
import { Exactly } from './utils'

export type OperationWithAxiosResponse = <T extends Exactly<Request, T>>(request: T) => Promise<AxiosResponse<Response>>
export const operationWithAxiosResponse = <T extends Exactly<Request, T>>(axios: AxiosInstance, request: T): Promise<AxiosResponse<Response>> => {
  return axios.request({
    url: `/api/hotels/${encodeURIComponent(request.path['spid'] as any)}`,
    // @ts-ignore for sm-axios
    urlTemplate: '/api/hotels/{spid}',
    method: 'GET',
    headers: request.headers,
  })
}

/**
 * Requires hotel:read permission
 */
export type Operation = <T extends Exactly<Request, T>>(request: T) => Promise<Response>
export const operation = async <T extends Exactly<Request, T>>(axios: AxiosInstance, request: T): Promise<Response> => {
  return (await operationWithAxiosResponse(axios, request)).data
}

export interface Request {
  headers: {
    'X-SM-TRACE-TOKEN': string
  }
  path: {
    spid: string
  }
}

export interface Response {
  spid: string
  latitude: number
  longitude: number
  countryCode: string
  /**
   * Radius in kilometres
   */
  suggestedRadius: number
  featureImportance: {
    category:
      | 'observances'
      | 'public-holidays'
      | 'concerts'
      | 'sports'
      | 'community'
      | 'expos'
      | 'performing-arts'
      | 'conferences'
      | 'school-holidays'
      | 'festivals'
      | 'severe-weather'
      | 'academic'
    pvalue: number
    important: boolean
    [k: string]: any
  }[]
  featureImportanceUpdatedAt: string
  createdAt: string
  updatedAt: string
  [k: string]: any
}


type Primitive =
  | null
  | undefined
  | string
  | number
  | boolean
  | symbol
  | bigint

/** copied from https://stackoverflow.com/questions/49401866/all-possible-keys-of-an-union-type */
type KeysOfUnion<T> = T extends T ? keyof T : never

/** apply excess property check, see https://github.com/siteminder-au/arch-packages/pull/348 */
export type Exactly<T, U extends T> = T extends Primitive
  ? T
  : { [P in keyof T]: Exactly<T[P], U[P]> } & Record<Exclude<keyof U, KeysOfUnion<T>>, never>

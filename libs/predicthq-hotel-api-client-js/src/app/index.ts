/* eslint-disable */
import { AxiosInstance } from 'axios'

import * as GetHotels from './get-hotels'
import * as GetHotel from './get-hotel'


type GetHotelsRequest = GetHotels.Request
type GetHotelsResponse = GetHotels.Response
type GetHotelRequest = GetHotel.Request
type GetHotelResponse = GetHotel.Response

const operations = {
  getHotels: GetHotels.operation,
  getHotel: GetHotel.operation,
}

const operationsWithAxiosResponse = {
  getHotels: GetHotels.operationWithAxiosResponse,
  getHotel: GetHotel.operationWithAxiosResponse,
}

export interface ApiClient {
  getHotels: GetHotels.Operation,
  getHotel: GetHotel.Operation,
}

export interface ApiClientWithAxiosResponse {
  getHotels: GetHotels.OperationWithAxiosResponse,
  getHotel: GetHotel.OperationWithAxiosResponse,
}

export const createClient = (axios: AxiosInstance): ApiClient => {

  const apiClient = {}
  for (let [name, operation] of Object.entries(operations)) {
    // @ts-ignore
    apiClient[name] = (...args: any[]) => operation(axios, ...args)
  }

  return apiClient as ApiClient

}

export const createClientWithAxiosResponse = (axios: AxiosInstance): ApiClientWithAxiosResponse => {

  const apiClient = {}
  for (let [name, operation] of Object.entries(operationsWithAxiosResponse)) {
    // @ts-ignore
    apiClient[name] = (...args: any[]) => operation(axios, ...args)
  }

  return apiClient as ApiClientWithAxiosResponse

}

export {
  GetHotelsRequest, GetHotelsResponse,
  GetHotelRequest, GetHotelResponse,
}

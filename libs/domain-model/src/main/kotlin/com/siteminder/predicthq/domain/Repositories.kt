package com.siteminder.predicthq.domain

import com.siteminder.jpa.BaseJpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.util.*

@Repository
interface HotelRepository : BaseJpaRepository<Hotel, Long> {

    fun findBySpid(spid: String): Optional<Hotel>

    @Query(nativeQuery = true, value = "SELECT h.* FROM Hotel h WHERE ST_Distance_Sphere(h.location, ST_SRID(POINT(:longitude, :latitude),4326)) <= ((h.suggested_radius * :radiusMultiplier) * 1000)")
    fun findAllByLocationWithinRadius(latitude: BigDecimal, longitude: BigDecimal, radiusMultiplier: BigDecimal): List<Hotel>

    @Query("FROM Hotel h WHERE h.featureImportanceUpdatedAt IS NULL OR DATEDIFF(NOW(), h.featureImportanceUpdatedAt) > 90")
    fun findAllByFeatureImportanceToUpdate(): List<Hotel>
}


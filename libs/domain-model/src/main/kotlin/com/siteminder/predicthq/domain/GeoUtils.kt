package com.siteminder.predicthq.domain

import org.locationtech.jts.geom.Coordinate
import org.locationtech.jts.geom.GeometryFactory
import org.locationtech.jts.geom.Point
import org.locationtech.jts.geom.PrecisionModel

object GeoUtils {

    private val geometryFactory = GeometryFactory(PrecisionModel(), 4326)

    fun newGeoPoint(latitude: Double, longitude: Double): Point {
        return geometryFactory.createPoint(Coordinate(longitude, latitude))
    }

    fun getLatitude(point: Point) = point.y.toBigDecimal()

    fun getLongitude(point: Point) = point.x.toBigDecimal()
}

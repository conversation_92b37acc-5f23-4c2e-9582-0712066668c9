package com.siteminder.predicthq.domain

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.MappedSuperclass
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import org.locationtech.jts.geom.Point
import java.math.BigDecimal
import java.time.ZonedDateTime

@Entity
class Hotel(

    @field:Id
    @field:GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @field:NotBlank
    @field:Column(unique = true)
    var spid: String,

    @field:NotNull
    @field:Column(columnDefinition = "geometry(Point,4326)")
    var location: Point,

    @field:NotNull
    var countryCode: String,

    @field:NotNull
    var suggestedRadius: BigDecimal,

    var featureImportance: String? = null,

    var featureImportanceUpdatedAt: ZonedDateTime? = null,

    var featureImportanceLastAttemptAt: ZonedDateTime? = null,

    @field:NotNull
    var createdAt: ZonedDateTime,

    @field:NotNull
    var updatedAt: ZonedDateTime

) : BaseEntity() {
    override fun equals(other: Any?): Boolean {

        if (this === other) return true

        if (other !is Hotel) return false

        return spid == other.spid
    }

    override fun hashCode() = spid.hashCode()
}

/**
 * For compatibility with BaseJpaRepository
 */
@MappedSuperclass
abstract class BaseEntity

package com.siteminder.predicthq.domain

import com.siteminder.BaseSpringBootTest
import io.kotlintest.shouldBe
import io.kotlintest.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.ZonedDateTime

class HotelRepositoryTests : BaseSpringBootTest() {

    @Autowired
    private lateinit var repository: HotelRepository

    @Test
    fun `should generate id on save`() {
        val saved = repository.save(
            Hotel(
                spid = "spid-123",
                location = GeoUtils.newGeoPoint(1.0, 1.0),
                countryCode = "AU",
                suggestedRadius = 3.5.toBigDecimal(),
                createdAt = ZonedDateTime.now(),
                updatedAt = ZonedDateTime.now()
            )
        )

        saved.id shouldNotBe null
    }

    @Test
    fun `should get hotel by spid`() {
        val saved = repository.save(
            Hotel(
                spid = "spid-123",
                location = GeoUtils.newGeoPoint(1.0, 1.0),
                countryCode = "AU",
                suggestedRadius = 3.5.toBigDecimal(),
                createdAt = ZonedDateTime.now(),
                updatedAt = ZonedDateTime.now()
            )
        )

        val found = repository.findBySpid(saved.spid).orElse(null)

        found shouldNotBe null
    }

    @Test
    fun `should find all hotels by location within radius by spid`() {
        repository.save(
            Hotel(
                spid = "spid-123",
                location = GeoUtils.newGeoPoint(-33.8688, 151.2093),
                countryCode = "AU",
                suggestedRadius = 9.toBigDecimal(),
                createdAt = ZonedDateTime.now(),
                updatedAt = ZonedDateTime.now()
            )
        )

        repository.save(
            Hotel(
                spid = "spid-124",
                location = GeoUtils.newGeoPoint(-33.8688, 151.2093),
                countryCode = "AU",
                suggestedRadius = 8.toBigDecimal(),
                createdAt = ZonedDateTime.now(),
                updatedAt = ZonedDateTime.now()
            )
        )

        val hotels = repository.findAllByLocationWithinRadius((-33.9400).toBigDecimal(),(151.1754).toBigDecimal(), 1.0.toBigDecimal())

        hotels.size shouldBe 1
        hotels[0].spid shouldBe "spid-123"
    }

    @Test
    fun `should find all hotels with no feature importance or last updated more than 90 days`() {
        repository.save(
            Hotel(
                spid = "spid-123",
                location = GeoUtils.newGeoPoint(-33.8688, 151.2093),
                countryCode = "AU",
                suggestedRadius = 9.toBigDecimal(),
                createdAt = ZonedDateTime.now(),
                updatedAt = ZonedDateTime.now()
            )
        )

        repository.save(
            Hotel(
                spid = "spid-124",
                location = GeoUtils.newGeoPoint(-33.8688, 151.2093),
                countryCode = "AU",
                suggestedRadius = 8.toBigDecimal(),
                featureImportance = """
                    [
                      {
                        "category": "sports",
                        "important": false,
                        "pvalue": 0.6
                      },
                      {
                        "category": "concerts",
                        "important": true,
                        "pvalue": 0.3
                      }
                    ]
                """.trimIndent(),
                featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(91),
                createdAt = ZonedDateTime.now(),
                updatedAt = ZonedDateTime.now()
            )
        )

        repository.save(
            Hotel(
                spid = "spid-125",
                location = GeoUtils.newGeoPoint(-33.8688, 151.2093),
                countryCode = "AU",
                suggestedRadius = 8.toBigDecimal(),
                featureImportance = """
                    [
                      {
                        "category": "sports",
                        "important": false,
                        "pvalue": 0.6
                      },
                      {
                        "category": "concerts",
                        "important": true,
                        "pvalue": 0.3
                      }
                    ]
                """.trimIndent(),
                featureImportanceUpdatedAt = ZonedDateTime.now().minusDays(40),
                createdAt = ZonedDateTime.now(),
                updatedAt = ZonedDateTime.now()
            )
        )

        val hotels = repository.findAllByFeatureImportanceToUpdate()

        hotels.size shouldBe 2
        hotels[0].spid shouldBe "spid-123"
        hotels[1].spid shouldBe "spid-124"
    }

}

apply plugin: 'springboot-kotlin-configuration-plugin' // https://github.com/siteminder-au/sm-gradle-plugins/tree/master/sm-build-plugin
apply plugin: 'kotlin-jpa'

dependencies {
    api "com.siteminder:sm-spring-boot-jpa-starter:${smLibraryVersion}"
    api "org.hibernate:hibernate-spatial:6.5.2.Final"
    api "org.springframework.boot:spring-boot-starter-validation:${springBootVersion}"

    testImplementation "com.siteminder:sm-spring-boot-starter-test:${smLibraryVersion}"
}

bootJar {
    enabled = false
}

jar {
    enabled = true
}

#!/usr/bin/env bash

echo "Testing DB migration scripts"

export PROJECT_ROOT=$(git rev-parse --show-toplevel)

DOCKER_COMPOSE_PROJECT_NAME="predicthq-playpen"

docker compose -p $DOCKER_COMPOSE_PROJECT_NAME -f ${PROJECT_ROOT}/playpen/docker-compose.yaml up -d mysql

docker compose -p $DOCKER_COMPOSE_PROJECT_NAME -f ${PROJECT_ROOT}/playpen/docker-compose.yaml run --rm db-migrations

docker_exit_code=$?

docker compose -p $DOCKER_COMPOSE_PROJECT_NAME down --volumes --remove-orphans --rmi all

unset PROJECT_ROOT

exit $docker_exit_code

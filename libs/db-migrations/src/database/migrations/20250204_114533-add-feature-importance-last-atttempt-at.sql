--
-- WARNING WARNING WARNING
--
-- If this file has been pushed to GitHub then:
--
-- Do not change the content
-- Do not delete this file.
--
-- Because you have to assume someone else has already executed this file and
-- whatever change that was done in this file might be on their DB so you have
-- to work on that basis if you want to do further migrations.
--
-- DO NOT REMOVE THIS WARNING MESSAGE
ALTER TABLE `hotel`
    ADD COLUMN `FEATURE_IMPORTANCE_LAST_ATTEMPT_AT` datetime NULL

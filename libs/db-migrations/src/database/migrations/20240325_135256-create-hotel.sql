--
-- WARNING WARNING WARNING
--
-- If this file has been pushed to GitHub then:
--
-- Do not change the content
-- Do not delete this file.
--
-- Because you have to assume someone else has already executed this file and
-- whatever change that was done in this file might be on their DB so you have
-- to work on that basis if you want to do further migrations.
--
-- DO NOT REMOVE THIS WARNING MESSAGE
CREATE TABLE `hotel` (
 `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
 `SPID` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
 `LOCATION` POINT NOT NULL SRID 4326,
 `SUGGESTED_RADIUS` decimal(6,3) NOT NULL,
 `FEATURE_IMPORTANCE` JSON DEFAULT NULL,
 `FEATURE_IMPORTANCE_UPDATED_AT` datetime NULL,
 `CREATED_AT` datetime NOT NULL,
 `UPDATED_AT` datetime NOT NULL,
 <PERSON><PERSON><PERSON><PERSON>E<PERSON> (`ID`),
 <PERSON><PERSON><PERSON>UE KEY `UNIQUE_SPID` (`SPID`),
 <PERSON><PERSON><PERSON><PERSON> KEY `LOCATION` (`LOCATION`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
